<script setup>
import ImgOther from "@/assets/<EMAIL>";
import ImgPhoto from "@/assets/<EMAIL>";
import ImgPDF from "@/assets/PDF.png";
import ImgVideo from "@/assets/<EMAIL>";
import ImgWord from "@/assets/word.png";
import ImgExcel from "@/assets/wordx.png";
import PreviewDialog from "@/components/PreviewV2/PreviewDialog.vue";
import PreviewFileDialog from "@/components/PreviewV2/PreviewFileDialog.vue";
import PreviewFileDialogV2 from "@/components/PreviewV2/PreviewFileDialogV2.vue";
import { downloadFileBySaveAs, getFileType } from "@/utils";

import { getFileFullUrl } from "@/utils/fileTools";
import { uploadFile as uploadHandler } from "@/utils/upload/upload";
import { useFileDialog } from "@vueuse/core";
import { ElLoading, ElMessage, ElMessageBox } from "element-plus";
import { computed, ref } from "vue";
const props = defineProps({
  data: {
    type: Object,
    default: () => {}
  },
  tip: {
    type: String,
    default: "请上传文件"
  },
  isUpload: {
    type: Boolean,
    default: false
  },
  isNeedDelte: {
    type: Boolean,
    default: false
  },
  index: {
    type: Number,
    default: -1
  },
  // 是否是多文件上传
  isMultiple: {
    type: Boolean,
    default: false
  },
  isHiddenPreview: {
    type: Boolean,
    default: false
  },
  type: {
    type: Boolean,
    default: true
  },
  isTypeVideo: {
    type: Boolean,
    default: false
  }
});
const emit = defineEmits(["uploadSuccess", "update:data", "delete"]);
const previewType = ["pdf", "doc", "docx"];
const { files, open, reset, onChange } = useFileDialog({
  accept: "*"
});

const isHaveData = computed(() => {
  const tData = props.data || {};
  return Object.keys(tData).length > 0;
});

onChange(async fileList => {
  console.log("🦄-----fileList-----", fileList);
  // loading
  if (props.fileFitter) {
    fileList = props.fileFitter(fileList);
  }
  if (!fileList || !fileList.length) {
    reset();
    return;
  }

  const loadingInstance = ElLoading.service({
    lock: true,
    text: "文件上传中...",
    background: "rgba(0, 0, 0, 0.7)"
  });
  const fileDatas = [];
  for (let i = 0; i < fileList.length; i++) {
    const item = fileList[i];

    const lastIndex = item.name.lastIndexOf(".");
    const urlnew = item.name.substring(lastIndex);
    console.log("🌵-----urlnew-----", urlnew);
    const fileTypeMapping = {
      ".pdf": "f",
      ".doc": "w",
      ".docx": "w",
      ".wps": "w",
      ".pptx": "p",
      ".ppt": "p",
      ".xls": "s",
      ".xlsx": "s",
      ".dbt": "d"
      // 可以继续添加其他文件类型
    };
    const fileType = {
      ".zip": "z",
      ".rar": "r",
      ".tar": "t",
      ".gz": "g",
      ".7z": "z",
      ".bz2": "b",
      ".xz": "x",
      ".zipx": "z"
      // 可以继续添加其他文件类型
    };
    if (fileType[urlnew]) {
      loadingInstance.close();
      ElMessage.warning("请上传正确的文件格式");
      return;
    }
    if (fileTypeMapping[urlnew]) {
      const fileSize = item.size / (1024 * 1024); // 将文件大小转换为MB
      if (fileSize > 30) {
        ElMessage.warning("文件大小超过30MB，请重新选择");
        loadingInstance.close();
        return;
      }
    }

    const isVideo = item.type.startsWith("video");
    await uploadHandler(
      item,
      { video: isVideo },
      speed => {
        const { total: { percent = 0 } = {} } = speed || {};
        // console.log("---onspeed---", speed, percent);
        // 根据length以及percent计算进度
        const progress = (i + percent / 100) / fileList.length;
        const pT = Math.floor(progress * 100);
        // console.log("pT", pT);
        loadingInstance.setText(`文件上传中...${pT}%`);
      },
      props.fileSize
    )
      .then(function (data) {
        // console.log("----上传成功----", data);
        fileDatas.push(data);
      })
      .catch(function (err) {
        console.log("----上传失败----", err);
      })
      .finally(() => {
        // console.log("上传完成");
      });

    if (i === fileList.length - 1) {
      // console.log("文件全部上传完成", fileDatas);
      reset();
      if (props.isMultiple) {
        emit("uploadSuccess", fileDatas);
      } else {
        emit("uploadSuccess", fileDatas[0]);
        emit("update:data", fileDatas[0]);
      }
      loadingInstance.close();
    }
  }
});
const showImageViewer = ref(false);

const filesType = computed(() => {
  const { url = "" } = props?.data;
  const fileType = getFileType(url);
  return fileType;
});
const showViewer = ref(false);
const isShowDialog = ref(false);
const isShowDialogV2 = ref(false);
const previewisShow = () => {
  const { url } = props.data;
  const fileType = getFileType(url);
  if (fileType == "video" || fileType == "radio") {
    isShowDialog.value = true;
    return;
  }
  if (!isCanPreview(url)) {
    props.isTypeVideo === true
      ? ElMessage.warning(
          "该文件格式不支持在线预览（仅支持word,pdf,音频,图片在线预览）"
        )
      : ElMessage.warning(
          "该文件格式不支持在线预览（仅支持word,pdf,音频,视频,图片在线预览）"
        );
    return;
  }
  if (fileType == "image") {
    showImageViewer.value = true;
    return;
  }
  isShowDialogV2.value = true;
};
const handleDownload = async () => {
  // console.log("🌈----- handleDownload----- "); //  handleDownload
  const { url, name } = props.data || {};
  if (!url) {
    ElMessage.warning("暂无文件下载");
    return;
  }
  const downloadUrl = await getFileFullUrl(url);
  downloadFileBySaveAs(downloadUrl, name);
};
// 文件预览判断
const isCanPreview = url => {
  const types = [
    "doc",
    "docx",
    "pdf",
    "png",
    "jpg",
    "jpeg",
    "bmp",
    "gif",
    "webp",
    "mp4",
    "m2v",
    "mkv",
    "rmvb",
    "wmv",
    "avi",
    "flv",
    "mov",
    "m4v",
    "mp3",
    "wav",
    "wmv",
    "ppt",
    "pptx",
    "xls",
    "xlsx"
  ];
  const flieArr = url.split(".");
  const suffix = flieArr[flieArr.length - 1];
  if (!suffix) return false;
  return types.includes(suffix.toLowerCase());
};

const imgSrc = computed(() => {
  const { url = "" } = props.data || {};
  const fileType = getFileType(url);
  switch (fileType) {
    case "image":
      return ImgPhoto;
    case "video":
      return ImgVideo;
    case "radio":
      return ImgVideo;
    case "doc":
      return ImgWord;
    case "docx":
      return ImgWord;
    case "xls":
      return ImgExcel;
    case "xlsx":
      return ImgExcel;
    case "pdf":
      return ImgPDF;
    default:
      return ImgOther;
  }
});
const handleRemove = () => {
  // console.log("🌈----- handleRemove----- "); //  handleRemove
  ElMessageBox.confirm("是否删除该文件", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    customClass: "message-box",
    autofocus: false,
    closeOnClickModal: false
  })
    .then(() => {
      console.log(props.data);
      emit("update:data", {});
      emit("delete", props.data, props.index);
    })
    .catch(() => {
      console.log("取消");
    });
};
</script>

<template>
  <div class="fileItem-box">
    <!-- 有文件 -->
    <template v-if="isHaveData || !isUpload">
      <div :class="type ? 'download' : 'download-1'">
        <div class="report-input">
          <img :src="imgSrc" class="logo" alt="logo">
          <div
            class="name-text"
            style="cursor: pointer"
            :title="data?.fileName || data?.name || '未知文件'"
            @click="previewisShow"
          >
            {{ data?.fileName || data?.name || "未知文件" }}
          </div>
        </div>
        <div
          v-if="isUpload || isNeedDelte"
          class="close-btn"
          @click="handleRemove"
        >
          x
        </div>
        <el-button class="report-btn" @click="handleDownload">
          <img
            style="object-fit: contain"
            src="@/assets/<EMAIL>"
            class="download-logo"
            alt="logo"
          >
          下载
        </el-button>
      </div>

      <!-- <el-button v-if="!isHiddenPreview" class="preview" @click="previewisShow">
        在线预览
      </el-button> -->
    </template>
    <!-- 无文件 -->
    <template v-else>
      <div :class="type ? 'download' : 'download-1'">
        <div class="report-input">
          <div class="text-tip">{{ tip }}</div>
        </div>
      </div>

      <el-button
        style="margin-left: 20px"
        type="primary"
        size="large"
        @click="open"
      >
        选择文件
      </el-button>
    </template>
    <PreviewDialog
      v-if="isShowDialog"
      v-model:isShowDialog="isShowDialog"
      :fileUrl="data?.url"
      :type="filesType"
      :title="data?.name"
    />
    <PreviewFileDialog
      v-if="showViewer"
      v-model:isShow="showViewer"
      :fileUrl="data?.url"
      :type="filesType"
      :name="data?.name"
    />
    <PreviewFileDialogV2
      v-if="isShowDialogV2"
      v-model:isShow="isShowDialogV2"
      :data="data"
    />
    <ElImageViewer
      v-if="showImageViewer"
      :url-list="[data?.url]"
      @close="() => (showImageViewer = false)"
    />
  </div>
</template>

<style lang="scss" scoped>
.fileItem-box {
  display: flex;
  // min-width: 400px !important; // 强制覆盖 min-width 样式
  align-items: center;
  width: 30%;
  margin-top: 5px;
  margin-bottom: 5px;
  // background: red;
  .download {
    // width: 953px;
    width: 100%;
    height: 40px;
    position: relative;
    display: flex;
    align-items: center;

    .logo {
      width: 24px;
      height: 23px;
      margin-left: 10px;
      margin-right: 10px;
    }

    .report-input {
      position: absolute;
      width: 100%;
      display: flex;
      align-items: center;
      height: 38px;
      border: solid 1px #cccccc;
      border-radius: 4px;

      .text-tip {
        margin-left: 20px;
        font-family: Source Han Sans CN;
        font-size: 14px;
        letter-spacing: 1px;
        color: #999999;
      }

      .name-text {
        width: calc(100% - 140px);
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        -o-text-overflow: ellipsis;
      }
    }

    .report-btn {
      width: 90px;
      height: 36px;
      background-color: #448efb;
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-family: Source Han Sans CN;
      font-size: 16px;
      letter-spacing: 1px;
      color: #ffffff;
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
    }

    .close-btn {
      position: absolute;
      right: 100px;
      color: #999999;
      cursor: pointer;
    }

    .download-logo {
      width: 14px;
      height: 14px;
      margin-right: 10px;
    }
  }

  // 在线预览
  .preview {
    width: 90px;
    height: 36px;
    border-radius: 4px;
    border: solid 1px #b1b0b0;
    color: #b1b0b0;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 12px;
  }

  .preview:hover {
    background-color: #ffffff;
  }
}
</style>
