import { http } from "@/utils/http";
//评论查询
export const findByComments = params => {
  return http.request(
    "get",
    "/platform/comments/findByComments",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 批量单个评论审核
export const batchSetAuditState = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/comments/batchSetAuditState",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COMMENTS",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
//评论回复查询
export const findByReplies = params => {
  return http.request(
    "get",
    "/platform/replies/findByReplies",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 批量单个评论回复审核
export const batchSetAuditRepliesState = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/replies/batchSetAuditState",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COMMENTS",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
