<script setup>
import { ref, reactive, onMounted } from "vue";
import { formatTime } from "@/utils/index";
import dayjs from "dayjs";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox, genFileId, ElUpload } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import {
  teacherDatabaseFindAll,
  teacherDatabaseDelete,
  teacherDatabaseImportExcel
} from "@/api/expertLibrary.js";
import { uploadFile } from "@/utils/upload/upload";
import { useIntervalFn } from "@vueuse/core";
import { getAsyncTask } from "@/utils/common";
import { saveAs } from "file-saver";
import domain from "@/utils/http/base";
import { downloadFile } from "@iceywu/utils";
defineOptions({
  name: "ExpertDatabasename"
});

const { staffTemplate } = domain;

const router = useRouter();
const route = useRoute();

const loadingTable = ref(false);
const exportLoading = ref(false); // 批量导入 弹框确认按钮加载状态
const defaultTime = [
  new Date(2024, 4, 1, 0, 0, 0),
  new Date(2024, 4, 1, 23, 59, 59)
];
const dataList = ref([]);
const from = reactive({
  name: "",
  time: [],
  file: {}
});
const pagination = {
  total: 0,
  pageSize: 15,
  currentPage: 1,
  background: true
};

const columns = [
  {
    label: "专家名",
    prop: "name",
    minWidth: 90,
    formatter: ({ name }) => {
      return name || "--";
    }
  },
  {
    label: "性别",
    prop: "gender",
    minWidth: 90,
    formatter: ({ gender }) => {
      return gender || "--";
    }
  },
  {
    label: "创建时间",
    minWidth: 90,
    prop: "createdAt",
    formatter: ({ createdAt }) => {
      return createdAt ? dayjs(createdAt).format("YYYY-MM-DD HH:mm:ss") : "--";
    }
  },
  {
    label: "工作单位",
    prop: "workUnit",
    minWidth: 90,
    formatter: ({ workUnit }) => {
      return workUnit || "--";
    }
  },
  {
    label: "擅长领域",
    prop: "goodAt",
    minWidth: 90,
    formatter: ({ goodAt }) => {
      return goodAt || "--";
    }
  },
  {
    label: "平台注册年限",
    prop: "registerYear",
    minWidth: 90,
    formatter: ({ registerYear }) => {
      return registerYear || "--";
    }
  },
  {
    label: "从教开始时间",
    prop: "startTeachYear",
    minWidth: 90,
    formatter: ({ startTeachYear }) => {
      return startTeachYear
        ? dayjs(startTeachYear).format("YYYY-MM-DD HH:mm:ss")
        : "--";
    }
  },
  {
    label: "操作",
    fixed: "right",
    width: 120,
    slot: "operation"
  }
];

const removeEmptyValues = obj => {
  return Object.fromEntries(
    Object.entries(obj).filter(
      ([key, value]) =>
        value !== "" && !(Array.isArray(value) && value.length === 0)
    )
  );
};
//模版下载
const Template_Download = async (url, fileName, type) => {
  if (type == "oss") {
    window.location.href = url;
  } else {
    fetch(url)
      .then(res => res.blob())
      .then(blob => {
        saveAs(blob, fileName);
      })
      .catch(error => console.error(error));
  }
};

// 列表 api
const onSearch = async val => {
  const paramsArg = {
    page: val.page,
    size: val.size,
    sort: "createdAt,desc",
    name: val.name || "",
    startTime: val.startTime || "",
    endTime: val.endTime || ""
  };
  let aee = removeEmptyValues(paramsArg);
  const [err, res] = await requestTo(teacherDatabaseFindAll(aee));
  if (res) {
    dataList.value = res.content;
    pagination.total = res.totalElements;
  }
  if (err) {
  }
};

// 搜索
const searchAdd = () => {
  let time1 = from.time !== null ? new Date(from.time[0]).getTime() : "";
  let time2 = from.time !== null ? new Date(from.time[1]).getTime() : "";
  const paramsArg = {
    name: from.name || "",
    startTime: time1 || "",
    endTime: time2 || "",
    page: pagination.currentPage - 1 || "",
    size: pagination.pageSize || ""
  };
  onSearch(paramsArg);
};

// 重置
const setData = () => {
  from.name = "";
  from.phone = "";
  from.time = [];
};

// 每页多少条
async function handleSizeChange(val) {
  pagination.pageSize = val;
  let time1 = from.time !== null ? new Date(from.time[0]).getTime() : "";
  let time2 = from.time !== null ? new Date(from.time[1]).getTime() : "";
  const paramsArg = {
    name: from.name || "",
    startTime: time1 || "",
    endTime: time2 || "",
    page: 0,
    size: val
  };
  onSearch(paramsArg);
}
// 前往页数
async function handleCurrentChange(val) {
  pagination.currentPage = val;
  let time1 = from.time !== null ? new Date(from.time[0]).getTime() : "";
  let time2 = from.time !== null ? new Date(from.time[1]).getTime() : "";
  const paramsArg = {
    name: from.name || "",
    startTime: time1 || "",
    endTime: time2 || "",
    page: val - 1,
    size: pagination.pageSize
  };
  onSearch(paramsArg);
}

// 编辑/创建 跳转
const openJumpTo = (title, val) => {
  if (title === "edit") {
    router.push({
      path: "/platform/components/expertCreateEdit",
      query: { title: "edit", id: val.id }
    });
  } else {
    router.push({
      path: "/platform/components/expertCreateEdit",
      query: { title: "create" }
    });
  }
};

// 删除
const isDeleteApi = (text, val) => {
  let freezeText = `确定要删除该专家的数据吗？`;
  ElMessageBox.confirm(`${freezeText}`, `确定删除`, {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(async () => {
      const paramsArg = {
        id: val.id
      };
      const operateLog = {
        operateLogType: "PARENT_MANAGEMENT",
        operateType: "删除了" + val.name
        // operatorTarget: form.value.name,
      };
      const { code } = await teacherDatabaseDelete(paramsArg, operateLog);
      if (code === 200) {
        const paramsArgapi = {
          page: 0,
          size: 10
        };
        onSearch(paramsArgapi);
        ElMessage({
          type: "success",
          message: "删除成功"
        });
      } else {
        ElMessage({
          type: "error",
          message: "删除失败"
        });
      }
    })
    .catch(() => {});
};

// 批量导出 按钮
const batchImportDom = ref(false);
const batchImport = () => {
  batchImportDom.value = true;
};

// 文件上传
const fileList = ref([]);
const beforeAvatarUpload = async rawFile => {
  if (!rawFile) return;
  from.file = rawFile;
};
const upload = ref();
// 文件上传后替换上一个文件
const handleExceed = files => {
  upload.value.clearFiles();
  const file = files[0];
  from.file = file;
  file.uid = genFileId();
  upload.value.handleStart(file);
};
// 显示文件大小
const getFileSize = size => {
  const units = ["B", "KB", "MB", "GB", "TB"];
  let index = 0;
  while (size >= 1024 && index < units.length - 1) {
    size /= 1024;
    index++;
  }
  return `${size.toFixed(2)} ${units[index]}`;
};
// 批量导出弹框 取消
const handleClose = () => {
  batchImportDom.value = false;
  upload.value.clearFiles();
  from.file = {};
};
// 批量导出弹框 确认
const sureImport = async () => {
  exportLoading.value = true;
  let formdata = new FormData();
  formdata.append("file", from.file);
  let { code, data } = await teacherDatabaseImportExcel(formdata);
  if (code === 200 && data) {
    const { id, complete, success } = data;
    // console.log('🎉-----id, complete, success-----', id, complete, success);
    const task = await getAsyncTask(id);
    if (task.data.complete && task.data.success) {
      const paramsArgapi = {
        page: 0,
        size: 10
      };
      onSearch(paramsArgapi);
      ElMessage({
        type: "success",
        message: "导入成功"
      });
      handleClose();
      exportLoading.value = false;
    } else {
      ElMessage.error(task.data.errMsg);
      exportLoading.value = false;
    }
  } else {
    ElMessage.error("文件不能为空");
    exportLoading.value = false;
  }
};

// 师资库下载模板文件
const TemplateDownload = () => {
  console.log("🌈-----staffTemplate-----", staffTemplate);
  // Template_Download(
  //   staffTemplate + "/gostaredu-cdn/template/师资库导入模板.xlsx",
  //   "专家库导入模板文件.xlsx"
  // );
  downloadFile(
    staffTemplate + "/gostaredu-cdn/template/师资库导入模板.xlsx",
    "专家库导入模板文件.xlsx"
  );
  // https://s.gostaredu.boran-tech.com/gostaredu-cdn/template/师资库导入模板.xlsx
};

onMounted(async () => {
  const paramsArg = {
    page: 0,
    size: 10
  };
  onSearch(paramsArg);
});
</script>

<template>
  <div>
    <div class="common">
      <!-- <div class="con_top">
        <div class="titles">专家库</div>
        <el-button type="primary">创建专家</el-button>
      </div> -->

      <div class="search">
        <el-form :inline="true" :model="from" class="demo-form-inline">
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="from.time"
              type="daterange"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              :default-time="defaultTime"
            />
          </el-form-item>
          <el-form-item label="专家名">
            <el-input
              v-model.trim="from.name"
              placeholder="请输入专家名"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <div class="button">
              <el-button type="primary" @click="searchAdd(from)">
                搜索
              </el-button>
              <el-button @click="setData(from)">重置</el-button>
              <!-- <el-button @click="openJumpTo()" type="primary"
                >创建专家</el-button
              > -->
              <el-button type="primary" @click="batchImport()">
                批量导入
              </el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <div class="common">
      <div class="puretable">
        <pure-table
          ref="tableRef"
          row-key="id"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="left"
          table-layout="auto"
          :loading="loadingTable"
          :data="dataList"
          :columns="columns"
          :pagination="{ ...pagination }"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #operation="{ row }">
            <!-- <el-button
              v-code="['612']"
              class="reset-margin"
              link
              type="primary"
              @click="openJumpTo('edit', row)"
            >
              编辑
            </el-button> -->
            <el-button link type="danger" @click="isDeleteApi('delete', row)">
              删除
            </el-button>
          </template>
        </pure-table>
      </div>
    </div>

    <el-dialog
      v-model="batchImportDom"
      style="min-width: 420px"
      width="30%"
      :title="`批量导入`"
      :before-close="handleClose"
    >
      <el-upload
        ref="upload"
        v-model:file-list="fileList"
        drag
        class="upload-demo"
        accept=".xlsx"
        :limit="1"
        :show-file-list="false"
        :http-request="() => {}"
        :on-exceed="handleExceed"
        :before-upload="beforeAvatarUpload"
      >
        <div v-if="!fileList.length" class="el-upload__text">
          将文件拖放到此处或 <em>单击上传</em>
        </div>
        <template v-else>
          <div class="custom-preview">
            <!-- 自定义文件预览区域 -->
            <div v-for="file in fileList" :key="file.uid" class="preview-item">
              <!-- 显示文件名  -->
              <div>{{ file.name }}</div>
              <!-- 显示文件大小 -->
              <div>{{ getFileSize(file.size) }}</div>
              <!-- 显示文件类型  -->
              <div>{{ file.type }}</div>
            </div>
          </div>
        </template>
      </el-upload>
      <el-button link type="primary" @click="TemplateDownload()">
        下载专家库模板文件
      </el-button>
      <div class="buttom">
        <el-button @click="handleClose()">取消</el-button>
        <el-button
          type="primary"
          :loading="exportLoading"
          @click="sureImport()"
        >
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.common {
  padding: 20px 20px 2px 20px;
  background-color: #fff;
  margin-bottom: 20px;

  .con_top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: fit-content;
    margin-bottom: 24px;

    .titles {
      font-size: 20px;
      font-weight: bold;
      color: #606266;
    }
  }

  .search {
    display: flex;

    // justify-content: space-between;
    .button {
      display: flex;
      justify-content: right;
    }
  }
}

.buttom {
  display: flex;
  justify-content: end;
}

.upload-demo {
  margin-bottom: 10px;
}
</style>
