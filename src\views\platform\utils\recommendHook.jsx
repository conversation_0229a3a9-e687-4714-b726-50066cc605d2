import { onMounted, reactive, ref, defineComponent } from "vue";
import { Edit, CirclePlus, Check } from "@element-plus/icons-vue";
import {
  premiumCourseFindAll,
  premiumCourseUpdate,
  premiumCourseSave,
  premiumCourseMove,
  premiumCourseseDelete,
  organizationFindAll,
  courseFindAll,
  popularCourseFindAll,
  popularCourseUpdate,
  popularCourseSave,
  popularCourseMove,
  popularCourseDelete,
  organizationFindByNoPage
} from "@/api/apiPlatform.js";
import { requestTo } from "@/utils/http/tool";
import ImgBlurHash from "@/components/ImgBlurHash";
import { ElMessage, ElMessageBox } from "element-plus";
import { decrypt, encryption } from "@/utils/SM4.js";
import { uploadFile } from "@/utils/upload/upload.js";
import dayjs from "dayjs";
import { ImageThumbnail } from "@/utils/imageProxy";

// import { uploadFile } from "@/utils/upload/upload.js";

export function useRole() {
  const ruleFormRef = ref();
  const optionsData = ref([]);
  const addressLinkDom = ref(false);
  const buttonLoading = ref(false); // 按钮加载状态
  const form = reactive({
    title: "",
    id: "",
    url: "",
    organizationName: "",
    textarea2: "",
    fileIdentifier: "",
    courseId: "",
    organizationId: ""
  });
  const gridData = ref([]);
  const params = {
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true
  };
  const loadingTable = ref(false);

  const removeEmptyValues = obj => {
    return Object.fromEntries(
      Object.entries(obj).filter(
        ([key, value]) =>
          value !== "" && !(Array.isArray(value) && value.length === 0)
      )
    );
  };

  const paginationOne = {
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true
  };
  const paginationTow = {
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true
  };
  const heatOne = ref();
  const heatTow = ref();
  const refinedOne = ref();
  const refinedTow = ref();

  // 开始
  const dataListOne = ref([]);
  const dataListTow = ref([]);
  async function onSearchA(val) {
    const paramsArg = {
      page: val.page || 0,
      size: val.size || 10,
      sort: "sortOrder,asc"
    };
    let aee = removeEmptyValues(paramsArg);
    const [err, result] = await requestTo(popularCourseFindAll(aee));
    // console.log("🦄-----err, result-热门课程列表---", err, result);
    if (result) {
      dataListOne.value = result?.content;
      paginationOne.total = result?.totalElements;

      if (result.number === 0) {
        heatOne.value = result?.content[0]?.id;
      }
      if (result.number + 1 === result.totalPages) {
        heatTow.value = result?.content[result.content.length - 1]?.id;
      }

      // heatOne.value = result?.content[0]?.id;
      // heatTow.value = result?.content[result?.content?.length - 1]?.id;
    } else {
      console.log("没有数据");
    }
  }
  async function onSearchB(val) {
    const paramsArg = {
      page: val.page || 0,
      size: val.size || 10,
      sort: "sortOrder,asc"
    };
    let aee = removeEmptyValues(paramsArg);
    const [err, result] = await requestTo(premiumCourseFindAll(aee));
    // console.log("🦄-----err, result--精品课程列表---", err, result);
    if (result) {
      dataListTow.value = result?.content;
      paginationTow.total = result?.totalElements;

      if (result.number === 0) {
        refinedOne.value = result?.content[0]?.id;
      }
      if (result.number + 1 === result.totalPages) {
        refinedTow.value = result?.content[result.content.length - 1]?.id;
      }
      // refinedOne.value = result?.content[0]?.id;
      // refinedTow.value = result?.content[result?.content?.length - 1]?.id;
    } else {
      console.log("没有数据");
    }
  }
  // 热门课程-新增按钮
  const popularNewlyAdd = async () => {
    if (ruleFormRef.value) ruleFormRef.value.resetFields();
    addressLinkDom.value = true;
    form.title = "新增";
    form.id = "";
    form.url = "";
    form.organizationName = "";
    form.organizationId = "";
    form.courseName = "";
    form.textarea2 = "";
    form.courseId = "";
    gridData.value = [];
  };
  // 精品课程-新增按钮
  const refinedNewlyAdd = async () => {
    if (ruleFormRef.value) ruleFormRef.value.resetFields();
    addressLinkDom.value = true;
    form.title = "新增";
    form.id = "";
    form.url = "";
    form.organizationName = "";
    form.organizationId = "";
    form.courseName = "";
    form.textarea2 = "";
    form.courseId = "";
    gridData.value = [];
  };

  // 热门课程 移动
  const handleMenuOne = async (text, val) => {
    const operateLog = {
      operateLogType: "PLATFORM_SETTINGS",
      operateType: text + "了编号为“" + val?.id + "”的热门课程"
    };
    const paramsArg = {
      id: val?.id,
      moveType: text === "上移" ? "UP" : "DOWN"
    };
    const res = await popularCourseMove(paramsArg, operateLog);
    if (res.code === 200) {
      const paramsArg = {
        page: paginationOne.currentPage - 1,
        size: paginationOne.pageSize || 10
      };
      onSearchA(paramsArg);
      ElMessage({
        type: "success",
        message: text === "上移" ? "上移成功" : "下移成功"
      });
    } else {
      ElMessage({
        type: "error",
        message: text === "上移" ? "上移失败" : "下移失败"
      });
    }
  };
  // 精品课程 移动
  const handleMenuTow = async (text, val) => {
    const operateLog = {
      operateLogType: "PLATFORM_SETTINGS",
      operateType: text + "了编号为“" + val?.id + "”的精品课程"
    };
    const paramsArg = {
      id: val?.id,
      moveType: text === "上移" ? "UP" : "DOWN"
    };
    const res = await premiumCourseMove(paramsArg, operateLog);
    if (res.code === 200) {
      const paramsArg = {
        page: paginationTow.currentPage - 1,
        size: paginationTow.pageSize || 10
      };
      onSearchB(paramsArg);
      ElMessage({
        type: "success",
        message: text === "上移" ? "上移成功" : "下移成功"
      });
    } else {
      ElMessage({
        type: "error",
        message: text === "上移" ? "上移失败" : "下移失败"
      });
    }
  };

  // 热门课程/精品课程 删除
  const isFreezeApi = async (text, val) => {
    const operateLog = {
      operateLogType: "PLATFORM_SETTINGS",
      operateType: "删除了编号为“" + val?.id + "”的" + text
      // operatorTarget: form.value.name,
    };
    const paramsArg = { id: val.id };
    console.log("🐬-----paramsArg-----", paramsArg);
    if (text === "热门课程") {
      const res = await popularCourseDelete(paramsArg, operateLog);
      if (res.code === 200) {
        const paramsArg = {
          page: paginationOne.currentPage - 1,
          size: paginationOne.pageSize || 10
        };
        onSearchA(paramsArg);
        ElMessage({
          type: "success",
          message: "删除成功"
        });
      } else {
        ElMessage({
          type: "error",
          message: "删除失败"
        });
      }
    } else if (text === "精品课程") {
      const res = await premiumCourseseDelete(paramsArg, operateLog);
      if (res.code === 200) {
        const paramsArg = {
          page: paginationTow.currentPage - 1,
          size: paginationTow.pageSize || 10
        };
        onSearchB(paramsArg);
        ElMessage({
          type: "success",
          message: "删除成功"
        });
      } else {
        ElMessage({
          type: "error",
          message: "删除失败"
        });
      }
    }
  };
  // 删除
  async function handleDelete(text, val) {
    let freezeText = "确定要删除吗？";
    ElMessageBox.confirm(`${freezeText}`, "确定删除", {
      confirmButtonText: "确定",
      cancelButtonText: "取消"
    })
      .then(() => {
        isFreezeApi(text, val);
      })
      .catch(() => {});
  }

  // 热门课程 每页多少条
  async function handleSizeChangeOne(val) {
    paginationOne.pageSize = val;
    const paramsArg = {
      page: 0,
      size: val
    };
    onSearchA(paramsArg);
  }
  // 热门课程 前往页数
  async function handleCurrentChangeOne(val) {
    paginationOne.currentPage = val;
    const paramsArg = {
      page: val - 1,
      size: paginationOne.pageSize
    };
    onSearchA(paramsArg);
  }

  // 热门课程 每页多少条
  async function handleSizeChangeTow(val) {
    paginationTow.pageSize = val;
    const paramsArg = {
      page: 0,
      size: val
    };
    onSearchA(paramsArg);
  }
  // 热门课程 前往页数
  async function handleCurrentChangeTow(val) {
    paginationTow.currentPage = val;
    const paramsArg = {
      page: val - 1,
      size: paginationTow.pageSize
    };
    onSearchA(paramsArg);
  }

  // 获取 机构名
  const obtainInstitution = async () => {
    const [err, result] = await requestTo(organizationFindByNoPage());
    if (result) {
      optionsData.value = result;
    } else {
      ElMessage.error(err);
    }
  };

  // 查询 课程名
  const institutionCourseA = async val => {
    const paramsData = {
      organizationId: val.organizationId
    };
    let api = removeEmptyValues(paramsData);
    const [err, result] = await requestTo(courseFindAll(api));
    if (result) {
      if (result?.content.length > 0) {
        gridData.value = result?.content;
      } else {
        // ElMessage({
        //   type: "error",
        //   message: "该机构暂无课程名"
        // });
        gridData.value = [];
        form.courseName = "";
      }
    } else {
      ElMessage.error(err);
    }
  };

  // 弹框 课程名
  const focusCourseId = val => {
    // if (val.organizationId === "") {
    //   ElMessage({
    //     type: "error",
    //     message: "请先选择机构"
    //   });
    // }
  };

  // 弹框 机构名发生变化
  const changeOrganization = val => {
    if (val.organizationId) {
      let paramsA = {
        organizationId: val.organizationId
      };
      institutionCourseA(paramsA);
    }
    form.courseName = "";
    form.courseId = "";
    gridData.value = [];
  };

  // 取消或关闭弹出窗口
  const beforeCloseDom = async () => {
    if (ruleFormRef.value) ruleFormRef.value.resetFields();
    form.title = "";
    addressLinkDom.value = false;
    buttonLoading.value = false;
    form.id = "";
    form.url = "";
    form.organizationName = "";
    form.textarea2 = "";
    form.courseId = "";
  };
  // 编辑 打开弹出窗口
  function editAdd(text, val) {
    console.log("🌳-----val-----", val);
    if (text === "精品课程") {
      institutionCourseA(val, "精品课程");
    } else {
      institutionCourseA(val, "热门课程");
    }
    form.title = "编辑";
    form.id = val?.id;
    form.url = val?.file?.url || "";
    form.fileIdentifier = val?.file?.fileIdentifier || "";
    form.organizationName = val?.organizationName || "";
    form.courseName = val?.courseName || "";
    form.courseId = val?.courseId || "";
    form.organizationId = val?.organizationId || "";
    form.textarea2 = val?.linkUrl || "";
    addressLinkDom.value = true;
  }
  // 图片上传
  const fileUpload = async (file, row) => {
    try {
      const { code, data } = await uploadFile(file, () => {}, ["image"]);
      console.log("🎁-----code, data-----", code, data);
      if (code === 200) {
        form.url = data.url;
        form.fileIdentifier = data?.fileIdentifier;
        if (ruleFormRef.value) {
          ruleFormRef.value.validateField("url", valid => {
            if (!valid) {
              console.log("验证失败");
            } else {
              console.log("验证成功");
            }
          });
        }
      }
    } catch (error) {
      ElMessage({
        type: "error",
        message: error.message
      });
    }
  };

  //  新增 热门课程/精品课程 api
  const newlyAddedApi = async (title, val) => {
    const operateLog = {
      operateLogType: "PLATFORM_SETTINGS",
      operateType: "新增了" + title
    };
    let isCourseId = gridData.value.filter(it => it.id == val.courseId);
    const paramsArg = {
      courseId: isCourseId[0]?.id || "",
      linkUrl: val?.textarea2 || "",
      fileIdentifier: val?.fileIdentifier || "",
      organizationId: val?.organizationId || ""
    };
    console.log(
      "🍭-----paramsArg, operateLog--新增了---",
      paramsArg,
      operateLog
    );
    let api = removeEmptyValues(paramsArg);
    if (title === "热门课程") {
      const res = await popularCourseSave(api, operateLog);
      if (res.code === 200) {
        const paramsArg = {
          page: paginationOne.currentPage - 1,
          size: paginationOne.pageSize || 10
        };
        onSearchA(paramsArg);
        ElMessage({
          type: "success",
          message: "新增热门课程成功"
        });
        addressLinkDom.value = false;
        buttonLoading.value = false;
      } else {
        ElMessage({
          type: "error",
          message: "新增热门课程失败"
        });
        buttonLoading.value = false;
      }
    } else {
      const res = await premiumCourseSave(api, operateLog);
      if (res.code === 200) {
        const paramsArg = {
          page: paginationOne.currentPage - 1,
          size: paginationOne.pageSize || 10
        };
        onSearchB(paramsArg);
        ElMessage({
          type: "success",
          message: "新增热门课程成功"
        });
        addressLinkDom.value = false;
        buttonLoading.value = false;
      } else {
        ElMessage({
          type: "error",
          message: "新增热门课程失败"
        });
        buttonLoading.value = false;
      }
    }
  };
  //  修改 热门课程/精品课程 api
  const editAddedApi = async (title, val) => {
    const operateLog = {
      operateLogType: "PLATFORM_SETTINGS",
      operateType: "修改了编号为“" + val?.id + "”的" + title
    };
    let isCourseId = gridData.value.filter(it => it.id == val.courseId);
    const paramsArg = {
      id: val?.id,
      courseId: isCourseId[0]?.id,
      linkUrl: val?.textarea2,
      fileIdentifier: val?.fileIdentifier,
      organizationId: val?.organizationId
    };
    let aee = removeEmptyValues(paramsArg);
    if (title === "热门课程") {
      const res = await popularCourseUpdate(aee, operateLog);
      if (res.code === 200) {
        const paramsArg = {
          page: paginationOne.currentPage - 1,
          size: paginationOne.pageSize || 10
        };
        onSearchA(paramsArg);
        ElMessage({
          type: "success",
          message: "编辑热门课程成功"
        });
        addressLinkDom.value = false;
        buttonLoading.value = false;
      } else {
        ElMessage({
          type: "error",
          message: "编辑热门课程失败"
        });
        buttonLoading.value = false;
      }
    } else {
      const res = await premiumCourseUpdate(aee, operateLog);
      if (res.code === 200) {
        const paramsArg = {
          page: paginationOne.currentPage - 1,
          size: paginationOne.pageSize || 10
        };
        onSearchB(paramsArg);
        ElMessage({
          type: "success",
          message: "编辑精品课程成功"
        });
        addressLinkDom.value = false;
        buttonLoading.value = false;
      } else {
        ElMessage({
          type: "error",
          message: "编辑精品课程失败"
        });
        buttonLoading.value = false;
      }
    }
  };
  // 编辑 弹框确认按钮
  const confirmAdd = async (title, formEl, val) => {
    // console.log('🍪-----title, formEl, val-----', title, formEl, val);
    if (!formEl) return;
    await formEl.validate(async (valid, fields) => {
      if (valid) {
        buttonLoading.value = true;
        console.log("🎉-----val-----", title, val);
        if (val.title === "编辑") {
          editAddedApi(title, val);
        } else if (val.title === "新增") {
          newlyAddedApi(title, val);
        }
      } else {
        console.log("error submit!", fields);
      }
    });
  };

  const rules = reactive({
    url: [{ required: true, message: "请上传图片", trigger: "blur" }],
    textarea2: [{ required: false, message: "请输地址链接", trigger: "blur" }],
    organizationId: [
      { required: false, message: "请选择机构", trigger: "change" }
    ],
    courseId: [{ required: false, message: "请选择课程", trigger: "change" }]
  });

  const columnsOne = [
    {
      label: "编号",
      prop: "id",
      minWidth: 60,
      cellRenderer: ({ row }) => (
        <div style="display: flex; justify-content: left; width: 60px;">
          {row.id || "--"}
        </div>
      )
    },
    {
      label: "图片",
      prop: "imgadd",
      width: 220,
      cellRenderer: ({ row }) => (
        // <div style="display: flex; justify-content: left; min-width: 220px;">
        //   <div style="width: 120px; height: 100px; display: flex; justify-content: center;  align-items: center;">
        //     <ImgBlurHash
        //       style="margin-right: 10px; object-fit: cover; height: 100px;"
        //       v-preview={{ url: row?.file?.url, type: "image" }}
        //       src={ImageThumbnail(row?.file?.url, '100px')}
        //     />
        //   </div>
        // </div>
        <el-image
          preview-teleported
          loading="lazy"
          src={ImageThumbnail(row?.file?.url, "200x")}
          preview-src-list={[row?.file?.url]}
          hide-on-click-modal={true}
          fit="cover"
          class="w-[100px] h-[100px]"
        />
      )
    },
    {
      label: "地址链接",
      prop: "ip",
      width: 200,
      cellRenderer: ({ row }) => (
        <div style="display: flex; justify-content: center">
          <div style="width: 200px; margin-right: 10px;">
            <div>{row.linkUrl ? row.linkUrl : <div>--</div>}</div>
          </div>
        </div>
      )
    },
    {
      label: "机构",
      prop: "organizationName",
      width: 300,
      formatter: ({ organizationName }) => {
        return organizationName || "--";
      }
    },
    {
      label: "课程",
      prop: "courseName",
      width: 300,
      formatter: ({ courseName }) => {
        return courseName || "--";
      }
    },
    {
      label: "创建时间",
      width: 180,
      prop: "createdAt",
      cellRenderer: ({ row }) => (
        <div style="display: flex; justify-content: left; width: 180px;">
          {row.createdAt
            ? dayjs(row.createdAt).format("YYYY-MM-DD HH:mm:ss")
            : "--"}
        </div>
      )
    },
    {
      label: "操作",
      fixed: "right",
      width: 120,
      slot: "operation"
    }
  ];
  const columnsTow = [
    {
      label: "编号",
      prop: "id",
      minWidth: 60,
      cellRenderer: ({ row }) => (
        <div style="display: flex; justify-content: left; width: 60px;">
          {row.id || "--"}
        </div>
      )
    },
    {
      label: "图片",
      prop: "imgadd",
      width: 220,
      cellRenderer: ({ row }) => (
        // <div style="display: flex; justify-content: left; min-width: 220px;">
        //   <div style="width: 120px; height: 100px; display: flex; justify-content: center;  align-items: center;">
        //     <ImgBlurHash
        //       style="margin-right: 10px; object-fit: cover; height: 100px;"
        //       v-preview={{ url: row?.file?.url, type: "image" }}
        //       src={ImageThumbnail(row?.file?.url, '100px')}
        //     />
        //   </div>
        // </div>
        <el-image
          preview-teleported
          loading="lazy"
          src={ImageThumbnail(row?.file?.url, "200x")}
          hide-on-click-modal={true}
          preview-src-list={[row?.file?.url]}
          fit="cover"
          class="w-[100px] h-[100px]"
        />
      )
    },
    {
      label: "地址链接",
      prop: "ip",
      width: 220,
      cellRenderer: ({ row }) => (
        <div style="display: flex; justify-content: left">
          <div style="width: 200px; margin-right: 10px;">
            <div>{row.linkUrl ? row.linkUrl : <div>--</div>}</div>
          </div>
        </div>
      )
    },
    {
      label: "机构",
      prop: "organizationName",
      width: 300,
      formatter: ({ organizationName }) => {
        return organizationName || "--";
      }
    },
    {
      label: "课程",
      prop: "courseName",
      width: 300,
      formatter: ({ courseName }) => {
        return courseName || "--";
      }
    },
    {
      label: "创建时间",
      width: 180,
      prop: "createdAt",
      cellRenderer: ({ row }) => (
        <div style="display: flex; justify-content: left; width: 180px;">
          {row.createdAt
            ? dayjs(row.createdAt).format("YYYY-MM-DD HH:mm:ss")
            : "--"}
        </div>
      )
    },
    {
      label: "操作",
      fixed: "right",
      width: 120,
      slot: "operation"
    }
  ];

  onMounted(async () => {
    // const paramsArg = {
    //   page: 0,
    //   size: 10
    // };
    // onSearchA(paramsArg);
    // onSearchB(paramsArg);
    obtainInstitution();
  });

  return {
    heatOne,
    heatTow,
    refinedOne,
    refinedTow,
    loadingTable,
    optionsData,

    columnsOne,
    columnsTow,
    dataListOne,
    dataListTow,
    popularNewlyAdd,
    refinedNewlyAdd,
    handleMenuOne,
    handleMenuTow,
    paginationOne,
    paginationTow,
    handleSizeChangeOne,
    handleCurrentChangeOne,
    handleSizeChangeTow,
    handleCurrentChangeTow,
    handleDelete,
    ruleFormRef,
    rules,
    form,
    gridData,
    params,
    changeOrganization,
    addressLinkDom,
    focusCourseId,
    beforeCloseDom,
    editAdd,
    fileUpload,
    confirmAdd,
    buttonLoading,

    onSearchA,
    onSearchB
  };
}
