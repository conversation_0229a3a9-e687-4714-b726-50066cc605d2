import { h, ref, toRaw, watch, computed, reactive, onMounted } from "vue";
import "plus-pro-components/es/components/form/style/css";
import dayjs from "dayjs";
import {
  ElForm,
  ElInput,
  ElFormItem,
  ElProgress,
  ElMessageBox
} from "element-plus";
import {
  getRoleIds,
  getDeptList,
  getUserList,
  getAllRoleList
} from "@/api/system";
export function useLocalend(tableRef) {
  const form = ref({
    // 左侧部门树的id
    time: "",
    name: "",
    phone: ""
  });
  const formRef = ref();
  const ruleFormRef = ref();
  const dataList = ref([]);
  const loading = ref(true);
  const loadingTable = ref(false);
  // 上传头像信息
  const avatarInfo = ref();
  const switchLoadMap = ref({});
  const higherDeptOptions = ref();
  const treeData = ref([]);
  const treeLoading = ref(true);
  const selectedNum = ref(0);
  const pagination = reactive({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true
  });
  const columns = [
    //   {
    //     label: "勾选列", // 如果需要表格多选，此处label必须设置
    //     type: "selection",
    //     fixed: "left",
    //     reserveSelection: true // 数据刷新后保留选项
    //   },
    {
      label: "课程名",
      prop: "cursename",
      width: 90
    },
    //   {
    //     label: "用户头像",
    //     prop: "avatar",
    //     cellRenderer: ({ row }) => (
    //       <el-image
    //         fit="cover"
    //         preview-teleported={true}
    //         src={row.avatar || userAvatar}
    //         preview-src-list={Array.of(row.avatar || userAvatar)}
    //         class="w-[24px] h-[24px] rounded-full align-middle"
    //       />
    //     ),
    //     width: 90
    //   },
    {
      label: "期号",
      prop: "number",
      minWidth: 90
    },
    {
      label: "机构",
      prop: "institutionname",
      minWidth: 130
    },

    {
      label: "申请时间",
      minWidth: 90,
      prop: "createTime",
      formatter: ({ createTime }) =>
        dayjs(createTime).format("YYYY-MM-DD HH:mm:ss")
    },
    {
      label: "审批类型",
      prop: "type",
      minWidth: 90,
      cellRenderer: ({ row, props }) => (
        <div
          size={props.size}
          type={row.type === 1 ? "danger" : null}
          effect="plain"
        >
          {row.sex === 1 ? "上架" : "下架"}
        </div>
      )
    },
    {
      label: "审核状态",
      prop: "status",
      minWidth: 90,
      cellRenderer: scope => (
        <el-switch
          size={scope.props.size === "small" ? "small" : "default"}
          loading={switchLoadMap.value[scope.index]?.loading}
          v-model={scope.row.status}
          active-value={1}
          inactive-value={0}
          active-text="已启用"
          inactive-text="已停用"
          inline-prompt
          onChange={() => onChange(scope)}
        />
      )
    },

    {
      label: "操作",
      fixed: "right",
      width: 120,
      slot: "operation"
    }
  ];
  // 筛选
  const columnsPS = [
    {
      label: "创建时间",
      prop: "time",
      valueType: "copy"
    },
    {
      label: "机构",
      prop: "institution"
    },
    {
      label: "课程名",
      prop: "curseName"
    },
    {
      label: "期数",
      prop: "number"
    },
    {
      label: "审批类型",
      prop: "type",
      valueType: "select",
      options: [
        {
          label: "全部",
          value: "0"
        },
        {
          label: "上架",
          value: "1"
        },
        {
          label: "下架",
          value: "2"
        }
      ]
    },
    {
      label: "审批状态",
      prop: "status",
      valueType: "select",
      options: [
        {
          label: "全部",
          value: "0"
        },
        {
          label: "审核中",
          value: "1"
        },
        {
          label: "已驳回",
          value: "2"
        },
        {
          label: "审核通过",
          value: "3"
        }
      ]
    }
  ];
  const buttonClass = computed(() => {
    return [
      "!h-[20px]",
      "reset-margin",
      "!text-gray-500",
      "dark:!text-white",
      "dark:hover:!text-primary"
    ];
  });
  const roleOptions = ref([]);

  function onChange({ row, index }) {
    ElMessageBox.confirm(
      `确认要<strong>${
        row.status === 0 ? "停用" : "启用"
      }</strong><strong style='color:var(--el-color-primary)'>${
        row.username
      }</strong>用户吗?`,
      "系统提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        dangerouslyUseHTMLString: true,
        draggable: true
      }
    )
      .then(() => {
        switchLoadMap.value[index] = Object.assign(
          {},
          switchLoadMap.value[index],
          {
            loading: true
          }
        );
        setTimeout(() => {
          switchLoadMap.value[index] = Object.assign(
            {},
            switchLoadMap.value[index],
            {
              loading: false
            }
          );
          //   message("已成功修改用户状态", {
          //     type: "success"
          //   });
        }, 300);
      })
      .catch(() => {
        row.status === 0 ? (row.status = 1) : (row.status = 0);
      });
  }

  function handleUpdate(row) {
    console.log(row);
  }

  function handleDelete(row) {
    // message(`您删除了用户编号为${row.id}的这条数据`, { type: "success" });
    onSearch();
  }

  function handleSizeChange(val) {
    console.log(`${val} items per page`);
  }

  function handleCurrentChange(val) {
    console.log(`current page: ${val}`);
  }

  /** 当CheckBox选择项发生变化时会触发该事件 */
  function handleSelectionChange(val) {
    selectedNum.value = val.length;
    // 重置表格高度
    tableRef.value.setAdaptive();
  }

  /** 取消选择 */
  function onSelectionCancel() {
    selectedNum.value = 0;
    // 用于多选表格，清空用户的选择
    tableRef.value.getTableRef().clearSelection();
  }

  async function onSearch() {
    loading.value = true;
    const { data } = await getUserList(toRaw(form.value));
    dataList.value = data.list;
    pagination.total = data.total;
    pagination.pageSize = data.pageSize;
    pagination.currentPage = data.currentPage;

    setTimeout(() => {
      loading.value = false;
    }, 500);
  }
  async function handleSearch() {
    loading.value = true;
    const { data } = await getUserList(toRaw(form.value));
    dataList.value = data.list;
    pagination.total = data.total;
    pagination.pageSize = data.pageSize;
    pagination.currentPage = data.currentPage;

    setTimeout(() => {
      loading.value = false;
    }, 500);
  }

  //   const resetForm = formEl => {
  //     if (!formEl) return;
  //     formEl.resetFields();
  //     form.value.deptId = "";
  //     treeRef.value.onTreeReset();
  //     onSearch();
  //   };

  function onTreeSelect({ id, selected }) {
    form.value.deptId = selected ? id : "";
    onSearch();
  }

  function formatHigherDeptOptions(treeList) {
    // 根据返回数据的status字段值判断追加是否禁用disabled字段，返回处理后的树结构，用于上级部门级联选择器的展示（实际开发中也是如此，不可能前端需要的每个字段后端都会返回，这时需要前端自行根据后端返回的某些字段做逻辑处理）
    if (!treeList || !treeList.length) return;
    const newTreeList = [];
    for (let i = 0; i < treeList.length; i++) {
      treeList[i].disabled = treeList[i].status === 0 ? true : false;
      formatHigherDeptOptions(treeList[i].children);
      newTreeList.push(treeList[i]);
    }
    return newTreeList;
  }

  const cropRef = ref();
  onMounted(async () => {
    treeLoading.value = true;
    // onSearch();
    handleSearch();
    // 归属部门
    const { data } = await getDeptList();
    treeLoading.value = false;

    // 角色列表
    roleOptions.value = (await getAllRoleList()).data;
  });

  return {
    form,
    columnsPS,
    loading,
    columns,
    dataList,
    treeData,
    treeLoading,
    selectedNum,
    pagination,
    buttonClass,
    onSearch,
    handleSearch,
    handleUpdate,
    handleSizeChange,
    onSelectionCancel,
    handleCurrentChange,
    handleSelectionChange,
    loadingTable
  };
}
