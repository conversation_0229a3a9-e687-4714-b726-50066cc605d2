<script setup>
import { ref, onMounted } from "vue";
import {
  platformfindById,
  platformResetPassword,
  platformIsFreeze
} from "@/api/platform.js";
import { requestTo } from "@/utils/http/tool";
import { useRoute, useRouter } from "vue-router";
import { formatTime, generatePassword } from "@/utils/index.js";
import { decrypt, encryption } from "@/utils/SM4.js";
import popup from "./components/popup.vue";
import { usePopup } from "vue-hooks-pure";
import Eye from "@/components/Base/eyes.vue";
import Popup from "@/views/institution/components/popup.vue";
import { ElMessage, ElMessageBox } from "element-plus";
const route = useRoute();
const router = useRouter();
// 表头
const tableHeader = ref([
  {
    id: "1",
    label: "账号ID",
    value: "--",
    width: "107px"
  },
  {
    id: "2",
    label: "姓名",
    value: "--",
    width: "107px"
  },
  {
    id: "3",
    label: "创建时间",
    value: "--",
    width: "107px"
  },
  {
    id: "4",
    label: "账号",
    value: "--",
    width: "107px"
  },
  {
    id: "5",
    label: "手机号",
    value: "",
    width: "107px",
    valueCt: ""
  },
  {
    id: "6",
    label: "身份证号",
    value: "",
    width: "107px",
    valueCt: ""
  },
  {
    id: "7",
    label: "邮箱",
    value: "--",
    width: "107px"
  },
  {
    id: "8",
    label: "角色",
    value: "--",
    width: "107px"
  }
]);
const newData = ref({});
// 重置密码弹窗
const dialogFormVisible = ref(false);
// 重置密码
const reset = () => {
  newData.value.id = personDetails.value.id;
  newData.value.name = personDetails.value.name;
  newData.value.phone = decrypt(personDetails.value.phoneCt);
  newData.value.account = personDetails.value.account;
  newData.value.newPassword = generatePassword(
    personDetails.value.name,
    decrypt(personDetails.value.phoneCt)
  );
  newData.value.operateLogType = "ACCOUNT_MANAGEMENT";
  newData.value.operateType = `重置了${personDetails.value?.name}的平台账号密码`;
  dialogFormVisible.value = true;
};
// 编辑信息
const edite = () => {
  // console.log("💗edite---------->");
  router.push({
    path: "/account/platform/account/add",
    query: { id: route.query?.id, type: "edite" }
  });
};
const personDetails = ref({});
// 查询
const getPlatformfindById = async () => {
  const params = {
    id: route.query.id
  };
  const [err, res] = await requestTo(platformfindById(params));
  if (res) {
    personDetails.value = res;
    // console.log(
    //   "🐬  personDetails.value------------------------------>",
    //   personDetails.value
    // );
    tableHeader.value[0].value = res?.id || "--";
    tableHeader.value[1].value = res?.name || "--";
    tableHeader.value[2].value = res?.createdAt
      ? formatTime(res?.createdAt)
      : "--";
    tableHeader.value[3].value = res?.account || "--";
    tableHeader.value[4].value = decrypt(res?.phoneCt) || "--";
    tableHeader.value[4].valueCt = res?.phone || "--";
    tableHeader.value[5].value = decrypt(res?.idNumberCt) || "--";
    tableHeader.value[5].valueCt = res?.idNumber || "--";
    tableHeader.value[6].value = res?.email || "--";
    tableHeader.value[7].value =
      res?.roles.map(it => it.name).join("、") || "--";
    // console.log('🌳 tableData.value------------------------------>',tableData.value);
  }
  if (err) {
    console.log("err------------------------------>", err);
  }
};
// 解冻
const freezeNotEvt = () => {
  ElMessageBox.confirm(`确定要解冻该账号吗`, "解冻账号", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(async () => {
      const params = {
        id: personDetails.value?.id,
        freeze: false
      };
      const operateLog = {
        operateLogType: "ACCOUNT_MANAGEMENT",
        operateType: `解冻了“${personDetails.value?.name}”的平台账号`
      };
      const { code, msg } = await platformIsFreeze(params, operateLog);
      if (code === 200) {
        ElMessage({
          type: "success",
          message: "解冻成功"
        });
        getPlatformfindById();
      } else {
        ElMessage({
          type: "error",
          message: `解冻失败,${msg}`
        });
      }
    })
    .catch(() => {
      // ElMessage({
      //   type: 'info',
      //   message: 'Delete canceled',
      // })
    });
};
// 冻结
const freezeEvt = () => {
  ElMessageBox.confirm(`确定要冻结该账号吗`, "冻结账号", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(async () => {
      const params = {
        id: personDetails.value?.id,
        freeze: true
      };
      const operateLog = {
        operateLogType: "ACCOUNT_MANAGEMENT",
        operateType: `冻结了“${personDetails.value?.name}”的平台账号`
      };
      const { code, msg } = await platformIsFreeze(params, operateLog);
      if (code === 200) {
        ElMessage({
          type: "success",
          message: "冻结成功"
        });
        getPlatformfindById();
      } else {
        ElMessage({
          type: "error",
          message: `冻结失败,${msg}`
        });
      }
    })
    .catch(() => {
      // ElMessage({
      //   type: 'info',
      //   message: 'Delete canceled',
      // })
    });
};
onMounted(() => {
  getPlatformfindById();
});
</script>

<template>
  <div class="detail-container">
    <div class="detail-table">
      <el-descriptions
        class="margin-top"
        title=""
        :column="3"
        border
        :label-width="'200px'"
      >
        <template v-for="(item, index) in tableHeader" :key="index">
          <el-descriptions-item label-align="center">
            <template #label>
              <div class="cell-item">{{ item.label }}</div>
            </template>

            <Eye
              v-if="item.label === '手机号' && item.value !== '--'"
              :data="item.value"
              :dataCt="item.valueCt"
              :label="item.label"
            />
            <Eye
              v-else-if="item.label === '身份证号' && item.value !== '--'"
              :data="item.value"
              :dataCt="item.valueCt"
              :label="item.label"
            />
            <div v-else>{{ item.value }}</div>
          </el-descriptions-item>
        </template>
      </el-descriptions>
    </div>
    <div class="detail-button">
      <div v-if="personDetails.freeze === true" class="freeze-text">
        本账号已冻结
      </div>
      <!-- <div class="reset" @click="reset">重置密码</div> -->
      <el-button type="danger" @click="reset">重置密码</el-button>
      <el-button
        v-if="personDetails.freeze === true"
        type="danger"
        @click="freezeNotEvt"
      >
        解冻
      </el-button>
      <el-button
        v-if="personDetails.freeze === false"
        type="primary"
        @click="freezeEvt"
      >
        冻结
      </el-button>
      <el-button type="primary" @click="edite">编辑信息</el-button>
      <!-- <div class="edite" @click="edite">编辑信息</div> -->
    </div>
    <Popup
      :id="newData?.id"
      v-model:dialogFormVisible="dialogFormVisible"
      :api="platformResetPassword"
      :name="newData?.name"
      :phone="newData?.phone"
      :account="newData?.account"
      :operateLogType="newData?.operateLogType"
      :newPassword="newData?.newPassword"
      :operateType="newData?.operateType"
      :logOut="false"
      @reset="dialogFormVisible = false"
    />
  </div>
</template>

<style lang="scss" scoped>
.detail-container {
  // width: 100%;
  height: calc(100vh - 114px);
  box-sizing: border-box;
  padding: 20px 20px;
  background-color: #fff;

  .detail-table {
    width: 100%;
    margin-bottom: 30px;
  }

  .detail-button {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    .freeze-text {
      color: red;
      font-size: 14px;
      margin-right: 20px;
    }
  }
}

:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  background: #e1f5ff;
}
</style>
