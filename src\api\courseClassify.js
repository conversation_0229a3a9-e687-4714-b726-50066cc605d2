import { http } from "@/utils/http";

// 新增课程分类
export const courseTypeSave = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/courseType/save",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ACCOUNT_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
// 上移或下移
export const courseTypeMove = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/courseType/move",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ACCOUNT_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
// 删除
export const courseTypeDelete = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/courseType/delete",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ACCOUNT_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
// 分页查询
export const courseTypeFindAll = params => {
  return http.request(
    "get",
    "/platform/courseType/findAll",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 根据父Id查询分类
export const courseTypeFindByParentId = params => {
  return http.request(
    "get",
    "/platform/courseType/findByParentId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 修改课程分类的分类名
export const courseTypeUpdate = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/courseType/update",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ACCOUNT_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
