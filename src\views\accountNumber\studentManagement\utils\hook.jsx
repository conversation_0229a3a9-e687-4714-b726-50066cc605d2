import { Edit, Hide, View } from "@element-plus/icons-vue";
import { onMounted, reactive, ref, onActivated } from "vue";
import { studentFindAll } from "@/api/studentManage.js";
import { requestTo } from "@/utils/http/tool";
import { useRouter } from "vue-router";
import { decrypt, encryption } from "@/utils/SM4.js";
import dayjs from "dayjs";

export function useRole() {
  const columns = [
    {
      label: "学生ID", // 如果需要表格多选，此处label必须设置
      prop: "id",
      width: 90,
      formatter: ({ id }) => {
        return id || "--";
      }
    },
    {
      label: "学生姓名",
      prop: "name",
      width: 200,
      formatter: ({ name }) => {
        return name || "--";
      }
    },
    {
      label: "学校名称",
      prop: "school",
      minWidth: 90,
      formatter: ({ school }) => {
        return school || "--";
      }
    },
    {
      label: "证件类型",
      prop: "idType",
      minWidth: 130,
      formatter: ({ idType }) => {
        return idType || "--";
      }
    },
    {
      label: "证件号",
      prop: "idNumber",
      minWidth: 200,
      cellRenderer: ({ row }) =>
        row.idNumber
? (
          <div style="display: flex;">
            {row.isName === true
? (
              <div style="min-width: 170px">{idNumberAdd(row.idNumberCt)}</div>
            )
: (
              <div style="min-width: 170px">{row.idNumber || "--"}</div>
            )}
            {row.isName === true
? (
              <el-icon
                style=" margin-left: 8px; margin-top: 4px;"
                onClick={() => imgAdd(row)}
              >
                <View />
              </el-icon>
            )
: (
              <el-icon
                style=" margin-left: 8px; margin-top: 4px;"
                onClick={() => imgAdd(row)}
              >
                <Hide />
              </el-icon>
            )}
          </div>
        )
: (
          <div>--</div>
        )
    },
    {
      label: "创建时间",
      width: 180,
      prop: "createdAt",
      formatter: ({ createdAt }) => {
        return createdAt
          ? dayjs(createdAt).format("YYYY-MM-DD HH:mm:ss")
          : "--";
      }
    },
    {
      label: "操作",
      fixed: "right",
      width: 120,
      slot: "operation"
    }
  ];

  const router = useRouter();
  const dataList = ref([]);
  const loadingTable = ref(false);
  const pagination = {
    total: 0,
    pageSize: 15,
    currentPage: 1,
    background: true
  };
  const form = reactive({
    time: [],
    name: "",
    school: ""
  });

  const startAdd = async () => {
    const paramsArg = {
      page: 0,
      size: 15,
      sort: "createdAt,desc"
    };
    const [err, res] = await requestTo(studentFindAll(paramsArg));
    if (res) {
      // console.log("🌵-----res.content-----", res.content);
      dataList.value = res.content;
      pagination.total = res.totalElements;
      // pagination.pageSize = res.size;
      // pagination.currentPage = 1;
    }
    if (err) {
    }
  };
  const removeEmptyValues = obj => {
    return Object.fromEntries(
      Object.entries(obj).filter(
        ([key, value]) =>
          value !== "" && !(Array.isArray(value) && value.length === 0)
      )
    );
  };
  const startAdd1 = async val => {
    const paramsArg = {
      name: val.name || "",
      school: val.school || "",
      startTime: val.startTime || "",
      endTime: val.endTime || "",
      page: val.page || 0,
      size: val.size || "",
      sort: "createdAt,desc"
    };
    let aee = removeEmptyValues(paramsArg);
    console.log("🐳-----aee-----", aee);
    const [err, res] = await requestTo(studentFindAll(aee));
    if (res) {
      console.log("🎁-----res---开始110--", res);
      dataList.value = res.content;
      pagination.total = res.totalElements;
      // // pagination.pageSize = res.size;
      // pagination.currentPage = res.totalPages === 0 ? 1 : res.totalPages;
    }
    if (err) {
    }
  };
  // 搜索
  const onSearch = () => {
    let time1 = form.time !== null ? new Date(form.time[0]).getTime() : "";
    let time2 = form.time !== null ? new Date(form.time[1]).getTime() : "";
    const text = {
      name: form.name || "",
      school: form.school || "",
      startTime: time1 || "",
      endTime: time2 || "",
      page: pagination.currentPage - 1 || "",
      size: pagination.pageSize || ""
    };
    startAdd1(text);
  };
  // 重置
  const setData = () => {
    form.name = "";
    form.school = "";
    form.time = [];
    startAdd();
  };

  // 每页多少条
  async function handleSizeChange(val) {
    pagination.pageSize = val;
    let time1 = form.time !== null ? new Date(form.time[0]).getTime() : "";
    let time2 = form.time !== null ? new Date(form.time[1]).getTime() : "";
    const paramsArg = {
      name: form.name || "",
      school: form.school || "",
      startTime: time1 || "",
      endTime: time2 || "",
      page: 0,
      size: val
    };
    startAdd1(paramsArg);
  }
  // 前往页数
  async function handleCurrentChange(val) {
    pagination.currentPage = val;
    console.log("🎉-----val-----", val);
    let time1 = form.time !== null ? new Date(form.time[0]).getTime() : "";
    let time2 = form.time !== null ? new Date(form.time[1]).getTime() : "";
    const paramsArg = {
      name: form.name || "",
      school: form.school || "",
      startTime: time1 || "",
      endTime: time2 || "",
      page: val - 1,
      size: pagination.pageSize
    };
    startAdd1(paramsArg);
  }
  // 详情
  const openDialog = (text, val) => {
    router.push({
      path: "/studentManagement/componts/studentDetails",
      query: { id: val.id }
    });
  };

  // 证件号
  function imgAdd(val) {
    dataList.value.map(it => {
      if (it.id === val.id) {
        it.isName = !val.isName;
      }
    });
  }
  function idNumberAdd(val) {
    if (val) {
      return decrypt(val);
    } else {
      return "--";
    }
  }

  onActivated(() => {
    let time1 = form.time !== null ? new Date(form.time[0]).getTime() : "";
    let time2 = form.time !== null ? new Date(form.time[1]).getTime() : "";
    const text = {
      name: form.name || "",
      school: form.school || "",
      startTime: time1 || "",
      endTime: time2 || "",
      page: pagination.currentPage - 1 || "",
      size: pagination.pageSize || ""
    };
    startAdd1(text);
  });

  onMounted(async () => {
    startAdd();
  });

  return {
    form,
    pagination,
    dataList,
    columns,
    onSearch,
    setData,
    // handleInput,
    // toAdd,
    openDialog,
    // isFreezeApi,
    handleCurrentChange,
    handleSizeChange,
    loadingTable
  };
}
