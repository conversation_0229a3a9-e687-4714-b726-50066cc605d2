<script setup>
import { ref, reactive, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { PlusSearch } from "plus-pro-components";
import { ElMessage } from "element-plus";

defineOptions({
  name: "CouponManagementDetail"
});

const router = useRouter();
const route = useRoute();

// 优惠券详情数据
const couponDetail = ref({
  couponName: "超级一家优惠券",
  couponType: "满减券",
  status: "启用",
  discountRule: "满100减50",
  createTime: "2024-10-12 16:00:11",
  issueTime: "2025-10-11 至 2025-11-11",
  useTime: "2025-11-11 至 2025-12-11",
  notes: "优惠券全场，一人一次。"
});

// 搜索配置
const searchColumns = ref([
  {
    label: "用户账号",
    prop: "userAccount",
    fieldProps: {
      style: { width: "200px" }
    }
  },
  {
    label: "获取方式",
    prop: "getMethod",
    valueType: "select",
    options: [
      { label: "全部", value: "" },
      { label: "在线领取", value: "online" },
      { label: "手动派发", value: "manual" }
    ],
    fieldProps: {
      style: { width: "150px" }
    }
  },
  {
    label: "优惠券状态",
    prop: "couponStatus",
    valueType: "select",
    options: [
      { label: "全部", value: "" },
      { label: "未使用", value: "unused" },
      { label: "已使用", value: "used" },
      { label: "已过期", value: "expired" }
    ],
    fieldProps: {
      style: { width: "150px" }
    }
  }
]);

// 搜索表单数据
const searchForm = reactive({
  userAccount: "",
  getMethod: "",
  couponStatus: ""
});

// 表格列配置
const columns = ref([
  {
    label: "用户账号",
    prop: "userAccount",
    minWidth: 120
  },
  {
    label: "昵称",
    prop: "nickname",
    minWidth: 100
  },
  {
    label: "领取时间",
    prop: "receiveTime",
    minWidth: 150
  },
  {
    label: "使用时间",
    prop: "useTime",
    minWidth: 150
  },
  {
    label: "优惠券使用类型",
    prop: "couponUseType",
    minWidth: 120
  },
  {
    label: "优惠说明",
    prop: "description",
    minWidth: 150
  },
  {
    label: "获取方式",
    prop: "getMethod",
    minWidth: 100
  },
  {
    label: "操作",
    prop: "operation",
    minWidth: 100,
    fixed: "right"
  }
]);

// 表格数据
const tableData = ref([]);
const loading = ref(false);

// 分页配置
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
});

// 统计数据
const statistics = ref({
  issued: 350,
  received: 350,
  used: 350
});

// Mock数据
const mockTableData = [
  {
    id: 1,
    userAccount: "135****2457",
    nickname: "老张",
    receiveTime: "2025-04-11 12:34:11",
    useTime: "2025-04-21 12:22:11",
    couponUseType: "材料",
    description: "开元山庄松的课程",
    getMethod: "在线领取"
  },
  {
    id: 2,
    userAccount: "135****2457",
    nickname: "老王",
    receiveTime: "2025-04-11 12:34:11",
    useTime: "2025-04-21 12:22:11",
    couponUseType: "通用",
    description: "开元山庄松的课程",
    getMethod: "在线领取"
  },
  {
    id: 3,
    userAccount: "135****2457",
    nickname: "老方",
    receiveTime: "2025-04-11 12:34:11",
    useTime: "2025-04-21 12:22:11",
    couponUseType: "材料",
    description: "开元山庄松的课程",
    getMethod: "手动派发"
  },
  {
    id: 4,
    userAccount: "135****2457",
    nickname: "老老",
    receiveTime: "2025-04-11 12:34:11",
    useTime: "",
    couponUseType: "",
    description: "",
    getMethod: ""
  },
  {
    id: 5,
    userAccount: "135****2457",
    nickname: "网友",
    receiveTime: "2025-04-11 12:34:11",
    useTime: "",
    couponUseType: "",
    description: "",
    getMethod: ""
  }
];

// 加载表格数据
const loadTableData = () => {
  loading.value = true;
  setTimeout(() => {
    tableData.value = mockTableData;
    pagination.total = mockTableData.length;
    loading.value = false;
  }, 500);
};

// 搜索处理
const handleSearch = () => {
  console.log("搜索条件:", searchForm);
  loadTableData();
};

// 重置搜索
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = "";
  });
  loadTableData();
};

// 新增优惠券
const handleAddCoupon = () => {
  router.push("/coupon/management/create");
};

// 关联门第操作
const handleRelateStore = (row) => {
  ElMessage.info(`关联门第: ${row.userAccount}`);
};

// 返回
const handleBack = () => {
  router.go(-1);
};

onMounted(() => {
  // 获取路由参数中的优惠券ID
  const couponId = route.params.id;
  console.log("优惠券ID:", couponId);

  // 加载优惠券详情和表格数据
  loadTableData();
});
</script>

<template>
  <div class="coupon-detail">
    <!-- 优惠券详情展示区域 -->
    <div class="detail-section">
      <div class="detail-card">
        <div class="detail-row">
          <div class="detail-item">
            <span class="label">优惠券名称：</span>
            <span class="value">{{ couponDetail.couponName }}</span>
          </div>
          <div class="detail-item">
            <span class="label">优惠券类型：</span>
            <span class="value">{{ couponDetail.couponType }}</span>
          </div>
        </div>

        <div class="detail-row">
          <div class="detail-item">
            <span class="label">状态：</span>
            <span class="value">{{ couponDetail.status }}</span>
          </div>
          <div class="detail-item">
            <span class="label">优惠门槛与金额：</span>
            <span class="value">{{ couponDetail.discountRule }}</span>
          </div>
        </div>

        <div class="detail-row">
          <div class="detail-item">
            <span class="label">创建时间：</span>
            <span class="value">{{ couponDetail.createTime }}</span>
          </div>
          <div class="detail-item">
            <span class="label">发放时间：</span>
            <span class="value">{{ couponDetail.issueTime }}</span>
          </div>
        </div>

        <div class="detail-row">
          <div class="detail-item">
            <span class="label">备注：</span>
            <span class="value">{{ couponDetail.notes }}</span>
          </div>
          <div class="detail-item">
            <span class="label">使用时间：</span>
            <span class="value">{{ couponDetail.useTime }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <!-- 搜索区域 -->
      <div class="search-section">
        <plus-search
          v-model="searchForm"
          :columns="searchColumns"
          :show-number="6"
          label-width="auto"
          class="search-form"
          @search="handleSearch"
          @reset="handleReset"
        >
          <template #footer>
            <el-button type="primary" @click="handleAddCoupon">
              新增优惠券
            </el-button>
          </template>
        </plus-search>
      </div>

      <!-- 统计信息 -->
      <div class="statistics">
        <span class="stat-item">发放数量：{{ statistics.issued }}</span>
        <span class="stat-item">领取数量：{{ statistics.received }}</span>
        <span class="stat-item">使用数量：{{ statistics.used }}</span>
      </div>

      <!-- 表格 -->
      <div class="table-container">
        <pure-table
          ref="tableRef"
          :data="tableData"
          :columns="columns"
          :loading="loading"
          :pagination="pagination"
          @page-size-change="loadTableData"
          @page-current-change="loadTableData"
        >
          <template #operation="{ row }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              @click="handleRelateStore(row)"
            >
              关联门第
            </el-button>
          </template>
        </pure-table>
      </div>

      <!-- 返回按钮 -->
      <div class="footer-actions">
        <el-button @click="handleBack">返回</el-button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.coupon-detail {

  .detail-section {
    margin-bottom: 20px;
  }

  .detail-card {
    background-color: #fff;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 24px;
  }

  .detail-row {
    display: flex;
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .detail-item {
    flex: 1;
    display: flex;
    align-items: center;

    .label {
      color: #606266;
      font-size: 14px;
      min-width: 120px;
      flex-shrink: 0;
    }

    .value {
      color: #303133;
      font-size: 14px;
      font-weight: 500;
    }
  }

  .table-section {
    background-color: #fff;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 20px;
  }

  .search-section {
    margin-bottom: 20px;

    .search-form {
      :deep(.plus-search) {
        background-color: #fff;
      }
    }
  }

  .statistics {
    display: flex;
    gap: 40px;
    margin-bottom: 20px;
    padding: 12px 0;
    border-bottom: 1px solid #e4e7ed;

    .stat-item {
      font-size: 14px;
      color: #303133;
      font-weight: 500;
    }
  }

  .table-container {
    margin-bottom: 20px;
  }

  .footer-actions {
    display: flex;
    justify-content: center;
    padding-top: 20px;
    border-top: 1px solid #e4e7ed;
  }
}
</style>
