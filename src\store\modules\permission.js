import { defineStore } from "pinia";
import {
  ascending,
  constantMenus,
  debounce,
  filterNoPermissionTree,
  filterTree,
  formatFlatteningRoutes,
  getKeyList,
  store
} from "../utils";
import { useMultiTagsStoreHook } from "./multiTags";

function filterData(data, filterFn) {
  return data.filter(item => {
    if (item.children) {
      const filteredChildren = filterData(item.children, filterFn);
      item.children = filteredChildren; // 更新子元素
      return filteredChildren.length > 0; // 如果子元素有符合条件的，保留当前元素
    }
    return filterFn(item); // 使用自定义过滤函数检查当前元素
  });
}
function getAllIdCodes(data) {
  const idCodes = new Set();
  function traverse(node) {
    if (node.idCode) {
      idCodes.add(node.idCode);
    }
    if (node.children) {
      node.children.forEach(traverse);
    }
  }
  data.forEach(traverse);
  return Array.from(idCodes);
}

export const usePermissionStore = defineStore({
  id: "pure-permission",
  state: () => ({
    // 静态路由生成的菜单
    constantMenus,
    // 整体路由生成的菜单（静态、动态）
    wholeMenus: [],
    // 整体路由（一维数组格式）
    flatteningRoutes: [],
    // 缓存页面keepAlive
    cachePageList: []
  }),
  actions: {
    /** 组装整体路由生成的菜单（兼容局端和平台端权限） */
    handleWholeMenus(routes) {
      // 安全获取用户信息和权限数据
      const getLocalStorageJSON = key => {
        try {
          return JSON.parse(localStorage.getItem(key) || "{}");
        } catch (e) {
          console.error(`解析 ${key} 失败`, e);
          return {};
        }
      };
      const userInfo = getLocalStorageJSON("user-info");
      const authorityData = getLocalStorageJSON("authorityDTOS");

      // 判断是否局端管理员
      const isAdmin = userInfo.roleTarget === "局端管理员";

      // 核心逻辑：无论是否局端，只要有 authorityDTOS 就按权限过滤
      const rawMenus = filterTree(ascending(this.constantMenus.concat(routes)));

      if (authorityData.authorityDTOS?.length > 0) {
        // 权限过滤模式（平台端或带权限的局端）
        const idCodeList = getAllIdCodes(authorityData.authorityDTOS).map(
          Number
        );
        const matchFunction = item =>
          item.meta?.idCode && idCodeList.includes(item.meta.idCode);
        this.wholeMenus = filterData(
          filterNoPermissionTree(rawMenus),
          matchFunction
        );
      } else {
        // 无权限数据时
        if (isAdmin) {
          // 如果是局端管理员，使用静态权限列表过滤
          const matchFunction = item =>
            item.meta?.idCode &&
            [200, 202, 204, 300, 303].includes(item.meta.idCode);
          this.wholeMenus = filterData(
            filterNoPermissionTree(rawMenus),
            matchFunction
          );
        } else {
          // 非局端管理员且无权限数据，菜单为空
          this.wholeMenus = [];
        }
      }

      this.flatteningRoutes = formatFlatteningRoutes(
        this.constantMenus.concat(routes)
      );
    },
    cacheOperate({ mode, name }) {
      const delIndex = this.cachePageList.findIndex(v => v === name);
      switch (mode) {
        case "refresh":
          this.cachePageList = this.cachePageList.filter(v => v !== name);
          break;
        case "add":
          this.cachePageList.push(name);
          break;
        case "delete":
          delIndex !== -1 && this.cachePageList.splice(delIndex, 1);
          break;
      }
      /** 监听缓存页面是否存在于标签页，不存在则删除 */
      debounce(() => {
        let cacheLength = this.cachePageList.length;
        const nameList = getKeyList(useMultiTagsStoreHook().multiTags, "name");
        while (cacheLength > 0) {
          nameList.findIndex(v => v === this.cachePageList[cacheLength - 1]) ===
          -1 &&
          this.cachePageList.splice(
              this.cachePageList.indexOf(this.cachePageList[cacheLength - 1]),
              1
            );
          cacheLength--;
        }
      })();
    },
    /** 清空缓存页面 */
    clearAllCachePage() {
      this.wholeMenus = [];
      this.cachePageList = [];
    }
  }
});

export function usePermissionStoreHook() {
  return usePermissionStore(store);
}
