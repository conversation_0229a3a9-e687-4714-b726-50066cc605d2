<script setup>
import { reactive, ref, onMounted } from "vue";

import Carousel from "./carousel.vue";
import Descriptions from "./descriptions.vue";
import Course from "./course.vue";
import SchoolData from "./schoolData.vue";

const tabTitle = ref([
  { id: 1, name: "轮播图" },
  { id: 2, name: "热门课程" },
  { id: 2, name: "精品课程" },
  { id: 3, name: "学校数据" },
  { id: 4, name: "平台信息" }
]);
const infoShow = ref("轮播图");
const activeName = ref(0);
const handleClick = (tab, event) => {
  // console.log(tab, event);
  activeName.value = tab.props.name;
  infoShow.value = tab.props.label;
};
onMounted(() => {});
</script>

<template>
  <div class="">
    <!-- <div id="my-tt" ref="myqq" class="text_6">
      推荐课程-学校数据-平台信息--推荐课程-学校数据-平台信息---推荐课程-学校数据-平台信息----推荐课程-学校数据-平台信息
    </div> -->

    <div class="common">
      <!-- <div class="title">平台设置</div> -->
      <el-tabs
        v-model="activeName"
        type="card"
        class="demo-tabs"
        @tab-click="handleClick"
      >
        <el-tab-pane
          v-for="(item, index) in tabTitle"
          :key="index"
          :label="item.name"
          :name="index"
        />
      </el-tabs>
      <div class="tab-info">
        <Carousel v-if="infoShow === '轮播图'" />
        <Course v-else-if="infoShow === '热门课程'" :label="'热门课程'" />
        <Course v-else-if="infoShow === '精品课程'" :label="'精品课程'" />
        <SchoolData v-else-if="infoShow === '学校数据'" />
        <Descriptions v-else-if="infoShow === '平台信息'" />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.common {
  width: 100%;
  height: 100%;
  padding: 20px 20px 2px;
  background-color: #fff;
  .title {
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: bold;
    color: #606266;
  }
}

.tab-info {
  box-sizing: border-box;
  width: 100%;
}

.demo-tabs > .el-tabs__content {
  padding: 32px;
  font-size: 32px;
  font-weight: 600;
  color: #6b778c;
}

:deep(.el-tabs__item.is-active) {
  color: #fff;
  background-color: #409eff;
}

.text_6 {
  background-color: #eee;
  width: 150px;
  border: 1px solid red;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3; /* 限制在3行内 */
}
</style>
