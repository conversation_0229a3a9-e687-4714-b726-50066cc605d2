<script setup>
import { ref, reactive, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import RichEditor from "@/components/Base/RichEditor.vue";
import dayjs from "dayjs";
import { ElMessage } from "element-plus";
// import { discoverFindById } from "@/api/findapi.js";
import { newsFindById } from "@/api/newsapi.js";
import { ImageThumbnail } from "@/utils/imageProxy";
const router = useRouter();
const route = useRoute();

const ruleFormRef = ref();
const form = ref({
  id: "",
  fileVOS: [],
  fileList: [],
  title: "标题",
  content: "内容",
  createdAt: ""
});
const customerType = {
  PLATFORM_ADMIN: "平台管理员",
  ORGANIZATION_ADMIN: "组织管理员",
  PARENT: "家长",
  STUDENT: "学生",
  EDUCATION_BUREAU: "教育局"
};

const formData = ref([
  {
    label: "创建时间",
    type: "text",
    prop: "createdAt",
    // placeholder: "请输入姓名",
    width: "200px"
  },
  // 根据新接口，移除发布者姓名和用户类型字段
  // {
  //   label: "发布者姓名",
  //   type: "text",
  //   prop: "promoterName",
  //   placeholder: "请输入发布者姓名",
  //   width: "200px"
  // },
  // {
  //   label: "用户类型",
  //   type: "text",
  //   prop: "userType",
  //   placeholder: "请输入用户类型",
  //   width: "200px"
  // },
  {
    label: "标题",
    type: "text",
    prop: "title",
    placeholder: "请输入标题",
    width: "200px"
  },
  // 根据新接口，暂时注释掉图片显示功能
  // {
  //   label: "图片",
  //   type: "elUpload",
  //   prop: "fileList",
  //   placeholder: "请上传图片",
  //   width: "200px"
  //   // rowspan: 2
  // },
  {
    label: "内容",
    type: "RichEditor",
    prop: "content",
    placeholder: "请输入内容",
    width: "200px"
    // rowspan: 2
  }
]);

const url = ref("");
const srcList = ref([]);
// 获取新闻详情
const onDetails = async val => {
  try {
    const { code, data } = await newsFindById({ id: val.id });
    if (code === 200) {
      // 根据实际响应，成功时code为200
      console.log("🎁-----data-----", data);
      form.value.id = data.id;
      form.value.createdAt = dayjs(data.createdAt).format(
        "YYYY-MM-DD HH:mm:ss"
      );
      form.value.title = data.title;
      form.value.content = data.content;
      // 新接口没有promoterName和userType字段，移除相关处理
      // form.value.promoterName = data.promoterName;
      // form.value.userType = data.userType;

      // 新接口暂时没有文件相关字段，注释掉文件处理逻辑
      // if (data.files?.length) {
      //   data.files.map(item => {
      //     form.value.fileList.push({
      //       url: item.uploadFile.url,
      //       name: item.uploadFile.fileName
      //     });
      //     srcList.value.push(item?.uploadFile?.url);
      //   });
      //   url.value = data.files[0]?.uploadFile?.url;
      // }
    } else {
      ElMessage({
        type: "error",
        message: "获取新闻详情失败"
      });
    }
  } catch (error) {
    console.error("获取新闻详情失败:", error);
    ElMessage({
      type: "error",
      message: "获取新闻详情失败，请重试"
    });
  }
};

const batchImport = () => {
  // 编辑
  router.push({
    path: "/platform/components/newsEdit",
    query: { id: route.query.id }
  });
};

onMounted(() => {
  const id = { id: route.query.id };
  onDetails(id);
});
</script>

<template>
  <div class="commonapp">
    <!-- <div class="formbox">新闻详情</div> -->
    <el-scrollbar class="scrollbar">
      <div class="table_content">
        <el-form ref="ruleFormRef" :model="form">
          <el-descriptions title="" :column="1" border :label-width="'200px'">
            <el-descriptions-item
              v-for="(item, index) in formData"
              :key="index"
              label-align="center"
              label-class-name="my-label"
              :span="item.rowspan || 1"
            >
              <template #label>
                {{ item.label }}
              </template>
              <el-form-item
                :prop="item.prop"
                :inline-message="item.check"
                style="margin-bottom: 0"
                :show-message="true"
                error-placement="right"
              >
                <!-- input输入 -->
                <template v-if="item.type === 'input'">
                  <el-input
                    v-model="form[item.prop]"
                    :placeholder="item.placeholder"
                    :style="{ width: item.width }"
                  />
                </template>
                <!-- input输入 -->
                <template v-else-if="item.type === 'text'">
                  <span v-if="item.prop === 'userType'">
                    {{ customerType[form[item.prop]] }}
                  </span>
                  <span v-else>{{ form[item.prop] }}</span>
                </template>
                <!-- 图片上传 -->
                <template v-else-if="item.type === 'elUpload'">
                  <div v-for="(t, i) in srcList" :key="i">
                    <el-image
                      :src="ImageThumbnail(srcList[i], '120x')"
                      :zoom-rate="1.2"
                      :max-scale="7"
                      :min-scale="0.2"
                      show-progress
                      :initial-index="i"
                      :preview-src-list="srcList"
                      :hide-on-click-modal="true"
                      fit="scale-down"
                      class="img-pic"
                    />
                  </div>
                </template>
                <!-- 富文本 -->
                <template v-else-if="item.type === 'RichEditor'">
                  <RichEditor
                    v-model="form[item.prop]"
                    height="325px"
                    :isOpen="false"
                    :readOnly="true"
                  />
                </template>
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>
        </el-form>
      </div>
    </el-scrollbar>

    <div class="footer">
      <el-button @click="router.go(-1)">取消</el-button>
      <el-button type="primary" @click="batchImport()">编辑</el-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.commonapp {
  background-color: #fff;
  padding: 20px;
  .scrollbar {
    height: calc(100vh - 206px);
    background-color: #fff;
    margin-bottom: 20px;
  }
  .formbox {
    margin-bottom: 20px;
  }
  // .table_content {
  //   margin-bottom: 20px;
  // }
  .footer {
    display: flex;
    justify-content: end;
  }
}
.img-pic {
  width: 120px;
  height: 120px;
  margin-left: 20px;
  // object-fit: cover;
}

:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  background: #e1f5ff;
}
</style>
