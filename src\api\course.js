import { http } from "@/utils/http";
/** 列表查询 */
export const courseFindAll = params => {
  return http.request(
    "get",
    "/platform/course/findAll",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 是否冻结
export const courseIsFreeze = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/course/isFreeze",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
// 新增
export const localEndAdd = data => {
  return http.request(
    "post",
    "/platform/educationBureau/save",
    { data },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 修改
// export const localEndUpdate = data => {
//     return http.request(
//       "post",
//       "/platform/admin/update",
//       { data },
//       { isNeedToken: true,isNeedEncrypt: true }
//     );
//   };

/** id查询详情 */
export const courseFindById = params => {
  return http.request(
    "get",
    "/platform/course/findById",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
/** 期数分页查询 */
export const coursePeriodFindAll = params => {
  return http.request(
    "get",
    "/platform/coursePeriod/findAll",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
/** 行程安排查询 */
export const itineraryFindAll = params => {
  return http.request(
    "get",
    "/platform/itinerary/findByCoursePeriodId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
/** 局端行程安排查询 */
export const bureauItineraryFindAll = params => {
  return http.request(
    "get",
    "/educationBureau/itinerary/findByCoursePeriodId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
/** 课程介绍查询 */
export const courseIntroductionFindAll = params => {
  return http.request(
    "get",
    "/platform/courseIntroduction/findByCoursePeriodId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
/** 局端课程介绍查询 */
export const educationBureauCourseIntroductionFindAll = params => {
  return http.request(
    "get",
    "/educationBureau/courseIntroduction/findByCoursePeriodId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
/** 课程知识点查询 */
export const courseKnowledgePointFind = params => {
  return http.request(
    "get",
    "/platform/courseKnowledgePoint/findByCoursePeriodId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
/** 局端课程知识点查询 */
export const educationBureauCourseKnowledgePointFind = params => {
  return http.request(
    "get",
    "/educationBureau/courseKnowledgePoint/findByCoursePeriodId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
/** 材料说明查询 */
export const equipmentDescriptionFind = params => {
  return http.request(
    "get",
    "/platform/equipmentDescription/findByCoursePeriodId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
/** 局端材料说明查询 */
export const educationBureauEquipmentDescriptionFind = params => {
  return http.request(
    "get",
    "/educationBureau/equipmentDescription/findByCoursePeriodId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
/** 注意事项查询 */
export const precautionsFindAll = params => {
  return http.request(
    "get",
    "/platform/precautions/findByCoursePeriodId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
/** 局端注意事项查询 */
export const educationBureauPrecautionsFindAll = params => {
  return http.request(
    "get",
    "/educationBureau/precautions/findByCoursePeriodId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
/** 价格设置查询 */
export const priceSettingFindAll = params => {
  return http.request(
    "get",
    "/platform/priceSetting/findFeeItemByCoursePeriodId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
/** 局端价格设置规格查询 */
export const bureauPriceSettingFindAll = params => {
  return http.request(
    "get",
    "/educationBureau/priceSetting/findFeeItemByCoursePeriodId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
/** 规格说明查询 */
export const findSpecificationName = params => {
  return http.request(
    "get",
    "/platform/priceSetting/findSpecificationNameByCoursePeriodId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
/** 局端查询退款政策等 */
export const bureauFindfree = params => {
  return http.request(
    "get",
    "/educationBureau/priceSetting/findByCoursePeriodId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
/** 用户协议查询 */
export const findUserAgreement = params => {
  return http.request(
    "get",
    "/platform/userAgreement/findByCoursePeriodId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
/** 局端用户协议查询 */
export const educationBureauFindAgreement = params => {
  return http.request(
    "get",
    "/educationBureau/userAgreement/findByCoursePeriodId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
/** 根据课程id查询领队讲师 */
export const findLeaderLecturerByCourseId = params => {
  return http.request(
    "get",
    "/platform/coursePeriod/findLeaderLecturerByCourseId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
/** 费用说明退款政策查询 */
export const findFree = params => {
  return http.request(
    "get",
    "/platform/priceSetting/findByCoursePeriodId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
/** 实践感悟查询 */
export const assignmentDesignFindAll = params => {
  return http.request(
    "get",
    "/platform/assignmentDesign/findByCoursePeriodId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
/** 局端实践感悟查询 */
export const bureauAssignmentDesignFindAll = params => {
  return http.request(
    "get",
    "/educationBureau/courseIntroduction/findByCoursePeriodId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
/** 学生实践感悟查询 */
export const findStudentAssignment = params => {
  return http.request(
    "get",
    "/platform/assignmentDesign/findStudentAssignment",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
/** 学生实践感悟得分 */
export const findByStudentAssignmentId = params => {
  return http.request(
    "get",
    "/platform/assignmentGrading/findByStudentAssignmentId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
/** 学生情况查询 */
export const studentSituationFindAll = params => {
  return http.request(
    "get",
    "/platform/studentSituation/findAllByCoursePeriodId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
/** 上课跟踪查询 */
export const classTrackingFind = params => {
  return http.request(
    "get",
    "/platform/classTracking/findByCoursePeriodId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
/** 课程报告查询 */
export const courseReportFindAll = params => {
  return http.request(
    "get",
    "/platform/courseReport/findByCoursePeriodId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
/** 用户评价查询分页 */
export const commentsFindAll = params => {
  return http.request(
    "get",
    "/platform/comments/findAll",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
/** 用户评价id查询 */
export const commentsFindId = params => {
  return http.request(
    "get",
    "/platform/comments/findById",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
/** 根据课期id和学生Id查询 */
export const findByCoursePeriodIdAndStudentId = params => {
  return http.request(
    "get",
    "/platform/comments/findByCoursePeriodIdAndStudentId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 设置是否公开
export const upOnlyMe = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/comments/upOnlyMe",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
/** 分页查询课期审核 */
export const findReviewAll = params => {
  return http.request(
    "get",
    "/platform/apply/findReview",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 课期审核
export const reviewApproval = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/apply/review",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
// 根据id查询课期审核详情
export const findByApplyId = params => {
  return http.request(
    "get",
    "/platform/apply/findByApplyId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 根据id查询课期详情
export const findcoursePeriodId = params => {
  return http.request(
    "get",
    "/platform/coursePeriod/findById",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 查询课期审核基础信息
export const findBasicInformation = params => {
  return http.request(
    "get",
    "/platform/coursePeriod/findCoursePeriodIdByBasicInformation",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 局端查询课期审核基础信息
export const bureauFindBasicInformation = params => {
  return http.request(
    "get",
    "/educationBureau/CoursePeriod/findCoursePeriodIdByBasicInformation",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 子订单退款
export const refundSub = params => {
  return http.request(
    "get",
    "/platform/orders/refundSub",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 多级分类
export const findAllCourseType = params => {
  return http.request(
    "get",
    "/platform/courseType/findAllCourseType",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 根据学生id子订单退款
export const refundSubByStudent = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/orders/refundSubByStudent",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ORDER_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
// 课期上架
export const coursePeriodOnline = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/coursePeriod/online",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
// 课期下架
export const coursePeriodOffline = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/coursePeriod/offline",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
// 关闭团单
export const platformCloseGroupOrder = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/coursePeriod/platformCloseGroupOrder",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};

// 转到局端审核
export const toTheLocalEnd = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/apply/transmit",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
// 获取购买材料费人数
export const getBuyMaterialCount = params => {
  return http.request(
    "get",
    "/platform/coursePeriod/getBuyMaterialCount",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
//改期审核
export const postponeReview = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/apply/postponeReview",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
