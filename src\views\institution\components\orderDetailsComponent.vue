<script setup>
import {
  confirmRefund,
  findByOrdersId,
  getOrderDetails,
  refund,
  confirmMainOrderRefund,
  confirmSubOrderRefund,
  rejectMainOrderRefund,
  rejectSubOrderRefund
} from "@/api/orderManagement";
import { formatTime } from "@/utils/index";
import { decrypt } from "@/utils/SM4.js";
import {
  Hide,
  View,
  Warning,
  ArrowDown,
  ArrowUp
} from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { computed, onMounted, ref, nextTick, onActivated } from "vue";
import { useRoute, useRouter } from "vue-router";
import { findcoursePeriodId } from "@/api/course.js";
import { studentFindById } from "@/api/studentManage.js";
import { requestTo } from "@/utils/http/tool";
import dayjs from "dayjs";
import FileItem from "@/components/PreviewV2/FileItem.vue";
import { AUDIT_ENUM, COURSE_PERIOD_ENUM } from "@/utils/enum.js";
import { ImageThumbnail } from "@/utils/imageProxy";
import RefundRejectDialog from "@/views/institution/components/refundRejectDialog.vue";
import MediaViewer from "@/components/Base/previewImgVideo.vue";
const props = defineProps({
  adminId: {
    default: ""
  },
  styleTyle: {
    type: Boolean,
    default: false
  },
  ordersDataJson: {
    type: String,
    default: ""
  }
});
const router = useRouter();
const route = useRoute();
const form = ref({});

const formRef = ref(null);
const richFlag = ref(false);
const adminId = ref(null); //订单id
const styleTyle = ref(false);
const ordersDataJson = ref(""); //财务数据
const isRefundButtonDisabled = ref(false); // 新增：控制全部退单按钮的禁用状态
const disabledButtonIds = ref(new Set()); // 存储已禁用按钮的子订单ID

// 新增：控制展开/收起的状态
const isCollapsed = ref(false);

// 新增：切换展开/收起的方法
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);
};
onMounted(() => {
  adminId.value = props?.adminId;
  if (props?.styleTyle === true) {
    styleTyle.value = props?.styleTyle;
  }
  if (route.query.type === "course") {
    styleTyle.value = true;
    getCoursePeriodFind();
  }
  if (route.query.type === "order") {
    styleTyle.value = true;
    getstudentFind();
    getCoursePeriodFindVA();
  }
  ordersDataJson.value = props?.ordersDataJson;
  // ? JSON.parse(props?.ordersDataJson)
  // : "";
  // console.log("🍪-----props.adminId-----", props.adminId);
  getData();
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);
  richFlag.value = true;
});

// 计算表格高度
const infoHeight = ref(0);
const curseHeight = ref(0);
const tableHeight = ref("calc(100vh - 207px)");
const calculateTableHeight = async val => {
  await nextTick();
  const searchForm = document.querySelector(".info");
  const tableForm = document.querySelector(".curse-table1");
  setTimeout(() => {}, 2000);
  if (searchForm || tableForm) {
    infoHeight.value = searchForm?.offsetHeight || 0;
    curseHeight.value = tableForm?.offsetHeight || 0;
    // 动态计算表格高度，减去表单高度、页面其他元素高度和边距
    if (val === "1") {
      tableHeight.value = `calc(100vh - 246px - ${infoHeight.value}px - ${curseHeight.value}px)`;
    } else {
      tableHeight.value = `calc(100vh - 226px - ${infoHeight.value}px - ${curseHeight.value}px)`;
    }
    console.log("🎁-----tableHeight.value-----", tableHeight.value);
  }
};

const formFile = ref([]);
const judgeTime = () => {
  switch (form.value.ordersStatus) {
    case "PAY":
      return "";
      break;
    case "REFUND":
      return "退单时间";
      break;
    case "CANCELLED":
      return "取消时间";
      break;
    default:
      return "完成时间";
      break;
  }
};
const judgestead = val => {
  console.log("🍧-----val-----", val);
  switch (val) {
    case "UNPAID":
      return "未支付";
      break;
    case "PAY":
      return "支付中";
      break;
    case "PAID":
      return "已支付";
      break;
    case "PAY_FAIL":
      return "支付失败";
      break;
    case "APPLICATION":
      return "申请退款";
      break;
    case "REFUND_REJECT":
      return "退款驳回";
      break;
    case "REFUND_FAILED":
      return "退款失败";
      break;
    case "PARTIAL_CANCEL":
      return "部分取消";
      break;
    case "REFUNDING":
      return "退款中"; //1
      break;
    case "REFUNDED":
      return "已退款"; //1
      break;
    case "PARTIALLY_REFUNDED":
      return "部分退款";
      break;
    case "PARTIAL_REFUND":
      return "部分退款";
      break;
    case "CANCELLED":
      return "已取消"; //1
      break;
    case "COMPLETED":
      return "已完成";
      break;
  }
};
const refundType = val => {
  console.log("🍧-----val-----", val);
  switch (val) {
    case "APPLICATION":
      return "申请退款";
      break;
    case "APPROVED":
      return "已批准";
      break;
    case "REFUNDING":
      return "退款中"; //1
      break;
    case "REFUNDED":
      return "已退款"; //1
      break;
    case "COMPLETED":
      return "已完成"; //1
      break;
    case "REJECTED":
      return "退款驳回";
      break;
    case "REFUND_REJECT":
      return "退款驳回";
      break;
    case "REFUND_FAILED":
      return "退款失败"; //1
      break;
    case "PARTIAL_REFUND":
      return "部分退款";
      break;
    case "PARTIAL_CANCEL":
      return "部分取消";
      break;
  }
};
const steadArr = ["已支付", "部分退款", "已完成"];
const steadArrv2 = ["已支付", "已完成"];
// 退单功能相关变量（参考机构端）
const steadArrForRefund = ["已支付", "已完成"];

// 辅助函数：检查退款状态是否为申请退款（兼容中英文）
const isRefundApplication = status => {
  return status === "申请退款" || status === "APPLICATION";
};

// 辅助函数：检查退款状态是否允许退单操作
const canRefund = status => {
  const allowedStatuses = [
    null,
    "申请退款",
    "APPLICATION",
    "退款驳回",
    "REFUND_REJECT",
    "退款失败",
    "REFUND_FAILED",
    "部分取消",
    "PARTIAL_CANCEL",
    "取消",
    "CANCEL"
  ];
  return allowedStatuses.includes(status);
};
const refunArr = [
  "REFUND_REJECT",
  "REFUND_FAILED",
  "PARTIAL_CANCEL",
  // "PARTIAL_REFUND",
  "CANCEL"
];
const refunArrV2 = [
  "REFUND_REJECT",
  "REFUND_FAILED",
  "PARTIAL_CANCEL",
  "PARTIAL_REFUND",
  "CANCEL"
];
const orderType = row => {
  let type = {
    WECHAT: "微信支付",
    ALPAY: "支付宝支付",
    PLATFORM: "平台",
    PUBLIC_ACCOUNT: "公账"
  };
  return type[row];
};
const displayValue = computed(() => {
  return (item, data) => {
    if (item.prop === "createdAt") {
      return formatTime(data[item.prop], "YYYY-MM-DD HH:mm:ss");
    } else if (item.prop === "channelType") {
      return orderType(data[item.prop]);
    } else if (item.prop === "recordType") {
      return data[item.prop] === "INCOME" ? "收入" : "支出";
    } else {
      return data[item.prop] || "--";
    }
  };
});
const formData = ref([
  {
    label: "订单号",
    type: "text",
    prop: "orderNo",
    // check: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "创建时间",
    type: "text",
    // check: true,
    prop: "createdAt",
    placeholder: "",
    width: "200px"
  },
  {
    label: "订单状态",
    type: "text",
    prop: "orderStatus",
    // check: true,
    placeholder: "",
    width: "200px",
    span: 2
  },
  {
    label: "付款时间",
    type: "text",
    prop: "payTime",
    // check: true,
    placeholder: "",
    width: "200px"
  },
  {
    // label: "完成时间",
    label: judgeTime(),
    type: "text",
    prop: "finishTime",
    // check: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "课程名",
    type: "text",
    prop: "courseName",
    // check: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "课程期号",
    type: "text",
    prop: "termNumber",
    // check: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "机构",
    type: "text",
    prop: "organizationName",
    // check: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "机构客服热线",
    type: "text",
    prop: "organizationServicePhone",
    // check: true,
    // isEye: true,
    isView: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "机构管理员",
    type: "text",
    prop: "organizationManager",
    // check: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "管理员电话",
    type: "text",
    prop: "organizationManagerPhone",
    // check: true,
    isEye: true,
    isView: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "购买人",
    type: "text",
    prop: "buyer",
    // check: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "购买人电话",
    type: "text",
    prop: "buyerPhone",
    // check: true,
    isEye: true,
    isView: true,
    placeholder: "",
    width: "200px"
  },

  {
    label: "购买价格",
    type: "text",
    prop: "totalPrice",
    // check: true,
    placeholder: "",
    width: "200px",
    span: 2
  },
  {
    label: "订单附带文件",
    type: "upload",
    // check: true,
    prop: "orderFile",
    placeholder: "",
    width: "200px",
    span: 2
  }
]);

const formfootData = ref([
  {
    label: "子订单号",
    type: "text",
    prop: "id",
    // check: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "学生",
    type: "text",
    prop: "studentName",
    // check: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "证件类型",
    type: "text",
    prop: "idType",
    // check: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "证件号",
    type: "text",
    prop: "idNumber",
    isEye: true,
    isView: true,
    // check: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "费用明细",
    type: "text",
    prop: "feeDetails",
    // check: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "购买金额",
    type: "text",
    prop: "totalFeeAmount",
    // check: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "退款状态",
    type: "text",
    prop: "refundStatus",
    // check: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "退款金额",
    type: "text",
    prop: "price",
    // check: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "退款理由",
    type: "text",
    prop: "refundReason",
    // check: true,
    placeholder: "",
    width: "200px",
    // 只有当退款理由存在且不为空且不为"null"字符串时才显示该字段
    showCondition: row =>
      row?.refundReason &&
      row.refundReason.trim() !== "" &&
      row.refundReason.trim().toLowerCase() !== "null"
  }
  // {
  //   label: "订单附带文件",
  //   type: "upload",
  //   // check: true,
  //   prop: "orderFile",
  //   placeholder: "",
  //   width: "200px"
  // }
]);
const formDataV2 = ref([
  {
    label: "编号",
    type: "text",
    prop: "id",
    // check: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "类型",
    type: "text",
    prop: "recordType",
    // check: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "支付渠道",
    type: "text",
    prop: "channelType",
    // check: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "备注",
    type: "text",
    prop: "remarks",
    // check: true,
    placeholder: "",
    width: "200px"
  }
]);
// 新增：计算属性，获取用于单行显示的表头 (前4项)
const firstRowHeaders = computed(() => tableHeader.value.slice(0, 4));
const newData = ref();
// 课程表头
const tableHeader = ref([
  {
    id: "1",
    label: "期号",
    value: "0",
    width: "107px"
  },
  {
    id: "2",
    label: "机构",
    value: "--",
    width: "107px"
  },
  {
    id: "3",
    label: "课期ID",
    value: "--",
    width: "107px"
  },
  {
    id: "4",
    label: "创建时间",
    value: "--",
    width: "107px"
  },
  {
    id: "5",
    label: "领队",
    value: "--",
    width: "107px"
  },
  {
    id: "6",
    label: "讲师",
    value: "--",
    width: "107px"
  },
  {
    id: "7",
    label: "开课时间",
    value: "--",
    width: "107px"
  },
  {
    id: "8",
    label: "截止报名",
    value: "--",
    width: "107px"
  },
  {
    id: "9",
    label: "实践点",
    value: "--",
    width: "107px"
  },
  {
    id: "10",
    label: "购买类型",
    value: "--",
    width: "107px"
  },
  {
    id: "11",
    label: "课期状态",
    value: "--",
    width: "107px",
    state: ""
  },
  {
    id: "12",
    label: "审核状态",
    value: "--",
    width: "107px",
    state: "",
    opinion: ""
  }
]);
// 实践感悟评价情况表头
const workHeader = ref([
  {
    id: "1",
    label: "学生ID",
    value: "--",
    width: "107px"
  },
  {
    id: "2",
    label: "学生姓名",
    value: "--",
    width: "107px"
  },
  {
    id: "3",
    label: "创建时间",
    value: "--",
    width: "107px"
  },
  {
    id: "4",
    label: "学校",
    value: "--",
    width: "107px"
  },
  {
    id: "5",
    label: "证件类型",
    value: "--",
    width: "107px"
  },
  {
    id: "6",
    label: "证件号",
    prop: "idNumber",
    value: "--",
    width: "107px"
  },
  {
    id: "7",
    label: "家长",
    value: "--",
    width: "107px"
  },
  {
    id: "8",
    label: "课程名",
    value: "--",
    width: "107px"
  },
  {
    id: "9",
    label: "上课时间",
    value: "--",
    width: "107px"
  }
]);
const url = ref();
const srcList = ref([]);
const coursePeriodName = ref("");
// 查询课程课期详情
const getCoursePeriodFind = async () => {
  let [err, res] = await requestTo(
    findcoursePeriodId({ id: route.query.periodId })
  );
  if (res) {
    // console.log("🐬-----res1111-33----", res);
    coursePeriodName.value = res.name || "--";
    tableHeader.value[0].value = res.termNumber || "0";
    tableHeader.value[1].value = res.organization?.name || "--";
    tableHeader.value[2].value = res.id || "--";
    tableHeader.value[3].value =
      dayjs(res.createdAt).format("YYYY-MM-DD HH:mm:ss") || "--";
    tableHeader.value[9].value = res.courseType?.name || "--";
    tableHeader.value[7].value =
      dayjs(res.signUpDeadline).format("YYYY-MM-DD HH:mm:ss") || "--";
    tableHeader.value[8].value = res.complex?.name || "--";
    tableHeader.value[6].value =
      dayjs(res.openTime).format("YYYY-MM-DD HH:mm:ss") || "--";
    if (res.leaders?.length) {
      let learderList = [];
      res.leaders.map(item => {
        learderList.push(item.name);
      });
      tableHeader.value[4].value = learderList.join(" 、") || "--";
    }
    if (res.lecturers?.length) {
      let lecturersList = [];
      res.lecturers.map(item => {
        lecturersList.push(item.name);
      });
      tableHeader.value[5].value = lecturersList.join(" 、") || "--";
    }
    tableHeader.value[10].value =
      COURSE_PERIOD_ENUM[res.coursePeriodState]?.label || "--";
    tableHeader.value[10].state = res.offlineType || "--";
    tableHeader.value[11].value = AUDIT_ENUM[res.reviewState]?.label || "无";
    tableHeader.value[11].opinion = res.opinion || "--";
    tableHeader.value[11].state = res.reviewState || "--";
    if (res.cover?.length) {
      res.cover.map(item => {
        fileList.value.push({ url: item.uploadFile.url, type: "image" });
      });
      // url.value = res.cover[0]?.uploadFile?.url;
    }
    if (res.coursePeriodVideos?.length) {
      res.coursePeriodVideos.map(item => {
        fileList.value.unshift({
          url: item.video.uploadFile.url,
          type: "video",
          poster: item.cover.uploadFile.url
        });
      });
    }
    await nextTick();
    calculateTableHeight();
  } else {
    console.log("🐳-----err-----", err);
  }
};

const studentIdNumberCt = ref();
// 查询(家长的学生)详情
const getstudentFind = async () => {
  let [err, res] = await requestTo(
    studentFindById({ id: Number(route.query.studentId) })
  );
  // console.log("🐬-----res333-----", res);
  if (res) {
    workHeader.value[0].value = res.id || "--";
    workHeader.value[1].value = res.name || "--";
    workHeader.value[2].value =
      dayjs(res.createdAt).format("YYYY-MM-DD HH:mm:ss") || "--";
    workHeader.value[3].value = res.school || "--";
    workHeader.value[4].value = res.idType || "--";
    workHeader.value[5].value = res.idNumber || "--";
    // workHeader.value[5].value = decrypt(res?.idNumberCt) || "--";
    workHeader.value[6].value =
      res.parentDTOS?.map(it => it.name).join("、") || "--";
    studentIdNumberCt.value = decrypt(res?.idNumberCt) || "--";
    await nextTick();
    calculateTableHeight("1");
  } else {
    console.log("🐳-----err-----", err);
  }
  if (err) {
    ElMessage.error(err.msg);
  }
};
const getCoursePeriodFindVA = async () => {
  let [err, res] = await requestTo(
    findcoursePeriodId({ id: route.query.coursePeriodId })
  );
  if (res) {
    // console.log('🍧-----res-----', res);
    workHeader.value[7].value = res.name || "--";
    workHeader.value[8].value =
      dayjs(res.openTime).format("YYYY-MM-DD HH:mm:ss") || "--";
  }
};
const iconteyp = ref(false);
// 家长的学生手机号
function imgAdd(val) {
  iconteyp.value = !iconteyp.value;
}

// 根据id查询
const getData = async () => {
  let params = { id: adminId.value };
  console.log("🎉-----params>>>>>>>>>>>>>>>-----", params);
  // return
  try {
    const { code, data, msg } = await getOrderDetails(params);
    console.log("🎁-----data-----", code, data, msg);
    if (code == 200 && data) {
      //判断是否为团单
      if (data.groupBuying) {
        formData.value.unshift(
          {
            label: "团购ID",
            type: "text",
            prop: "promoterId",
            // check: true,
            placeholder: "",
            width: "200px"
          },
          {
            label: "团购发起人",
            type: "text",
            prop: "promoter",
            // check: true,
            placeholder: "",
            width: "200px"
          }
        );
      }
      form.value = data;
      form.value.orderStatus = judgestead(data.orderStatus);
      // 处理主订单的退款状态
      if (data.refundStatus) {
        form.value.refundStatus = refundType(data.refundStatus);
      }
      if (form.value.subOrdersDetails.length > 0) {
        form.value.subOrdersDetails.forEach(element => {
          element.orderStatus = judgestead(element.orderStatus);
          // 处理子订单的退款状态
          if (element.refundStatus) {
            element.refundStatus = refundType(element.refundStatus);
          }
          // 处理退款理由
          if (
            element.refund &&
            element.refund.reason &&
            element.refund.reason.trim() !== ""
          ) {
            element.refundReason = element.refund.reason;
          }
          // 处理费用明细
          element.feeDetails = formatFeeDetails(element.items);
          // 计算购买金额
          element.totalFeeAmount = calculateTotalFeeAmount(element.items);
          element.isView = true;
        });
      }
      // formFile.value = data.files;
      newData.value = JSON.parse(JSON.stringify(data));
    }
  } catch (error) {
    console.log("catch-----error-----", error);
  }
};
//查看关联课程
const cancel = () => {
  if (route.query.title === "institution") {
    // 机构-财务 跳转关联课程
    router.push({
      path: "/institution/management/current",
      query: {
        periodId: form.value.coursePeriodId,
        text: "course"
      }
    });
  } else if (route.query.title === "parentManagement") {
    // 家长-订单 跳转关联课程
    router.push({
      path: "/parentManagement/management/current",
      query: {
        periodId: form.value.coursePeriodId,
        text: "course"
      }
    });
  } else {
    router.push({
      path: "/course/management/current",
      query: {
        periodId: form.value.coursePeriodId,
        text: "course"
      }
    });
  }
};
const edit = () => {
  router.push({
    path: "/institution/baseEdit",
    query: { id: adminId.value }
  });
};

const isViewFn = (val, type, item) => {
  // console.log('🎁-----val-----', val);
  if (type) {
    form.value.subOrdersDetails[val].isView =
      !form.value.subOrdersDetails[val].isView;
    if (form.value.subOrdersDetails[val].isView) {
      form.value.subOrdersDetails[val][item] =
        newData.value.subOrdersDetails[val][item];
    } else {
      let v = item + "Ct";
      form.value.subOrdersDetails[val][item] = decrypt(
        newData.value.subOrdersDetails[val][v]
      );
    }
    // console.log('🐬----- form.value.subOrdersDetails-----', form.value.subOrdersDetails);
  } else {
    formData.value[val].isView = !formData.value[val].isView;
    if (formData.value[val].isView) {
      form.value[item] = newData.value[item];
    } else {
      let v = item + "Ct";
      form.value[item] = decrypt(newData.value[v]);
    }
  }
};
//文件处理
// const fileData = () => {
//   //机构营业执照
//   let institutionLicense = formFile.value.institutionLicense;
//   //资质文件
//   let qualificationDocuments = formFile.value.qualificationDocuments;
//   if (institutionLicense.length > 0) {
//     setFilesFn(institutionLicense, "BUSINESS_LICENSE");
//   }
//   if (qualificationDocuments.length > 0) {
//     setFilesFn(qualificationDocuments, "QUALIFICATION_DOCUMENT");
//   }
// };
// const setFilesFn = (val, type) => {
//   for (let index = 0; index < val.length; index++) {
//     const element = val[index].fileIdentifier;
//     console.log("🌵-----element-----", element);
//     form.value.files.push({
//       fileType: type,
//       fileIdentifier: element,
//       sortOrder: index + 1
//     });
//   }
// };
// 文件上传
// const beforeUpload = async (file, item) => {
//   console.log("🦄-----item-----", item);
//   console.log("💗beforeUpload---------->", file);
//   // let fileName = file.name.split(".");
//   // let fileStyle = ["ppt", "pptx"];
//   // if (!fileStyle.includes(fileName[1])) {
//   //   ElMessage.error("请上传ppt文件");
//   //   return;
//   // }
//   // console.log("💗beforeUpload---------->33333", file);
//   let { code, data } = await uploadFile(file);
//   if (code === 200) {
//     // console.log("🌳-----data-----", data);
//     form.value[item].push(data);
//     console.log("🎁----- form.fileData-----", form.value[item]);
//   }
// };
//删除文件
const handleClickDetele = (item, index) => {
  formFile.value[item].splice(index, 1);
};

// 新增：团购订单退款审核相关方法
const isSubmittingGroupBuying = ref(false);
const disabledGroupBuyingButtonIds = ref(new Set());

// 退款驳回弹窗相关
const refundRejectDialogVisible = ref(false);
const currentRejectOrderId = ref(null);
const currentRejectType = ref(""); // 'main' 表示主订单，'sub' 表示子订单

// 批量处理弹窗相关
const batchProcessDialogVisible = ref(false);

// 团购订单确认退款
const handleGroupBuyingConfirmRefund = async (orderId, type) => {
  if (isSubmittingGroupBuying.value) return;

  try {
    await ElMessageBox.confirm(
      `你确定要确认${type === "main" ? "主订单" : "子订单"}退款吗?`,
      "确认退款",
      {
        confirmButtonText: "确认退款",
        cancelButtonText: "取消",
        type: ""
      }
    );

    isSubmittingGroupBuying.value = true;
    disabledGroupBuyingButtonIds.value.add(orderId);

    try {
      await groupBuyingRefundApi(orderId, type, "confirm");
    } finally {
      setTimeout(() => {
        isSubmittingGroupBuying.value = false;
      }, 1000);
    }
  } catch (error) {
    // 用户取消操作
  }
};

// 团购订单驳回退款
const handleGroupBuyingRejectRefund = async (orderId, type) => {
  if (isSubmittingGroupBuying.value) return;

  // 显示驳回理由弹窗
  currentRejectOrderId.value = orderId;
  currentRejectType.value = type;
  refundRejectDialogVisible.value = true;
};

// 处理退款驳回弹窗确认
const handleRefundRejectConfirm = async reason => {
  if (isSubmittingGroupBuying.value) return;

  isSubmittingGroupBuying.value = true;
  disabledGroupBuyingButtonIds.value.add(currentRejectOrderId.value);

  try {
    await groupBuyingRefundApi(
      currentRejectOrderId.value,
      currentRejectType.value,
      "reject",
      reason
    );
  } finally {
    setTimeout(() => {
      isSubmittingGroupBuying.value = false;
    }, 1000);
    refundRejectDialogVisible.value = false;
    currentRejectOrderId.value = null;
    currentRejectType.value = "";
  }
};

// 处理退款驳回弹窗重置
const handleRefundRejectReset = () => {
  refundRejectDialogVisible.value = false;
  currentRejectOrderId.value = null;
  currentRejectType.value = "";
};

// 批量处理相关方法
const openBatchProcessDialog = () => {
  batchProcessDialogVisible.value = true;
};

const closeBatchProcessDialog = () => {
  batchProcessDialogVisible.value = false;
};

// 批量确认主订单退款
const handleBatchConfirmRefund = async () => {
  if (isSubmittingGroupBuying.value) return;

  // 直接触发，无需二次确认（原二次确认代码保留但不再使用）
  isSubmittingGroupBuying.value = true;
  closeBatchProcessDialog();

  try {
    await groupBuyingRefundApi(form.value.id, "main", "confirm");
  } finally {
    setTimeout(() => {
      isSubmittingGroupBuying.value = false;
    }, 1000);
  }

  // 以下为原二次确认逻辑，按需保留但不再调用
  // try {
  //   await ElMessageBox.confirm("你确定要确认全部退款吗?", "确认全部退款", {
  //     confirmButtonText: "确认退款",
  //     cancelButtonText: "我再想想",
  //     type: ""
  //   });
  // } catch (error) {
  //   // 用户取消操作，不做任何处理
  // }
};

// 批量驳回主订单退款
const handleBatchRejectRefund = async () => {
  if (isSubmittingGroupBuying.value) return;

  // 关闭批量处理弹窗，打开驳回理由弹窗
  closeBatchProcessDialog();
  currentRejectOrderId.value = form.value.id;
  currentRejectType.value = "main";
  refundRejectDialogVisible.value = true;
};

// 团购订单退款API调用
const groupBuyingRefundApi = async (orderId, type, action, reason = "") => {
  const params = { id: orderId };

  // 如果是驳回操作且有理由，添加reason参数
  if (action === "reject" && reason) {
    params.reason = reason;
  }

  const operateLog = {
    operateLogType: "ORDER_MANAGEMENT",
    operateType: `${action === "confirm" ? "确认" : "驳回"}了${type === "main" ? "主订单" : "子订单"}退款`,
    operatorTarget: form.value.courseName
  };

  let api = null;
  if (type === "main" && action === "confirm") {
    api = confirmMainOrderRefund;
  } else if (type === "sub" && action === "confirm") {
    api = confirmSubOrderRefund;
  } else if (type === "main" && action === "reject") {
    api = rejectMainOrderRefund;
  } else if (type === "sub" && action === "reject") {
    api = rejectSubOrderRefund;
  }

  try {
    const { code } = await api(params, operateLog);
    if (code === 200) {
      // 操作成功后，从禁用列表中移除对应的ID
      disabledGroupBuyingButtonIds.value.delete(orderId);
      ElMessage({
        type: "success",
        message: `${action === "confirm" ? "确认" : "驳回"}退款成功`
      });
      getData();
    } else {
      // 操作失败时，也从禁用列表中移除对应的ID，允许重试
      disabledGroupBuyingButtonIds.value.delete(orderId);
      ElMessage({
        type: "error",
        message: `${action === "confirm" ? "确认" : "驳回"}退款失败`
      });
    }
  } catch (error) {
    // 发生异常时，也从禁用列表中移除对应的ID，允许重试
    disabledGroupBuyingButtonIds.value.delete(orderId);
    console.error("团购订单退款API调用出错:", error);
    ElMessage.error("操作失败");
  }
};
const Freeze = async (row, type = "") => {
  console.log("确认退单");
  ElMessageBox.confirm("你确定要退单吗?", "确认退单", {
    confirmButtonText: "确认退单",
    cancelButtonText: "我再想想",
    type: ""
  })
    .then(() => {
      isChargebackApi(row, type);
      disabledButtonIds.value.add(row);
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "已取消"
      });
    });
};
const isChargebackApi = async (row, bool) => {
  let params = {
    id: row
  };
  console.log("🦄-----params-----", params);
  let api = null;
  if (bool === "全部") {
    api = refund;
  } else {
    api = confirmRefund;
  }
  // console.log('🌵-----api-----', api);
  // return
  const operateLog = {
    operateLogType: "ORDER_MANAGEMENT",
    // operateType: `${typeName}了订单`,
    operateType: "退单了",
    operatorTarget: `"${form.value.courseName}"`
  };
  if (api === confirmRefund) {
    params = { subOrderId: row };
  } else if (api === refund) {
    params = { orderId: row };
  } else {
    params = { id: row };
  }
  const { code } = await api(params, operateLog);
  if (code === 200) {
    // ElMessage({
    //   type: "success",
    //   // message: "退单成功"
    //   message: "系统处理中，请稍后查询确认"
    // });
    // 如果是全部退单并且成功，禁用全部退单按钮
    if (bool === "全部") {
      isRefundButtonDisabled.value = true;
    }
    ElMessageBox.alert("系统处理中，请稍后查询确认", "提示", {
      confirmButtonText: "确认"
    });
    getData();
  } else {
    ElMessage({
      type: "error",
      message: "退单失败"
    });
  }
};
const dialogTableVisible = ref(false);
const gridData = ref([]);
const getOrdersId = async () => {
  let params = { ordersId: form.value.id };
  try {
    const { code, data, msg } = await findByOrdersId(params);
    console.log("🎁-----data-----", code, data, msg);
    if (code == 200 && data) {
      gridData.value = data;
      gridData.value.forEach(e => {
        // 适配新接口：ordersType 替代 refundType
        e.refundType = e.ordersType === "ORDERS" ? "主订单" : "子订单";
        // 适配新接口：applyStatus 替代 refundStatus
        e.refundStatus = refundType(e.applyStatus);
      });
    }
  } catch (error) {
    console.log("catch-----error-----", error);
  }
};
function onCloseOnClickModalClick() {
  dialogTableVisible.value = true;
  getOrdersId();
}

//处理购买规格字符串
const buyGroupToString = str => {
  // console.log(str)
  try {
    // 尝试将字符串作为 JSON 解析
    return JSON.parse(str).join("+");
  } catch (error) {
    // 若解析失败，直接返回原字符串
    return str;
  }
};

// 处理费用明细字符串
const formatFeeDetails = items => {
  if (!items || !Array.isArray(items)) {
    return "--";
  }

  const feeTypeMap = {
    CLASS_HOUR: "课时费",
    INSURANCE: "保险费",
    MATERIAL: "材料费",
    SERVICE: "服务费"
  };

  const feeDetails = items.map(item => {
    const feeTypeName = feeTypeMap[item.feeType] || item.feeType;
    if (item.price !== item.originalPrice) {
      return `${feeTypeName}${item.price}元（原价${item.originalPrice}元）`;
    } else {
      return `${feeTypeName}${item.price}元`;
    }
  });

  return feeDetails.join("；") || "--";
};

// 计算购买金额总和
const calculateTotalFeeAmount = items => {
  if (!items || !Array.isArray(items)) {
    return 0;
  }

  return items.reduce((total, item) => {
    return total + (item.price || 0);
  }, 0);
};
const fileList = ref([]);
const previewState = ref({
  isShow: false,
  index: 0,
  srcList: []
});

async function openViewer(index) {
  mediaLoading.value = true;

  // 确保数据已经加载完成
  if (fileList.value && fileList.value.length > 0) {
    // 检查视频资源是否已准备就绪
    const targetItem = fileList.value[index];
    if (targetItem.type.includes("video") && !targetItem.url) {
      ElMessage.warning("视频资源未准备就绪，请稍后重试");
      mediaLoading.value = false;
      return;
    }

    // 等待下一个 DOM 更新周期
    await nextTick();

    const srcListCopy = [...fileList.value];
    previewState.value.isShow = true;
    previewState.value.index = index;
    previewState.value.srcList = srcListCopy;
  } else {
    ElMessage.warning("媒体资源尚未加载完成，请稍后重试");
  }

  mediaLoading.value = false;
}

function closeViewer() {
  previewState.value.isShow = false;
  previewState.value.index = 0;
  previewState.value.srcList = [];
}
const displayMediaList = computed(() => {
  // 检查是否有视频
  const hasVideos = fileList.value.some(item => item.type.includes("video"));

  if (hasVideos) {
    // 如果有视频，只显示视频封面
    return fileList.value.filter(item => item.type.includes("video"));
  } else {
    // 如果没有视频，显示所有图片
    return fileList.value.filter(item => item.type.includes("image"));
  }
});

// 获取在完整fileList中的实际索引
const getActualIndex = displayIndex => {
  const displayedItems = displayMediaList.value;
  if (displayedItems.length > 0) {
    const displayedItem = displayedItems[displayIndex];
    return fileList.value.findIndex(
      item => item.url === displayedItem.url && item.type === displayedItem.type
    );
  }
  return 0;
};
</script>

<template>
  <div
    :class="{
      content: !styleTyle,
      course: route.query.type === 'order' || route.query.type === 'course'
    }"
  >
    <!-- 课程详情表格 -->
    <!-- 新增 info 容器，用于控制可折叠区域 -->
    <div
      v-if="route.query.type === 'course'"
      class="info"
      :class="{ 'info-collapsed': isCollapsed }"
    >
      <div v-show="!isCollapsed" class="curse-table">
        <div class="img">
          <el-tooltip
            class="box-item"
            title=""
            :content="coursePeriodName ? coursePeriodName : ''"
            placement="bottom"
            effect="light"
          >
            <div class="coursePeriodName">
              {{ coursePeriodName || "" }}
            </div>
          </el-tooltip>
          <!-- <el-image
            :src="ImageThumbnail(url, '115x')"
            :zoom-rate="1.2"
            :max-scale="7"
            :min-scale="0.2"
            :preview-src-list="srcList"
            :hide-on-click-modal="true"
            show-progress
            :initial-index="4"
            fit="cover"
            class="img-pic"
          /> -->
          <div class="img-pic">
            <div
              v-for="(item, index) in displayMediaList"
              :key="index"
              class="img-item"
              @click="openViewer(getActualIndex(index))"
            >
              <img
                v-if="item.type.includes('video')"
                :src="item.poster"
                class="video"
              >
              <img v-else :src="ImageThumbnail(item.url)" class="video">
            </div>
          </div>
        </div>
        <el-descriptions
          class="margin-top"
          title=""
          :column="4"
          border
          style="width: 1500px"
        >
          <template v-for="(item, index) in tableHeader" :key="index">
            <el-descriptions-item width="120px" label-align="center">
              <template #label>
                <div class="cell-item">{{ item.label }}</div>
              </template>
              <div
                v-if="
                  (item.label === '审核状态' &&
                    item.state === 'OFFLINE_REJECT') ||
                  item.state === 'ONLINE_REJECT'
                "
              >
                <el-tooltip
                  class="box-item"
                  title=""
                  :content="item.opinion ? item.opinion : '无'"
                  placement="bottom"
                  effect="light"
                >
                  <div class="warning">
                    {{ item.value }}
                    <el-icon style="color: red; margin-left: 3px">
                      <Warning />
                    </el-icon>
                  </div>
                </el-tooltip>
              </div>
              <div
                v-else-if="
                  (item.label === '课期状态' &&
                    item.state === 'PLATFORM_OFFLINE') ||
                  item.state === 'PLATFORM_CLOSE_GROUP'
                "
              >
                <el-tooltip
                  class="box-item"
                  title=""
                  :content="
                    item.state === 'PLATFORM_OFFLINE'
                      ? '课期已被强制下架，请联系平台客服了解情况'
                      : '课期已被强制取消定制，请联系平台客服了解情况'
                  "
                  placement="bottom"
                  effect="light"
                >
                  <div class="warning">
                    {{ item.value }}
                    <el-icon style="color: red; margin-left: 3px">
                      <Warning />
                    </el-icon>
                  </div>
                </el-tooltip>
              </div>
              <div v-else>
                {{ item.value }}
              </div>
            </el-descriptions-item>
          </template>
        </el-descriptions>
      </div>
      <!-- 新增：收起状态的布局 -->
      <div v-show="isCollapsed" class="curse-table collapsed-table">
        <el-descriptions
          class="margin-top first-row-only"
          title=""
          :column="4"
          border
          style="width: 100%"
        >
          <template v-for="(item, index) in firstRowHeaders" :key="index">
            <el-descriptions-item width="120px" label-align="center">
              <template #label>
                <div class="cell-item">{{ item.label }}</div>
              </template>
              <!-- 收缩时简化显示，不包含复杂状态的tooltip和icon -->
              <div>{{ item.value }}</div>
            </el-descriptions-item>
          </template>
        </el-descriptions>
      </div>
      <!-- 新增：收起/展开控制按钮 -->
      <div class="collapse-bar" @click="toggleCollapse">
        <div class="collapse-control">
          <el-icon class="collapse-icon">
            <ArrowDown v-if="isCollapsed" />
            <ArrowUp v-else />
          </el-icon>
        </div>
      </div>
    </div>
    <!-- 学生详情表格 -->
    <div v-if="route.query.type === 'order'" class="curse-table1">
      <el-descriptions
        class="margin-top"
        style="width: 100%"
        title=""
        :column="3"
        border
      >
        <template v-for="(item, index) in workHeader" :key="index">
          <el-descriptions-item width="120px" label-align="center">
            <template #label>
              <div class="cell-item">{{ item.label }}</div>
            </template>
            <div>
              <div v-if="item.prop === 'idNumber'" style="width: 180px">
                <span v-if="!iconteyp" style="margin-right: 10px">
                  {{ item.value }}
                </span>
                <span v-else style="margin-right: 10px">
                  {{ studentIdNumberCt }}
                </span>
                <el-icon
                  v-if="!iconteyp"
                  style="cursor: pointer"
                  @click="imgAdd"
                >
                  <Hide />
                </el-icon>
                <el-icon v-else style="cursor: pointer" @click="imgAdd">
                  <View />
                </el-icon>
              </div>
              <div v-else>
                {{ item.value }}
              </div>
            </div>
          </el-descriptions-item>
        </template>
      </el-descriptions>
    </div>

    <div
      :class="
        route.query.type === 'order' || route.query.type === 'course'
          ? 'other'
          : ''
      "
    >
      <el-scrollbar
        :height="
          route.query.type === 'order' || route.query.type === 'course'
            ? tableHeight
            : 'calc(100vh - 207px)'
        "
      >
        <el-descriptions
          v-if="ordersDataJson"
          title=""
          :column="2"
          border
          :label-width="'15%'"
        >
          <el-descriptions-item
            v-for="(item, index) in formDataV2"
            :key="index"
            label-align="center"
            label-class-name="my-label"
          >
            <template #label>
              {{ item.label }}
            </template>
            <el-form-item
              :prop="item.prop"
              :inline-message="item.check"
              style="margin-bottom: 0"
              :show-message="true"
              error-placement="right"
            >
              <!-- text -->
              <template v-if="item.type === 'text'">
                <div class="cell_item">
                  <div>
                    {{ displayValue(item, ordersDataJson) }}
                  </div>
                </div>
              </template>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
        <div v-if="ordersDataJson" class="box" />
        <el-descriptions title="" :column="2" border :label-width="'15%'">
          <el-descriptions-item
            v-for="(item, index) in formData"
            :key="index"
            label-align="center"
            label-class-name="my-label"
            :span="item.span || 1"
          >
            <template #label>
              {{ item.label }}
            </template>
            <el-form-item
              :prop="item.prop"
              :inline-message="item.check"
              style="margin-bottom: 0"
              :show-message="true"
              error-placement="right"
            >
              <!-- text -->
              <template v-if="item.type === 'text'">
                <div class="cell_item">
                  <div style="">
                    <span v-if="item.prop === 'totalPrice'">
                      <!-- 保留两位数小数 -->
                      {{
                        `￥${form[item.prop]?.toFixed(2) || "--"}（原价￥${form.originalPrice?.toFixed(2) || "--"}）`
                      }}
                    </span>
                    <span v-else>
                      {{
                        item.prop === "createdAt" ||
                        (item.prop === "payTime" && form[item.prop]) ||
                        (item.prop === "finishTime" && form[item.prop])
                          ? formatTime(form[item.prop], "YYYY-MM-DD HH:mm:ss")
                          : form[item.prop] || "--"
                      }}</span>
                  </div>
                  <span v-if="form[item.prop] && item.isEye" class="icon">
                    <el-icon
                      v-if="item.isView"
                      style="cursor: pointer"
                      @click="isViewFn(index, false, item.prop)"
                    >
                      <Hide />
                    </el-icon>
                    <el-icon
                      v-else
                      style="cursor: pointer"
                      @click="isViewFn(index, false, item.prop)"
                    >
                      <View />
                    </el-icon>
                  </span>
                </div>
              </template>
              <!-- 示例：上传组件 -->
              <template v-else-if="item.type === 'upload'">
                <!-- <el-upload
                  action="#"
                  :show-file-list="false"
                  class="upload-demo"
                  :http-request="() => {}"
                  :before-upload="file => beforeUpload(file, item.prop)"
                >
                  <img :src="uploadImg" alt="" /> -->
                <!-- <img v-if="" :src="uploadImg" alt="" />
                </el-upload> -->
                <span v-if="form?.files === null">{{ "--" }}</span>
                <template v-for="(item2, index2) in form.files" :key="index2">
                  <FileItem
                    :data="item2.uploadFile"
                    :index="index2"
                    @delete="getDeleted(item.prop, index2)"
                  />
                </template>
              </template>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
        <div v-for="(t, i) in form.subOrdersDetails" :key="i" class="subclass">
          <el-descriptions title="" :column="2" border :label-width="'15%'">
            <el-descriptions-item
              v-for="(item, index) in formfootData"
              v-show="!item.showCondition || item.showCondition(t)"
              :key="index"
              label-align="center"
              label-class-name="my-label"
            >
              <template #label>
                {{
                  item.prop === "price"
                    ? t.refundStatus
                      ? "退款金额"
                      : "金额"
                    : item.label
                }}
              </template>
              <el-form-item
                :prop="item.prop"
                :inline-message="item.check"
                style="margin-bottom: 0"
                :show-message="true"
                error-placement="right"
              >
                <!-- text -->
                <template v-if="item.type === 'text'">
                  <div class="cell_item">
                    <div
                      style="
                        display: flex;
                        justify-content: space-between;
                        width: 100%;
                      "
                    >
                      <div style="display: flex">
                        <span v-if="item.prop === 'price'">
                          <!-- 保留两位数小数 -->
                          {{ `￥${t[item.prop]?.toFixed(2) || "--"}` }}
                        </span>
                        <span v-else-if="item.prop === 'feeDetails'">
                          {{ t[item.prop] || "--" }}
                        </span>
                        <span v-else-if="item.prop === 'totalFeeAmount'">
                          <!-- 购买金额显示为货币格式 -->
                          {{ `￥${t[item.prop]?.toFixed(2) || "--"}` }}
                        </span>
                        <span v-else>
                          {{
                            item.prop === "createdAt"
                              ? formatTime(
                                  t[item.prop],
                                  "YYYY-MM-DD HH:mm:ss"
                                ) || "--"
                              : item.prop === "refundReason"
                                ? t[item.prop] &&
                                  t[item.prop].trim() !== "" &&
                                  t[item.prop].trim().toLowerCase() !== "null"
                                  ? t[item.prop].trim()
                                  : "--"
                                : t[item.prop] || "--"
                          }}
                        </span>
                        <span v-if="t[item.prop] && item.isEye" class="icon">
                          <el-icon
                            v-if="t.isView"
                            style="cursor: pointer"
                            @click="isViewFn(i, true, item.prop)"
                          >
                            <Hide />
                          </el-icon>
                          <el-icon
                            v-else
                            style="cursor: pointer"
                            @click="isViewFn(i, true, item.prop)"
                          >
                            <View />
                          </el-icon>
                        </span>
                      </div>
                      <!-- {{ form[item.prop] }}
                      {{ t.refundStatus }} -->
                      <!-- 按钮组：驳回退款、确认退款、退单 -->
                      <div
                        v-if="item.prop === 'refundStatus'"
                        class="refund-buttons"
                        style="margin-left: auto; display: flex"
                      >
                        <!-- 团购订单子订单退款审核按钮 -->
                        <el-button
                          v-if="
                            form.groupBuying &&
                            isRefundApplication(t.refundStatus)
                          "
                          type="danger"
                          :disabled="disabledGroupBuyingButtonIds.has(t.id)"
                          @click="handleGroupBuyingRejectRefund(t.id, 'sub')"
                        >
                          驳回退款
                        </el-button>
                        <el-button
                          v-if="
                            form.groupBuying &&
                            isRefundApplication(t.refundStatus)
                          "
                          type="primary"
                          :disabled="disabledGroupBuyingButtonIds.has(t.id)"
                          @click="handleGroupBuyingConfirmRefund(t.id, 'sub')"
                        >
                          确认退款
                        </el-button>
                        <!-- 普通退单按钮 -->
                        <el-button
                          v-if="
                            steadArrForRefund.includes(form.orderStatus) &&
                            canRefund(t.refundStatus)
                          "
                          type="primary"
                          plain
                          :disabled="disabledButtonIds.has(t.id)"
                          @click="Freeze(t.id)"
                        >
                          退单
                        </el-button>
                      </div>
                    </div>
                  </div>
                </template>
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-scrollbar>
      <div class="account_management">
        <el-button type="primary" @click="cancel">查看关联课程</el-button>
        <el-button type="primary" @click="onCloseOnClickModalClick">
          退单详情
        </el-button>
        <!-- 新增：批量处理主订单退款按钮 -->
        <el-button
          type="primary"
          :disabled="isSubmittingGroupBuying"
          @click="openBatchProcessDialog"
        >
          批量处理退单
        </el-button>
        <el-button
          v-if="
            steadArr.includes(form.orderStatus) && canRefund(form.refundStatus)
          "
          type="primary"
          plain
          :disabled="isRefundButtonDisabled"
          @click="Freeze(form.id, '全部')"
        >
          全部退单
        </el-button>
      </div>
      <el-dialog v-model="dialogTableVisible" title="" align-center width="800">
        <template #header>
          <div class="my-header" style="font-weight: 600">退单详情</div>
        </template>
        <el-table :data="gridData">
          <el-table-column
            property="id"
            label="编号"
            width="100"
            align="center"
          />
          <el-table-column
            property="ordersId"
            label="订单Id"
            width="100"
            align="center"
          />
          <el-table-column
            property="subOrdersId"
            label="子订单Id"
            align="center"
          />
          <el-table-column property="userName" label="用户名" align="center">
            <template #default="scope">
              {{ scope.row.userName || "--" }}
            </template>
          </el-table-column>
          <el-table-column
            property="refundPrice"
            label="退款金额"
            align="center"
          >
            <template #default="scope">
              {{ scope.row.refundPrice || "--" }}
            </template>
          </el-table-column>
          <el-table-column property="reason" label="退款原因" align="center">
            <template #default="scope">
              {{ scope.row.reason || "--" }}
            </template>
          </el-table-column>
          <el-table-column
            property="refundStatus"
            label="退款状态"
            align="center"
          >
            <template #default="scope">
              {{ scope.row.refundStatus || "--" }}
            </template>
          </el-table-column>
          <el-table-column
            property="refundType"
            label="退款类型"
            align="center"
          >
            <template #default="scope">
              {{ scope.row.refundType || "--" }}
            </template>
          </el-table-column>
        </el-table>
      </el-dialog>

      <!-- 退款驳回弹窗 -->
      <RefundRejectDialog
        v-model:dialogFormVisible="refundRejectDialogVisible"
        :title="
          currentRejectType === 'main' ? '驳回主订单退款' : '驳回子订单退款'
        "
        @confirm="handleRefundRejectConfirm"
        @reset="handleRefundRejectReset"
      />

      <!-- 批量处理弹窗 -->
      <el-dialog
        v-model="batchProcessDialogVisible"
        title="批量处理退单"
        width="400px"
        align-center
      >
        <div class="batch-process-content">
          <p style="margin-bottom: 20px; color: #666">
            确定要批量处理主订单退款申请吗？
          </p>
        </div>
        <template #footer>
          <div class="batch-dialog-footer">
            <el-button @click="closeBatchProcessDialog">取消</el-button>
            <el-button
              type="danger"
              :disabled="isSubmittingGroupBuying"
              @click="handleBatchRejectRefund"
            >
              全部驳回退单
            </el-button>
            <el-button
              type="primary"
              :disabled="isSubmittingGroupBuying"
              @click="handleBatchConfirmRefund"
            >
              全部确认退单
            </el-button>
          </div>
        </template>
      </el-dialog>
    </div>
    <MediaViewer
      v-if="previewState.isShow"
      :key="`media-viewer-${previewState.isShow}-${fileList.length}`"
      :z-index="9999"
      :initial-index="previewState.index"
      :url-list="previewState.srcList"
      :hide-on-click-modal="true"
      @close="closeViewer"
    />
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-descriptions__cell) {
  width: 34%;
}
.content {
  padding: 20px;
  background: #fff;
}
.course {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 100px);
  // padding: 20px;
  // background: #fff;
}
.other {
  padding: 20px;
  background: #fff;
  width: 100%;
  // flex: 1;
  // overflow: hidden;
  background-color: #fff;
  display: flex;
  flex-direction: column;
}
.curse-table {
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 20px 20px 0 20px;
  // height: 250px;
  // margin-bottom: 20px;
  // width: calc(100% - 48px);
  background-color: #fff;
  .img {
    width: 115px;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-right: 20px;
    .coursePeriodName {
      width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-bottom: 10px;
    }
    .img-pic {
      width: 115px;
      height: 88px;
      // height: 100%;
      // margin-left: 20px;
      // object-fit: cover;
      .img-item {
        width: 115px;
        height: 88px;
      }
      .video {
        width: 100%;
        height: 100%;
      }
    }
  }
}
.curse-table1 {
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 20px 20px 20px 20px;
  background-color: #fff;
  margin-bottom: 20px;
}
.header {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 20px;

  .title_left {
    display: flex;

    :nth-child(2) {
      margin-left: 40px;
    }
  }

  .title_rigth {
    display: flex;

    :nth-child(2) {
      margin-left: 40px;
    }
  }
}

.account_management {
  margin-top: 20px;
  box-sizing: border-box;
  display: flex;
  justify-content: flex-end;

  // justify-content: space-between;
  .el-button + .el-button {
    margin-left: 10px;
  }
}

:deep(.my-label) {
  background: #e1f5ff !important;
}
.cell_item {
  width: 100%;
  display: flex;
  .icon {
    display: flex;
    align-items: center;
    margin-left: 10px;
  }
}
.upload-demo {
  display: flex;
  align-items: center;
}
.fileOther {
  // margin-top: 26px;
  margin-left: 20px;
  display: flex;
  align-items: center;
  line-height: 22px;
  width: 420px;
  // margin-bottom: 56px;
  .title {
    color: #0e1832;
    font-family: Source Han Sans CN;
    font-size: 16px;
    font-weight: 400;
    height: 24px;
    white-space: nowrap;
  }
  .link {
    width: 20px;
    height: 20px;
    margin: 0 10px;
    cursor: pointer;
  }
  .fileName {
    color: #3876fd;
    font-family: Source Han Sans CN;
    font-size: 16px;
    font-weight: 400;
    height: 24px;
    cursor: pointer;
    // width: 350px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .linkDetele {
    width: 20px;
    height: 20px;
    margin-left: 10px;
    cursor: pointer;
  }
}
.subclass {
  margin-top: 20px;
}
.box {
  margin-bottom: 20px;
}
:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  background: #e1f5ff;
}
.info {
  background-color: #fff;
  box-sizing: border-box;
  padding: 20px 20px 0 20px;
  margin-bottom: 20px;
  transition: all 0.3s ease;
  overflow: hidden;
  flex-shrink: 0;

  &.info-collapsed {
    padding-top: 20px;
    padding-bottom: 0;
  }
}

.collapse-bar {
  height: 30px;
  border-radius: 0 0 4px 4px;
  width: calc(100% - 40px);
  margin: 0 auto;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  // box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;

  &:hover {
    background-color: #ecf5ff;
  }

  .collapse-control {
    display: flex;
    justify-content: center;
    align-items: center;

    .collapse-icon {
      color: #909399;
      font-size: 16px;
      transition: transform 0.3s;

      &:hover {
        color: #409eff;
      }
    }
  }
}

.batch-process-content {
  text-align: center;
  padding: 20px 0;
}

.batch-dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
