import { ref, onMounted, watch } from "vue";
// 表格数据
const tableData = ref([
  {
    name: "名称0",
    time: "2025-10-20",
    type: "类型",
    demand: "我是招募需求",
    status: ["已发布", "已申请", "已通过", "已拒绝"],
    nameProme: "发布人"
  },
  {
    name: "名称1",
    time: "2025-10-20",
    type: "类型",
    demand: "我是招募需求",
    status: ["已发布", "已申请", "已通过", "已拒绝"],
    nameProme: "发布人"
  },
  {
    name: "名称2",
    time: "2025-10-20",
    type: "类型",
    demand: "我是招募需求",
    status: ["已发布", "已申请", "已通过", "已拒绝"],
    nameProme: "发布人"
  },
  {
    name: "名称3",
    time: "2025-10-20",
    type: "类型",
    demand: "我是招募需求",
    status: ["已发布", "已申请", "已通过", "已拒绝"],
    nameProme: "发布人"
  },
  {
    name: "名称2",
    time: "2025-10-20",
    type: "类型",
    demand: "我是招募需求",
    status: ["已发布", "已申请", "已通过", "已拒绝"],
    nameProme: "发布人"
  },
  {
    name: "名称3",
    time: "2025-10-20",
    type: "类型",
    demand: "我是招募需求",
    status: ["已发布", "已申请", "已通过", "已拒绝"],
    nameProme: "发布人"
  }
]);

const params = ref({
  page: 1,
  size: 10,
  sort: "createdAt,desc",
  totalElements: 0
});

// 分页数据
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 10 // 假设总数为100，实际应该从API获取
});

// 分页变化处理函数
const handlePageChange = page => {
  pagination.value.currentPage = page;
  params.value.page = page;
  // 这里应该调用获取数据的函数，例如 getTableList();
};

// 页大小变化处理函数
const handleSizeChange = size => {
  pagination.value.pageSize = size;
  params.value.size = size;
  pagination.value.currentPage = 1;
  params.value.page = 1;
  // 这里应该调用获取数据的函数，例如 getTableList();
};

// 选项卡
const activeName = ref([
  {
    name: "机构发布",
    id: 0
  },
  {
    name: "师资发布",
    id: 1
  }
]);
// 机构发布
const Institutions = ref([
  {
    label: 0,
    name: "全部"
  },
  {
    label: 1,
    name: "已发布"
  },
  {
    label: 2,
    name: "收到申请"
  }
]);
// 师资发布
const Teachers = ref([
  {
    label: 0,
    name: "全部"
  },
  {
    label: 1,
    name: "已申请"
  }
]);
// 选项卡切换
const handleClick = e => {
  params.value.page = 1;
  if (e.props.label === "机构发布") {
    // paramsChild.value = Institutions.value;
  } else {
    // paramsChild.value = Teachers.value;
  }
};
export function MarketHook() {
  return {
    tableData,
    params,
    activeName,
    handleClick,
    pagination,
    handlePageChange,
    handleSizeChange
  };
}
