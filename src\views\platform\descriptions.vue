<script setup>
import { ref, reactive, onMounted } from "vue";
import {
  platformInfoFind,
  platformInfoSaveOrUpdate,
  platformInfoUpdate,
  platformInfoSave
} from "@/api/apiPlatform.js";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox } from "element-plus";
import { Plus } from "@element-plus/icons-vue";
import { uploadFile } from "@/utils/upload/upload.js";

const ruleFormRef = ref();
const buttonLoading = ref(false); // 按钮加载状态
const from = ref({
  id: "",
  platformName: "",
  platformIcon: "",
  platformLogo: "",
  platformLogoLight: "",
  platformLogoVertical: "",
  platformSlogan: "",
  customerServiceHotline: "",
  email: "",
  copyright: "",
  icpRecordNumber: "",
  securityNumber: "",
  securityNumberLink: "",
  icpRecordNumberLink: "",
  officialAvatar: "",
  officialName: ""
});

// 添加一个映射对象，用于存储fileIdentifier和url的对应关系
const fileIdentifierMap = ref({});

const formData = ref([
  {
    label: "平台图标",
    type: "upload",
    prop: "platformIcon",
    check: false,
    placeholder: "请上传平台图标",
    uploadTip:
      "支持上传png、jpg、jpeg、gif、bmp、webp图片格式，最佳尺寸：117*139px，单张大小不超过1MB"
  },
  {
    label: "平台LOGO(左右布局，深色背景)",
    type: "upload",
    prop: "platformLogo",
    check: false,
    placeholder: "请上传平台LOGO（左右布局，深色背景）",
    uploadTip:
      "支持上传png、jpg、jpeg、gif、bmp、webp图片格式，最佳尺寸：195*32px，单张大小不超过1MB"
  },
  {
    label: "平台LOGO(左右布局，浅色背景)",
    type: "upload",
    prop: "platformLogoLight",
    check: false,
    placeholder: "请上传平台LOGO（左右布局，浅色背景）",
    uploadTip:
      "支持上传png、jpg、jpeg、gif、bmp、webp图片格式，最佳尺寸：342*60px，单张大小不超过1MB"
  },
  {
    label: "平台LOGO(上下布局，深色背景)",
    type: "upload",
    prop: "platformLogoVertical",
    check: false,
    placeholder: "请上传平台LOGO（上下布局，深色背景）",
    uploadTip:
      "支持上传png、jpg、jpeg、gif、bmp、webp图片格式，最佳尺寸：132*75px，单张大小不超过1MB"
  },
  {
    label: "平台名称",
    type: "input",
    prop: "platformName",
    check: false,
    placeholder: "请输入平台名称"
  },
  {
    label: "平台SLOGAN",
    type: "input",
    prop: "platformSlogan",
    check: false,
    placeholder: "请输入平台SLOGAN"
  },
  {
    label: "平台客服热线",
    type: "input",
    prop: "customerServiceHotline",
    check: false,
    placeholder: "请输入平台客服热线"
  },
  {
    label: "平台邮箱",
    type: "input",
    prop: "email",
    check: false,
    placeholder: "请输入平台邮箱"
  },
  {
    label: "版权信息文字",
    type: "input",
    prop: "copyright",
    check: false,
    placeholder: "请输入版权信息文字"
  },
  {
    label: "ICP备案号",
    type: "input",
    prop: "icpRecordNumber",
    check: false,
    placeholder: "请输入ICP备案号"
  },
  {
    label: "ICP备案网站链接",
    type: "input",
    prop: "icpRecordNumberLink",
    check: false,
    placeholder: "请输入ICP备案网站链接"
  },
  {
    label: "公网安备信息",
    type: "input",
    prop: "securityNumber",
    check: false,
    placeholder: "请输入公网安备信息"
  },
  {
    label: "网安备案网站链接",
    type: "input",
    prop: "securityNumberLink",
    check: false,
    placeholder: "请输入网安备案网站链接"
  },
  {
    label: "发现官方号头像",
    type: "upload",
    prop: "officialAvatar",
    check: false,
    placeholder: "请上传发现官方号头像",
    uploadTip:
      "支持上传png、jpg、jpeg、gif、bmp、webp图片格式，最佳尺寸：132*75px，单张大小不超过1MB"
  },
  {
    label: "发现官方号名称",
    type: "input",
    prop: "officialName",
    check: false,
    placeholder: "请输入发现官方号名称"
  }
]);

const rules = reactive({
  platformName: [
    { required: false, message: "平台名称不能为空！", trigger: "blur" }
  ],
  platformSlogan: [
    { required: false, message: "平台SLOGAN不能为空！", trigger: "blur" }
  ],
  customerServiceHotline: [
    { required: false, message: "平台客服热线 不能为空！", trigger: "blur" }
  ],
  email: [{ required: false, message: "平台邮箱 不能为空！", trigger: "blur" }],
  copyright: [
    { required: false, message: "版权信息文字 不能为空！", trigger: "blur" }
  ],
  icpRecordNumber: [
    { required: false, message: "ICP备案号 不能为空！", trigger: "blur" }
  ],
  icpRecordNumberLink: [
    { required: false, message: "ICP备案网站链接 不能为空！", trigger: "blur" }
  ],
  securityNumber: [
    { required: false, message: "公网安备信息 不能为空！", trigger: "blur" }
  ],
  securityNumberLink: [
    { required: false, message: "网安备案网站链接 不能为空！", trigger: "blur" }
  ]
});

const removeEmptyValues = obj => {
  return Object.fromEntries(
    Object.entries(obj).filter(
      ([key, value]) =>
        value !== "" && !(Array.isArray(value) && value.length === 0)
    )
  );
};
function findDifferences(objA, objB) {
  const differences = {};
  // 遍历 objA 的属性
  for (const key in objA) {
    if (objA.hasOwnProperty(key)) {
      if (objB.hasOwnProperty(key)) {
        if (objA[key] !== objB[key]) {
          differences[key] = { oldValue: objA[key], newValue: objB[key] };
        }
      } else {
        differences[key] = { oldValue: objA[key], newValue: undefined };
      }
    }
  }
  // 检查 objB 中是否有 objA 没有的属性
  for (const key in objB) {
    if (objB.hasOwnProperty(key) && !objA.hasOwnProperty(key)) {
      differences[key] = { oldValue: undefined, newValue: objB[key] };
    }
  }
  return differences;
}

function find(val) {
  if (val === "platformName") {
    return "平台名称";
  } else if (val === "platformSlogan") {
    return "平台SLOGAN";
  } else if (val === "platformIcon") {
    return "平台图标";
  } else if (val === "platformLogo") {
    return "平台LOGO(深色背景)";
  } else if (val === "platformLogoLight") {
    return "平台LOGO(浅色背景)";
  } else if (val === "platformLogoVertical") {
    return "平台LOGO(上下布局，深色背景)";
  } else if (val === "customerServiceHotline") {
    return "平台客服热线";
  } else if (val === "email") {
    return "平台邮箱";
  } else if (val === "copyright") {
    return "版权信息文字";
  } else if (val === "icpRecordNumber") {
    return "ICP备案号";
  } else if (val === "icpRecordNumberLink") {
    return "ICP备案网站链接";
  } else if (val === "securityNumber") {
    return "公网安备信息";
  } else if (val === "securityNumberLink") {
    return "网安备案网站链接";
  }
}

// 取消
const cancel = () => {
  startAdd();
};

async function save(text, val) {
  let freezeText = "确定要保存吗？";
  ElMessageBox.confirm(`${freezeText}`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(() => {
      // save1();
      if (!ruleFormRef.value) return;
      ruleFormRef.value.validate((valid, fields) => {
        if (valid) {
          buttonLoading.value = true;
          save1();
        }
      });
    })
    .catch(() => {
      // if (ruleFormRef.value) ruleFormRef.value.resetFields();
      // startAdd();
    });
}

// 保存
const save1 = async () => {
  const result = findDifferences(deepCopy.value, from.value);
  let isEmpty = Object.keys(result).length === 0;
  if (isEmpty) {
    ElMessage({
      type: "warning",
      message: "未修改任何信息"
    });
    return (buttonLoading.value = false);
  }
  const firstKey = Object.keys(result)[0];
  const text = find(firstKey);
  const operateLog = {
    operateLogType: "PLATFORM_SETTINGS",
    operateType: "修改了" + "平台信息" + text
  };

  const paramsArg = {
    id: from.value.id,
    platformName: from.value.platformName,
    platformIcon: from.value.platformIcon,
    platformLogo: from.value.platformLogo,
    platformLogoLight: from.value.platformLogoLight,
    platformLogoVertical: from.value.platformLogoVertical,
    platformSlogan: from.value.platformSlogan,
    customerServiceHotline: from.value.customerServiceHotline,
    email: from.value.email,
    copyright: from.value.copyright,
    icpRecordNumber: from.value.icpRecordNumber,
    icpRecordNumberLink: from.value.icpRecordNumberLink,
    securityNumber: from.value.securityNumber,
    securityNumberLink: from.value.securityNumberLink,
    officialAvatar: from.value.officialAvatar,
    officialName: from.value.officialName
  };
  console.log("🐳-----paramsArg-----", paramsArg);
  const api = removeEmptyValues(paramsArg);
  const res = await platformInfoSaveOrUpdate(api, operateLog);
  if (res.code === 200) {
    startAdd();
    ElMessage({
      type: "success",
      message: "保存成功"
    });
    buttonLoading.value = false;
  } else {
    ElMessage({
      type: "error",
      message: "保存失败"
    });
    buttonLoading.value = false;
  }
};

//  新增
const newlyAdd = async () => {
  console.log(from.value.customerServiceHotline);

  if (!ruleFormRef.value) return;
  ruleFormRef.value.validate(async (valid, fields) => {
    if (valid) {
      const paramsArg = {
        platformName: from.value.platformName,
        platformIcon: from.value.platformIcon,
        platformLogo: from.value.platformLogo,
        platformLogoLight: from.value.platformLogoLight,
        platformLogoVertical: from.value.platformLogoVertical,
        platformSlogan: from.value.platformSlogan,
        customerServiceHotline: from.value.customerServiceHotline,
        email: from.value.email,
        copyright: from.value.copyright,
        icpRecordNumber: from.value.icpRecordNumber,
        icpRecordNumberLink: from.value.icpRecordNumberLink,
        securityNumber: from.value.securityNumber,
        securityNumberLink: from.value.securityNumberLink,
        officialAvatar: from.value.officialAvatar,
        officialName: from.value.officialName
      };
      const res = await platformInfoSaveOrUpdate(paramsArg);
      if (res.code === 200) {
        ElMessage({
          type: "success",
          message: "新增成功"
        });
      } else {
        ElMessage({
          type: "error",
          message: "新增失败"
        });
      }
    }
  });
};

const dev = ref(false);
const deepCopy = ref();
const startAdd = async val => {
  const res = await platformInfoFind();
  if (res.code === 200) {
    if (res.data === null) {
      dev.value = true;
    } else {
      // 检查返回的数据，如果图片字段是URL形式，需要做特殊处理
      const data = res.data;

      // 处理所有图片字段
      formData.value.forEach(item => {
        if (item.type === "upload" && data[item.prop]) {
          const url = data[item.prop];
          // 如果是URL形式，则需要从URL中提取fileIdentifier
          if (url.startsWith("http")) {
            try {
              const regex = /\/([a-f0-9]{32})\.(?:png|jpg|jpeg|gif|bmp|webp)/i;
              const match = url.match(regex);

              if (match && match[1]) {
                const fileIdentifier = match[1];

                // 保存映射关系
                fileIdentifierMap.value[item.prop] = {
                  fileIdentifier: fileIdentifier,
                  url: url
                };

                // 将表单值改为fileIdentifier
                data[item.prop] = fileIdentifier;
              } else {
                console.warn(`无法从URL中提取fileIdentifier: ${url}`);
              }
            } catch (error) {
              console.error(`提取fileIdentifier时出错: ${error.message}`);
            }
          }
        }
      });

      from.value = data;
      console.log("🌳-----res.data-----", res.data);
      deepCopy.value = JSON.parse(JSON.stringify(data));
    }
  }
};

onMounted(() => {
  startAdd();
});

// 文件上传方法
const fileUpload = async (file, prop) => {
  try {
    // 检查文件大小，10MB = 10 * 1024 * 1024 字节
    const maxSize = 1 * 1024 * 1024;
    if (file.size > maxSize) {
      ElMessage.error("上传图片大小不能超过1MB！");
      return false;
    }

    const { code, data } = await uploadFile(file, () => {}, ["image"]);
    if (code === 200) {
      // 保存fileIdentifier和URL的映射关系
      fileIdentifierMap.value[prop] = {
        fileIdentifier: data.fileIdentifier,
        url: data.url
      };

      // 显示图片使用URL，但表单值使用fileIdentifier
      from.value[prop] = data.fileIdentifier;

      if (ruleFormRef.value) {
        ruleFormRef.value.validateField(prop, valid => {
          if (!valid) {
            console.log("验证失败");
          } else {
            console.log("验证成功");
          }
        });
      }
    }
  } catch (error) {
    ElMessage({
      type: "error",
      message: error.message
    });
  }
};

// const fromobj = reactive({
//   name:"2",
// })

// const options = [
//   { value: '1', label: 'Option1', },
//   { value: '2', label: 'Option2', },
// ]
</script>

<template>
  <div class="commonapp">
    <el-scrollbar style="height: calc(100vh - 282px); margin-bottom: 20px">
      <el-form
        ref="ruleFormRef"
        :model="from"
        :rules="rules"
        label-width="140px"
      >
        <el-form-item
          v-for="(item, index) in formData"
          :key="index"
          :label="item.label"
          :prop="item.prop"
          :required="item.check"
        >
          <el-col :span="11">
            <template v-if="item.type === 'input'">
              <el-input
                v-model="from[item.prop]"
                :placeholder="item.placeholder"
                :type="item.label === '平台SLOGAN' ? 'textarea' : 'text'"
                clearable
              />
            </template>
            <template v-else-if="item.type === 'upload'">
              <el-upload
                v-model="from[item.prop]"
                class="avatar-uploader"
                accept="image/*"
                :show-file-list="false"
                :http-request="() => {}"
                :before-upload="uploadFile => fileUpload(uploadFile, item.prop)"
              >
                <el-image
                  v-if="from[item.prop]"
                  :src="fileIdentifierMap[item.prop]?.url || from[item.prop]"
                  fit="scale-down"
                  class="avatar"
                />
                <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
              </el-upload>
              <div class="upload-tip">
                {{ item.uploadTip }}
              </div>
            </template>
          </el-col>
        </el-form-item>
        <!-- <el-form-item>
          <el-button v-if="dev" type="primary" @click="newlyAdd">新增</el-button>
          <el-button v-else type="primary" :loading="buttonLoading" @click="save">
            保存
          </el-button>
        </el-form-item> -->
      </el-form>
    </el-scrollbar>
    <div class="buttons">
      <el-button v-if="dev" type="primary" @click="newlyAdd">新增</el-button>
      <el-button
        v-else
        type="primary"
        :loading="buttonLoading"
        style="margin-right: 60px"
        @click="save"
      >
        保存
      </el-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.commonapp {
  // height: calc(100vh - 191px);
  height: 100%;
  padding: 20px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
  }
}
.buttons {
  display: flex;
  justify-content: flex-end;
  width: 100%;
}
:deep(.avatar-uploader .avatar) {
  width: 150px;
  height: 150px;
  display: block;
}

:deep(.avatar-uploader .el-upload) {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);

  &:hover {
    border-color: var(--el-color-primary);
  }
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 150px;
  height: 150px;
  text-align: center;
  line-height: 150px;
}

.avatar {
  width: 150px;
  height: 150px;
  display: block;
}

.upload-tip {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
  line-height: 1.4;
}
</style>
