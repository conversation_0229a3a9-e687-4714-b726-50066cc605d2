import { http } from "@/utils/http";

/*  学生管理  */
// 分页查询
export const studentFindAll = params => {
  return http.request(
    "get",
    "/platform/student/findAll",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};

// 根据id查询
export const studentFindById = params => {
  return http.request(
    "get",
    "/platform/student/findById",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};

// 根据学生id分页查询
export const studentSituationFindAllByStudentId = params => {
  return http.request(
    "get",
    "/platform/studentSituation/findAllByStudentId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 根据学生id分页查询
export const studentSituationFindById = params => {
  return http.request(
    "get",
    "/platform/studentSituation/findById",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
