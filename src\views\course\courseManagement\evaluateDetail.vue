<script setup>
import { useRouter, useRoute } from "vue-router";
import { onMounted, ref } from "vue";
import { formatTime } from "@/utils/index";
import { commentsFindId, upOnlyMe } from "@/api/course.js";
import { requestTo } from "@/utils/http/tool";
import { to } from "@iceywu/utils";
import { ElMessage, ElMessageBox } from "element-plus";
import ImgList from "@/components/Base/list1.vue";
import CourseDescrip from "./components/courseDescrip.vue";

const emit = defineEmits(["tabShow", "tabText"]);
const router = useRouter();
const route = useRoute();
const fileListAPi = ref([]);
const srcList = ref([]);
const srcId = ref(0);
const showPreview = ref(false);
const deteilInfo = ref({ textarea: "", content: "" });
// 表头
const tableHeader = ref([
  {
    id: "1",
    label: "课程评分",
    value: "--",
    width: "107px"
  },
  {
    id: "2",
    label: "领队评分",
    value: "--",
    width: "107px"
  },
  {
    id: "3",
    label: "场地评分",
    value: "--",
    width: "107px"
  },
  {
    id: "4",
    label: "讲师评分",
    value: "--",
    width: "107px"
  }
]);
// 获取数据
const getFindCommentsId = async () => {
  const params = {
    id: Number(route.query.id)
  };
  const [err, result] = await to(commentsFindId(params));
  // console.log("🦄result----111-------------------------->", result);
  if (result?.code === 200) {
    deteilInfo.value = result?.data;
    tableHeader.value[0].value = result?.data?.courseScore;
    tableHeader.value[1].value = result?.data?.leaderScore;
    tableHeader.value[2].value = result?.data?.siteScore;
    tableHeader.value[3].value = result?.data?.lecturerScore;
    deteilInfo.value.textarea = result?.data?.replies?.content;
    if (result?.data?.files?.length) {
      result?.data?.files?.map(item => {
        fileListAPi.value.push(item?.uploadFile);
        srcList.value.push(item?.uploadFile?.url);
      });
    }
    // console.log('🐬 deteilInfo.value------------------------------>',deteilInfo.value);
  } else {
    console.log("获取失败");
  }
};
// 图片预览
const handleClick = id => {
  showPreview.value = true;
  srcId.value = id;
};
const courseName = ref("");
const periodNameEvt = val => {
  courseName.value = val;
};
const operateLog = ref({});
// 是否公开
const publicEvt = async (text, bool) => {
  // console.log(
  //   "🐬 props.evaluateData------------------------------>",
  // );
  // console.log(
  //   "🍭 deteilInfo.value?.parent?.name------------------------------>",
  //   deteilInfo.value
  // );
  // return;
  if (bool === true) {
    if (courseName.value && deteilInfo.value?.parent?.name) {
      operateLog.value = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `不公开“${courseName.value}”课程中的“${deteilInfo.value?.parent?.name}”用户评价`
      };
    } else {
      operateLog.value = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `不公开“${courseName.value}”课程中的用户评价`
      };
    }
  } else {
    if (courseName.value && deteilInfo.value?.parent?.name) {
      operateLog.value = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `公开“${courseName.value}”课程中的“${deteilInfo.value?.parent?.name}”用户评价`
      };
    } else {
      operateLog.value = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `公开“${courseName.value}”课程中的用户评价`
      };
    }
  }

  // const operateLog = {
  //   operateLogType: "COURSE_MANAGEMENT",
  //   operateType:
  //     bool === true
  //       ? `不公开“${props.evaluateData.name}”课期中的${deteilInfo.value?.parent?.name}用户评价`
  //       : `公开“${props.evaluateData.name}”课期中的${deteilInfo.value?.parent?.name}用户评价`
  // };
  ElMessageBox.confirm(`确定${text}该用户评价吗?`, `${text}用户评价`, {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: ""
  })
    .then(async () => {
      const params = {
        id: Number(route.query.id) || "",
        onlyMe: bool
      };
      const [err, res] = await to(upOnlyMe(params, operateLog.value));
      if (res.code == 200) {
        ElMessage({
          type: "success",
          message: bool === true ? "不公开成功" : "公开成功"
        });
        getFindCommentsId();
      } else {
        ElMessage({
          type: "error",
          message: bool === true ? "不公开失败" : "公开失败"
        });
      }
    })
    .catch(() => {
      // ElMessage({
      //   type: "info",
      //   message: "已取消"
      // });
      // emit("tabShow", true);
    });
};
onMounted(() => {
  getFindCommentsId();
});
</script>

<template>
  <div>
    <CourseDescrip
      :periodId="Number(route.query.periodId)"
      :courseId="Number(route.query.courseId)"
      @name="periodNameEvt"
    />
    <div class="evaluate-detail">
      <div class="user-comments">
        <div class="evaluate">
          <div class="user-content">
            <div class="title">
              <div class="text">
                <div class="name">
                  {{
                    deteilInfo?.parent?.name
                      ? `${deteilInfo?.parent?.name}`
                      : userName
                        ? `${userName}`
                        : ""
                  }}
                </div>
              </div>
            </div>
            <el-input
              v-model="deteilInfo.content"
              type="textarea"
              resize="none"
              disabled
              class="area"
              :placeholder="
                deteilInfo?.content ? deteilInfo?.content : '暂无数据'
              "
            />
          </div>
          <div class="pic">
            <div class="title">
              <div class="text">
                <!-- <div class="name">评价配图</div> -->
              </div>
              <div class="text">
                {{
                  deteilInfo?.createdAt
                    ? formatTime(deteilInfo?.createdAt, "YYYY-MM-DD HH:mm:ss")
                    : ""
                }}
              </div>
            </div>
            <ImgList
              v-if="fileListAPi?.length"
              :imgList="fileListAPi"
              :srcList="srcList"
              :itemCount="fileListAPi?.length"
            />
            <!-- <div v-if="fileListAPi?.length" class="banner">
            <el-carousel
              :interval="4000"
              type="card"
              height="200px"
              :autoplay="false"
            >
              <el-carousel-item
                v-for="(item, index) in fileListAPi"
                :key="item"
              >
                <img
                  :src="item.url"
                  class="h-full"
                  @click="handleClick(index)"
                >
              </el-carousel-item>
            </el-carousel>
            <el-image-viewer
              v-if="showPreview"
              :url-list="srcList"
              show-progress
              :initial-index="srcId"
              @close="showPreview = false"
            />
          </div> -->
            <!-- <div class="buttons">
              <el-button
                v-if="deteilInfo.onlyMe === true"
                class="create"
                type="primary"
                @click="publicEvt('公开', false)"
              >
                {{ "通过" }}
              </el-button>
              <el-button v-else type="danger" class="create" @click="publicEvt('不公开', true)">
                {{ "驳回" }}
              </el-button>
            </div> -->
          </div>
        </div>
      </div>
      <div class="rating">
        <div class="title">
          <div class="name">{{ "评分" }}</div>
          <div class="number">{{ deteilInfo?.rating || 0 }}</div>
        </div>
        <div class="content">
          <el-descriptions class="margin-top" title="" :column="2" border>
            <template v-for="(item, index) in tableHeader" :key="index">
              <el-descriptions-item width="120px">
                <template #label>
                  <div class="cell-item">{{ item.label }}</div>
                </template>
                {{ item.value }}
              </el-descriptions-item>
            </template>
          </el-descriptions>
        </div>
      </div>
      <div class="replies">
        <div class="title">
          <div class="text">
            {{ "机构回复" }}
          </div>
          <div class="text">
            {{
              deteilInfo?.replies?.createdAt
                ? formatTime(
                    deteilInfo?.replies?.createdAt,
                    "YYYY-MM-DD HH:mm:ss"
                  )
                : ""
            }}
          </div>
        </div>
        <div class="content">
          <div class="text-area">
            <el-input
              v-model="deteilInfo.textarea"
              type="textarea"
              resize="none"
              :placeholder="
                deteilInfo?.textarea ? deteilInfo?.textarea : '暂无数据'
              "
              disabled
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.evaluate-detail {
  font-size: 14px;
  background-color: #fff;
  width: 100%;
  height: 700px;
  padding: 20px 30px;
  .user-comments {
    width: 100%;
    margin-bottom: 60px;

    .evaluate {
      width: 100%;
      display: flex;
      justify-content: space-between;
      // margin-bottom: 25px;
      .title {
        width: 100%;
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        .text {
          display: flex;
        }
        .name {
          margin-right: 50px;
        }
      }
      .user-content {
        width: 49%;
        height: 250px;
        .area {
          width: 100%;
          height: 250px;
        }
      }

      :deep(.el-textarea__inner) {
        height: 250px;
      }
      .pic {
        width: 49%;
        height: 100px;
        :deep(.el-carousel--horizontal) {
          height: 100px;
        }
        .buttons {
          // display: flex;
          // justify-content: space-between;
          // width: 95%;
          // margin: 0 auto;
          // margin-top: 28vh;
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          justify-content: flex-end;
          .create {
            // display: flex;
            // align-items: center;
            // justify-content: center;
            // width: 100px;
            // height: 36px;
            // color: rgb(255 255 255 / 100%);
            // cursor: pointer;
            // background-color: rgb(64 149 229 / 100%);
            // border-radius: 6px;
            margin-top: 15px;
          }
        }
      }
    }
  }
  .rating {
    width: 100%;
    margin-bottom: 30px;
    .title {
      width: 100%;
      display: flex;
      margin-bottom: 10px;
      .name {
        margin-right: 30px;
      }
    }
    .content {
      width: 49%;
    }
  }
  .replies {
    width: 100%;
    .title {
      width: 100%;
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
    }
    .content {
      width: 100%;
      display: flex;
      justify-content: space-between;
      .text-area {
        width: 100%;
        :deep(.el-textarea__inner) {
          height: 120px;
        }
      }
    }
  }
  :deep(.el-carousel__item) {
    margin: 0;
    line-height: 200px;
    text-align: center;
  }
}
</style>
