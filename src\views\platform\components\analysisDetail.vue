<script setup>
import { ref, computed } from "vue";
import activeImg from "@/assets/dataAnalysis/active.png";
import courseImg from "@/assets/dataAnalysis/course.png";
import platformImg from "@/assets/dataAnalysis/plateform.jpg";
import institutionImg from "@/assets/dataAnalysis/institution.png";
import userImageImg from "@/assets/dataAnalysis/user.png";
import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const route = useRoute();
const imgView = computed(() => {
  switch (route.query.type) {
    case "active":
      return activeImg;
    case "course":
      return courseImg;
    case "platform":
      return platformImg;
    case "institution":
      return institutionImg;
    case "userImage":
      return userImageImg;
    default:
      return activeImg;
  }
});
</script>

<template>
  <div class="img-view">
    <img :src="imgView" alt="">
  </div>
</template>

<style lang="scss" scoped>
.img-view {
  width: 100%;
  img {
    width: 100%;
    height: 100%;
  }
}
</style>
