<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from "vue";
import { useRole } from "./utils/hook.jsx";
import ImgBlurHash from "@/components/ImgBlurHash";
import { Edit, Check, View, Plus } from "@element-plus/icons-vue";

const {
  moveUp,
  moveDown,
  loadingTable,
  columns,
  dataList,
  pagination,
  openDialog,
  handleDelete,
  handleMenu,
  handleSizeChange,
  handleCurrentChange,

  dialogFormVisible,
  form,
  ruleFormRef,
  rules,
  gridData,

  optionsData,
  changeSelect,
  handleCurrentChangeA,
  handleSizeChangeA,

  getInfoid,
  changeOrganization,
  clearOrganization,
  addressLinkDom,
  inputAdd,
  beforeCloseDom,
  fileUpload,
  confirmAdd,
  changeInput,
  focusCourseId,
  buttonLoading
} = useRole();
</script>

<template>
  <div class="commonapp">
    <div class="title">
      <el-button type="primary" @click="openDialog('新增')">新增</el-button>
    </div>

    <pure-table
      ref="tableRef"
      row-key="id"
      adaptive
      :adaptiveConfig="{ offsetBottom: 108 }"
      align-whole="left"
      table-layout="auto"
      :loading="loadingTable"
      :data="dataList"
      :style="{ height: 'calc(100%-110px)' }"
      :columns="columns"
      :pagination="{ ...pagination }"
      :header-cell-style="{
        background: 'var(--el-fill-color-light)',
        color: 'var(--el-text-color-primary)'
      }"
      @page-size-change="handleSizeChange"
      @page-current-change="handleCurrentChange"
    >
      <template #operation="{ row }">
        <div class="botlist">
          <div class="u">
            <el-button
              class="reset-margin"
              link
              type="primary"
              @click="inputAdd('编辑', row)"
            >
              编辑
            </el-button>
          </div>
          <div class="u">
            <el-button
              class="reset-margin"
              link
              type="danger"
              @click="handleDelete('删除', row)"
            >
              删除
            </el-button>
          </div>
          <div class="u">
            <el-button
              v-if="row.id !== moveUp"
              class="reset-margin"
              link
              type="primary"
              @click="handleMenu('上移', row)"
            >
              上移
            </el-button>
          </div>
          <div class="u">
            <el-button
              v-if="row.id !== moveDown"
              class="reset-margin"
              link
              type="primary"
              @click="handleMenu('下移', row)"
            >
              下移
            </el-button>
          </div>
        </div>
      </template>
    </pure-table>

    <el-dialog
      v-model="addressLinkDom"
      style="min-width: 420px"
      width="30%"
      :title="`${form.title}轮播图`"
      @closed="beforeCloseDom(ruleFormRef)"
    >
      <el-form
        ref="ruleFormRef"
        :model="form"
        label-width="110px"
        label-position="right"
        :rules="rules"
      >
        <el-form-item
          label="图片"
          prop="url"
          show-word-limit
          style="display: flex; align-items: center; flex-wrap: wrap"
        >
          <el-upload
            v-model="form.url"
            class="avatar-uploader"
            accept="image/*"
            :show-file-list="false"
            :http-request="() => {}"
            :before-upload="uploadFile => fileUpload(uploadFile, form)"
          >
            <el-image
              v-if="form.url"
              :src="form.url"
              fit="scale-down"
              class="avatar"
            />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <text class="custom-title">
            支持上传png、jpg、jpeg、gif、bmp、webp图片格式，最多上传1张，最佳尺寸：200*200px，单张大小不超过10MB
          </text>
        </el-form-item>
        <el-form-item label="地址链接" prop="linkUrl" show-word-limit>
          <el-input
            v-model.trim="form.linkUrl"
            placeholder="请输入地址链接"
            clearable
          />
        </el-form-item>
        <el-form-item label="机构" prop="organizationId" show-word-limit>
          <el-select
            v-model="form.organizationId"
            placeholder="请选择机构"
            clearable
            filterable
            @change="changeOrganization(form)"
            @clear="clearOrganization()"
          >
            <el-option
              v-for="item in optionsData"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="课程" prop="courseId" show-word-limit>
          <el-select
            v-model="form.courseId"
            placeholder="请选择课程"
            clearable
            filterable
            @focus="focusCourseId(form)"
          >
            <template v-if="gridData.length > 0">
              <el-option
                v-for="course in gridData"
                :key="course.id"
                :label="course.name"
                :value="course.id"
              />
            </template>
            <template v-else>
              <el-option
                v-if="form.organizationId"
                :label="'该机构暂无课程'"
                :value="''"
                disabled
                style="text-align: center"
              />
              <el-option
                v-else
                :label="'请先选择机构'"
                :value="''"
                disabled
                style="text-align: center"
              />
            </template>
          </el-select>
        </el-form-item>
      </el-form>
      <div class="buttom">
        <el-button @click="beforeCloseDom(ruleFormRef)">取消</el-button>
        <el-button
          :loading="buttonLoading"
          type="primary"
          @click="confirmAdd(ruleFormRef, form)"
        >
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.commonapp {
  // margin: 20px;
  .title {
    margin-bottom: 10px;
    font-size: 20px;
    font-weight: bold;
    color: #606266;
    display: flex;
    justify-content: end;
  }
}

// .custom-title {
//   font-size: 20px;
//   font-weight: bold;
//   color: #606266;
// }

.con_pagination {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  padding-top: 20px;
}

.buttom {
  display: flex;
  justify-content: end;
}

.botlist {
  display: flex;
  .u {
    width: 50px;
  }
}

:deep(.el-form-item__label) {
  display: flex;
  align-items: center;
}

.custom-dropdown {
  max-height: 200px; /* 设定一个最大高度 */
  overflow-y: auto; /* 允许垂直滚动 */
}

:deep(.avatar-uploader .avatar) {
  width: 120px;
  height: 120px;
  display: block;
}
:deep(.avatar-uploader .el-upload) {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}
:deep(.avatar-uploader .el-upload:hover) {
  border-color: var(--el-color-primary);
}
:deep(.el-icon.avatar-uploader-icon) {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 120px;
  text-align: center;
}
.custom-title {
  display: block;
  width: 100%;
  font-size: 12px;
  color: #8c939d;
}
</style>
