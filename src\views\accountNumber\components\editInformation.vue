<script setup>
import { ref, onMounted, watchEffect, watch, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { requestTo } from "@/utils/http/tool";
import uploadImg from "@/assets/login/upload1.png";
import FileItem from "@/components/PreviewV2/FileItem.vue";
import {
  ledeterList,
  editteaInfo,
  getPhonecode,
  verifyPhone,
  fileLeaderList,
  fileLeatureList
} from "@/api/leaderLecturer.js";
import { formatTime } from "@/utils/index";
import {
  uploadFile,
  isOverSizeLimit,
  validateFileType
} from "@/utils/upload/upload";
import { decrypt, encryption } from "@/utils/SM4.js";
import { compareObjects, debounce } from "@iceywu/utils";
import { Hide, View, Loading } from "@element-plus/icons-vue";
import qrcode from "qrcode";
import WxQrCode from "@/components/WxQrCode/index.vue";
import { getBindCodeOrganization, getUnbindOrganization } from "@/api/user.js";

const router = useRouter();
const route = useRoute();
const url = ref("https:/www.baidu.com/");
const qrCodeData = ref("");
const richFlag = ref(false);

// 微信相关变量
const wxQrCodeRef = ref(null);
const showUnbindDialog = ref(false);
const isWxCallbackProcessed = ref(false);
const unbindWxLoading = ref(false);

onMounted(() => {
  getacctable();
  getDifferentFile();
  qrcode.toDataURL(url.value, (err, url) => {
    if (err) {
      console.error(err);
    } else {
      qrCodeData.value = url;
    }
  });

  // 确保初始状态下没有验证码字段
  const codenumbIndex = formData.value.findIndex(
    item => item.prop === "codenumb"
  );
  if (codenumbIndex !== -1) {
    formData.value.splice(codenumbIndex, 1);
  }
});
// 表头
const tableHeader = ref([
  { label: "机构", value: "", key: "organizationName" },
  { label: "", value: "", width: "107px", key: "id" },
  { label: "创建时间", value: "", width: "107px", key: "createdAt" }
]);
watchEffect(() => {
  const title = route.query.title;
  // console.log("🍭-----title-----", title);
  tableHeader.value[1].label = title === "ldMang" ? "领队ID" : "讲师ID";
});
// 表单
const form = ref({
  files: [],
  name: "", //姓名
  account: "", //账号
  phone: "", //手机号码
  email: "", //邮箱
  idNumber: "", //身份号
  isBindWx: null //是否绑定微信
  // upload: []
});
const formFile = ref({
  institutionLicense: [],
  qualificationDocuments: []
});
// 根据不同身份获取上传文件
const getDifferentFile = async () => {
  const params = {
    organizationAdminId: route.query?.id
  };
  let requestApi =
    route.query.title === "ldMang"
      ? fileLeaderList(params)
      : fileLeatureList(params);
  const { code, data, msg } = await requestApi;
  if (code == 200) {
    // console.log("🐠-----data---/////////////--", data);
    formFile.value.institutionLicense = [];
    formFile.value.qualificationDocuments = [];
    if (data?.fileDTOS) {
      data.fileDTOS.forEach(item => {
        if (item.fileType == "BUSINESS_LICENSE") {
          formFile.value.institutionLicense.push(item.uploadFile);
        } else if (item.fileType == "QUALIFICATION_DOCUMENT") {
          formFile.value.qualificationDocuments.push(item.uploadFile);
        }
      });
    }
  }
};
// 提交
const formRef = ref(null);
const formData = ref([
  {
    label: "姓名",
    type: "input",
    prop: "name",
    check: true,
    placeholder: "请输入姓名",
    width: "400px",
    maxLength: 10
  },
  {
    label: "账号",
    type: "input",
    prop: "account",
    check: true,
    placeholder: "请输入账号",
    width: "400px",
    maxLength: 20
  },
  {
    label: "手机号",
    type: "input",
    prop: "phone",
    check: true,
    isView: true,
    placeholder: "请输入手机号",
    width: "400px",
    maxLength: 11
  },
  {
    label: "邮箱",
    type: "input",
    prop: "email",
    placeholder: "请输入邮箱",
    width: "400px",
    maxLength: 30
  },
  {
    label: "身份证号",
    type: "input",
    prop: "idNumber",
    isView: true,
    placeholder: "请输入身份证号",
    width: "400px",
    maxLength: 18
  },
  {
    label: "微信绑定",
    type: "img",
    prop: "isBindWx",
    url: "",
    width: "400px",
    height: "120px"
  },
  {
    label: "资质文件",
    type: "upload",
    prop: "qualificationDocuments",
    width: "1200px",
    text: "支持上传图片及pdf、doc、docx、ppt、pptx、xls、xlsx文件，图片最佳尺寸：750*1334px，单张图片大小不超过10MB，文件单个大小不超过30MB "
  }
]);
//文件处理
const fileData = () => {
  //机构营业执照
  let institutionLicense = formFile.value.institutionLicense;
  //资质文件
  let qualificationDocuments = formFile.value.qualificationDocuments;
  if (institutionLicense.length > 0) {
    setFilesFn(institutionLicense, "BUSINESS_LICENSE");
  }
  if (qualificationDocuments.length > 0) {
    setFilesFn(qualificationDocuments, "QUALIFICATION_DOCUMENT");
  }
};
const setFilesFn = (val, type) => {
  for (let index = 0; index < val.length; index++) {
    const element = val[index].fileIdentifier;
    console.log("🌵-----element-----", element);
    form.value.files.push({
      fileType: type,
      fileIdentifier: element,
      sortOrder: index + 1
    });
  }
};
// 自定义电话号码校验方法
const validatePhoneNumber = async (rule, value, callback) => {
  // 先检查是否是切换加密/解密导致的值变化
  const originalPhone = oldData.value.phone;
  const decryptedPhone = decrypt(oldData.value.phoneCt || "");
  // 如果当前值等于原始掩码值或解密值，则认为没有修改
  if (value === originalPhone || value === decryptedPhone) {
    // 如果有验证码字段，移除它
    const codenumbIndex = formData.value.findIndex(
      item => item.prop === "codenumb"
    );
    if (codenumbIndex !== -1) {
      formData.value.splice(codenumbIndex, 1);
    }
    callback();
    return;
  }
  const phoneRegex = /^1[3-9]\d{9}$/; // 匹配中国大陆手机号码
  if (!value) {
    callback(new Error("手机号不能为空"));
  } else if (!phoneRegex.test(value)) {
    callback(new Error("请输入有效的手机号码"));
  } else {
    // 手机号有实际修改，添加验证码字段
    // const codenumbIndex = formData.value.findIndex(
    //   item => item.prop === "codenumb"
    // );
    // if (codenumbIndex === -1) {
    //   // 往数组指定位置添加验证码字段
    //   formData.value.splice(3, 0, {
    //     label: "验证码",
    //     type: "input",
    //     prop: "codenumb",
    //     placeholder: "请输入验证码",
    //     width: "220px",
    //     check: true
    //   });
    // }
    const params = {
      phone: encryption(value)
    };
    try {
      const response = await verifyPhone(params);
      if (response.code === 70008) {
        callback(new Error("手机号已存在"));
      } else {
        callback();
      }
    } catch (error) {
      console.log("🌈-----error-----", error);
    }
  }
};
const ID_NUMBER_REGEX = /(^\d{15}$)|(^\d{17}([0-9X])$)/i;
const CHECK_CODES = ["1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2"];
const FACTORS = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
const validateIdNumber = (rule, value, callback) => {
  if (!value) {
    callback();
    return;
  }
  const original = oldData.value.idNumber;
  if (value.includes("*")) {
    if (!formData.value.find(f => f.prop === "idNumber").isView) {
      return callback(new Error("身份证号格式不正确"));
    } else if (original === value) {
      callback();
      return;
    }
  }
  if (!value) {
    callback();
    return;
  }
  if (value.length < 18) {
    callback(new Error("身份证号格式不正确"));
    return;
  }
  if (value && !ID_NUMBER_REGEX.test(value)) {
    return callback(new Error("身份证号格式不正确"));
  }
  if (value && value.length === 18 && !isValidChineseIDChecksum(value)) {
    return callback(new Error("身份证号不正确"));
  }
  oldData.value.idNumber =
    value.substr(0, 3) + "********" + value.substr(value.length - 4);
  oldData.value.idNumberCt = encryption(value);
  callback();
};
function isValidChineseIDChecksum(idNumber) {
  let sum = 0;
  for (let i = 0; i < 17; i++) {
    const digit = parseInt(idNumber[i], 10);
    if (isNaN(digit)) {
      return false; // 遇到非数字字符返回 false
    }
    sum += digit * FACTORS[i];
  }

  const index = sum % 11;
  const expectedCheckCode = CHECK_CODES[index];
  return expectedCheckCode.toUpperCase() === idNumber[17].toUpperCase();
}
// 校验规则
const rules = ref({
  name: [
    { required: true, message: "姓名不能为空", trigger: "blur" },
    { min: 2, max: 10, message: "姓名长度应在2-10个字符之间", trigger: "blur" }
  ],
  account: [{ required: true, message: "账号不能为空", trigger: "blur" }],
  phone: [{ required: true, validator: validatePhoneNumber, trigger: "blur" }],
  codenumb: [
    {
      required: true,
      validator: (rule, value, callBack) => {
        if (!value) {
          callBack("验证码不能为空");
          return;
        }
        if (value.length > 10) {
          callBack("请输入有效的验证码");
          return;
        }
        callBack();
      },
      trigger: ["blur", "change"]
    }
  ],
  idNumber: [{ validator: validateIdNumber, trigger: ["blur", "change"] }],
  email: [
    {
      validator: (rule, value, callBack) => {
        if (!value) {
          callBack();
          return;
        }
        // 邮箱正则表达式
        const pattern = /^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$/i;

        // 测试邮箱是否匹配模式
        if (!pattern.test(value)) {
          callBack("邮箱格式错误");
          return;
        }
        callBack();
      },
      trigger: "blur"
    }
  ]
});
// 提交表单/编辑
const oldData = ref({});
const submitForm = () => {
  formRef.value.validate(async (s, b) => {
    if (s) {
      // console.log("表单数据:", form.value);
      form.value.files = []; // 清空文件集合
      fileData(); //文件集合处理
      // 新增
      const addParam = {
        id: form.value?.id,
        name: form.value?.name,
        account: `${form.value?.account}@${organ.value}`,
        files: form.value.files,
        leaderLecturerType:
          route.query?.title === "jsMang" ? "LECTURER" : "LEADER"
      };

      // 处理邮箱 - 有值就保留
      if (form.value.email && form.value.email.trim() !== "") {
        addParam.email = form.value.email;
      }

      // 处理身份证号 - 根据当前状态判断如何处理
      if (form.value.idNumber && form.value.idNumber.trim() !== "") {
        // 判断身份证号是否已经是掩码状态
        const idNumberIndex = formData.value.findIndex(
          item => item.prop === "idNumber"
        );

        if (idNumberIndex !== -1 && formData.value[idNumberIndex].isView) {
          // 如果是掩码状态，使用原始的加密数据
          if (oldData.value.idNumberCt) {
            addParam.idNumber = oldData.value.idNumberCt;
          } else {
            addParam.idNumber = encryption(form.value.idNumber);
          }
        } else if (
          form.value.idNumber === decrypt(oldData.value.idNumberCt || "")
        ) {
          // 如果是已解密但未修改，使用原始加密数据
          addParam.idNumber = oldData.value.idNumberCt;
        } else {
          // 否则加密新输入的身份证号
          addParam.idNumber = encryption(form.value.idNumber);
        }
      }

      // 处理手机号参数
      const originalPhone = oldData.value.phone;
      const decryptedPhone = decrypt(oldData.value.phoneCt || "");
      if (
        form.value.phone &&
        form.value.phone !== originalPhone &&
        form.value.phone !== decryptedPhone
      ) {
        // 手机号有实际修改，需要加密并验证码
        addParam.phone = encryption(form.value.phone);
        // console.error(form.value.codenumb)
        // if (!form.value.codenumb) {
        //   ElMessage({
        //     type: "error",
        //     message: "修改手机号需要验证码"
        //   });
        //   return;
        // }
        // addParam.code = form.value.codenumb;
        // addParam.code = form.value.phone;
      }

      console.log("🦄-----addParam-----", addParam);
      let paramsData = {};
      for (const paramsDataKey in addParam) {
        console.log("🐬-----paramsDataKey-----", addParam[paramsDataKey]);
        let isArray = Array.isArray(addParam[paramsDataKey]);
        if (isArray) {
          if (addParam[paramsDataKey].length > 0) {
            paramsData[paramsDataKey] = addParam[paramsDataKey];
          }
        } else {
          if (addParam[paramsDataKey]) {
            paramsData[paramsDataKey] = addParam[paramsDataKey];
          }
        }
      }

      console.log("🌈-----copyData.value-----", copyData.value);
      console.log("🍧-----paramsData-----", paramsData);
      // return;
      // 创建一个新的对象，排除email和idNumber
      const paramsForComparison = { ...paramsData };
      delete paramsForComparison.email;
      delete paramsForComparison.idNumber;

      let params = compareObjects(copyData.value, paramsData);

      // 确保email和idNumber总是被包含在参数中（如果有值）
      if (form.value.email && form.value.email.trim() !== "") {
        params.email = form.value.email;
      }

      if (form.value.idNumber && form.value.idNumber.trim() !== "") {
        // 判断身份证号是否已经是掩码状态
        const idNumberIndex = formData.value.findIndex(
          item => item.prop === "idNumber"
        );

        if (idNumberIndex !== -1 && formData.value[idNumberIndex].isView) {
          // 如果是掩码状态，使用原始的加密数据
          if (oldData.value.idNumberCt) {
            params.idNumber = oldData.value.idNumberCt;
          } else {
            params.idNumber = encryption(form.value.idNumber);
          }
        } else if (
          form.value.idNumber === decrypt(oldData.value.idNumberCt || "")
        ) {
          // 如果是已解密但未修改，使用原始加密数据
          params.idNumber = oldData.value.idNumberCt;
        } else {
          // 否则加密新输入的身份证号
          params.idNumber = encryption(form.value.idNumber);
        }
      }

      params.id = form.value?.id;
      params.leaderLecturerType =
        route.query?.title === "jsMang" ? "LECTURER" : "LEADER";
      console.log("🐠---889--params-----", params);
      // return;
      const operateLog = {
        operateLogType:
          route.query?.title === "jsMang"
            ? "LECTURER_MANAGEMENT"
            : "LEADER_MANAGEMENT",
        operateType: `编辑了${form.value.name}的账号`
      };
      const { code, data, msg } = await editteaInfo(params, operateLog);
      if (code == 200) {
        ElMessage({
          message: "编辑成功",
          type: "success"
        });
        const title = route.query.title === "jsMang" ? "jsMang" : "ldMang";
        router.push({
          path: "/accountNumber/components/teacherDetails",
          query: { id: route.query?.id, title }
        });
      } else {
        ElMessage({
          message: msg,
          type: "error"
        });
      }
    } else {
      console.log("表单校验失败");
    }
  });
};
const copyData = ref({});
const organ = ref("");
// 账号详情回显
const getacctable = async () => {
  const [err, result] = await requestTo(ledeterList({ id: route.query?.id }));
  console.log("🎉-----result-----", result);
  if (result) {
    organ.value = result?.organizationAlias;
    tableHeader.value = tableHeader.value.map(item => ({
      ...item,
      value:
        item.key === "createdAt"
          ? formatTime(result[item.key], "YYYY-MM-DD HH:mm")
          : (result[item.key] ?? "--")
    }));
    form.value = {
      id: result?.id,
      name: result?.name,
      account: result?.account.split("@")[0],
      // phone: decrypt(result?.phoneCt),
      phone: result?.phone,
      email: result?.email,
      // idNumber: result?.idNumberCt ? decrypt(result?.idNumberCt) : "",
      idNumber: result?.idNumber,
      // email: result?.email
      upload: result?.upload ?? form.value.upload,
      isBindWx: result?.isBindWx
    };

    richFlag.value = true;
    oldData.value = {
      isAdmin: false,
      name: result?.name,
      account: result?.account,
      email: result?.email,
      phone: result?.phone,
      phoneCt: result?.phoneCt,
      idNumber: result?.idNumber,
      idNumberCt: result?.idNumberCt,
      upload: result?.upload ?? form.value.upload
    };

    copyData.value = {
      // id: result?.id,
      name: result?.name,
      account: result?.account,
      phone: encryption(result?.phone || ""),
      // phone: result?.phone,
      // organizationId: result?.organizationId,
      email: result?.email,
      // idNumber: encryption(result.idNumber || ""),
      idNumber: encryption(result?.idNumber || ""),
      idNumberCt: result?.idNumberCt,
      // files: result.files,
      leaderLecturerType:
        route.query?.title === "jsMang" ? "LECTURER" : "LEADER"
    };
  }
  // console.log(decrypt(result?.phoneCt));
};
// 获取验证码
const getCaptcha = async (phoneCode, prop) => {
  let t = prop + "Ct";
  const phoneRegex = /^1[3-9]\d{9}$/; // 匹配中国大陆手机号码
  if (!phoneCode) {
    ElMessage({ message: "手机号不能为空" });
    return;
  }
  if (!phoneRegex.test(phoneCode)) {
    phoneCode = decrypt(oldData.value[t]);
    // ElMessage({ message: "电话号码格式不正确", type: "warning" });
    // return;
  }
  const params = {
    phone: encryption(phoneCode),
    codeType: "VERIFICATION_CODE"
  };
  const { code, msg } = await getPhonecode(params);
  if (code == 200) {
    ElMessage({
      message: "验证码已发送",
      type: "success"
    });
  }
};
// 取消
const cancelForm = () => {
  const title = route.query.title === "jsMang" ? "jsMang" : "ldMang";
  router.push({
    path: "/accountNumber/components/teacherDetails",
    query: { id: route.query?.id, title }
  });
};
// 文件上传之前
const fileList = ref(null);
const beforeUpload = async (file, item) => {
  console.log("🦄-----item-----", item);
  console.log("💗beforeUpload---------->", file);
  let isType = validateFileType(file, [
    "image",
    "pdf",
    "word",
    "text",
    "excel",
    "ppt"
  ]);
  let isSize = isOverSizeLimit(file, isType.valid === true ? 10 : 30);
  if (isSize.valid === true) {
    let { code, data } = await uploadFile(file);
    if (code === 200) {
      console.log("🌳-----data-----", data);
      formFile.value[item].push(data);
      console.log("🎁----- form.fileData-----", formFile.value[item]);
    }
  } else {
    ElMessage.error(`文件大小不能超过${isSize.message}M`);
  }
};
//删除文件
// const handleClickDetele = (item, index) => {
//   console.log("🌈-----item, index-----", item, index);
//   // return
//   formFile.value[item].splice(index, 1);
//   // maxUpload.value.uploadFile = 0;
// };
const getDeleted = (item, index) => {
  // form.value[item].splice(index, 1);
  formFile.value[item].splice(index, 1);
};
const isViewFn = (val, index) => {
  formData.value[index].isView = !formData.value[index].isView;
  const originalValue = form.value[val];

  if (val === "idNumber") {
    if (formData.value[index].isView) {
      // 切换为显示掩码数据
      form.value[val] = oldData.value[val];
    } else {
      // 切换为显示解密数据
      form.value[val] = decrypt(oldData.value[val + "Ct"]);
    }
  } else if (val === "phone") {
    if (formData.value[index].isView) {
      // 切换为显示掩码数据
      form.value[val] = oldData.value[val];
    } else {
      // 切换为显示解密数据
      form.value[val] = decrypt(oldData.value[val + "Ct"]);
    }

    // 检查新值是否与原始值或解密值相同
    const newValue = form.value[val];
    const originalPhone = oldData.value.phone;
    const decryptedPhone = decrypt(oldData.value.phoneCt || "");
    // 如果只是切换加密/解密状态，不是实际修改内容，则移除验证码
    if (newValue === originalPhone || newValue === decryptedPhone) {
      const codenumbIndex = formData.value.findIndex(
        item => item.prop === "codenumb"
      );
      if (codenumbIndex !== -1) {
        formData.value.splice(codenumbIndex, 1);
      }
    }
  }
  // console.error(val, form.value[val]);
};

// 微信回调处理
const handleWxCallback = (code, state) => {
  // 检查是否已经处理过此次绑定请求
  const processedKey = `wx_bind_processed_${code}_${state}`;
  if (sessionStorage.getItem(processedKey)) {
    console.log("已处理过此绑定请求，不再重复处理");
    isWxCallbackProcessed.value = true;
    return;
  }

  const storedState = sessionStorage.getItem("wx_login_state");

  // 状态码 是否一致
  if (state !== storedState) {
    ElMessage.error("微信状态不一致，请重新扫码");
    isWxCallbackProcessed.value = true; // 标记为已处理
    return;
  }

  console.log(form.value.isBindWx, "isbind");

  // 状态码一致，处理微信绑定
  if (form.value.isBindWx) {
    // 如果当前已绑定微信，这里可以处理解绑逻辑（如果需要的话）
    ElMessage.info("当前账号已绑定微信");
    isWxCallbackProcessed.value = true; // 标记为已处理
  } else {
    // 如果当前未绑定微信，处理绑定逻辑
    bindCode(code, processedKey);
  }
};

// 绑定账号
const bindCode = async (code, processedKey) => {
  console.log(code, "code");
  const params = {
    code: code,
    userId: route.query.id,
    userType: "ORGANIZATION_ADMIN"
  };

  try {
    const res = await getBindCodeOrganization(params, {
      operateLogType: "ACCOUNT_MANAGEMENT",
      operateType: `对账号“${form.value.account}”完成微信绑定操作`
    });
    if (res.code === 200) {
      ElMessage.success("绑定成功");
      // 记录已处理状态到会话存储
      sessionStorage.setItem(processedKey, "true");

      // 重新获取用户信息
      await getacctable();

      // 重置二维码组件状态
      if (wxQrCodeRef.value) {
        wxQrCodeRef.value.resetInit();
      }

      // 标记为已处理
      isWxCallbackProcessed.value = true;
    } else {
      ElMessage.warning(res.msg);
      isWxCallbackProcessed.value = true; // 标记为已处理
    }
  } catch (err) {
    isWxCallbackProcessed.value = true; // 标记为已处理
    throw new Error(err);
  }
};

// 解绑微信
const handleChangeWx = debounce(
  async () => {
    if (unbindWxLoading.value) return;
    unbindWxLoading.value = true;
    try {
      const params = {
        userId: route.query.id,
        userType: "ORGANIZATION_ADMIN"
      };

      const res = await getUnbindOrganization(params, {
        operateLogType: "ACCOUNT_MANAGEMENT",
        operateType: `对账号“${form.value.account}”完成微信解绑操作`
      });

      if (res.code === 200) {
        ElMessage.success("微信解绑成功");

        // 关闭对话框
        showUnbindDialog.value = false;

        await getacctable();
      }
      console.log("微信解绑完成");
    } catch (error) {
      ElMessage.error("微信解绑失败，请重试");
    } finally {
      unbindWxLoading.value = false;
    }
  },
  1000,
  { immediate: true }
);

// 构建包含当前 query 参数的重定向路径
const redirectPathWithQuery = computed(() => {
  const currentPath = route.path;
  const queryParams = new URLSearchParams();

  // 将当前的 query 参数添加到 URLSearchParams 中，排除微信回调参数
  Object.keys(route.query).forEach(key => {
    // 排除微信回调相关的参数
    if (key !== "code" && key !== "state") {
      if (route.query[key] !== null && route.query[key] !== undefined) {
        queryParams.append(key, route.query[key]);
      }
    }
  });

  // 如果有 query 参数，则构建完整路径
  const queryString = queryParams.toString();
  return queryString ? `${currentPath}?${queryString}` : currentPath;
});

// 检测是否有微信回调参数且未处理
const hasWxCallbackParams = computed(() => {
  return !!(
    route.query.code &&
    route.query.state &&
    !isWxCallbackProcessed.value
  );
});

// 监听路由参数变化
watch(
  () => route.query,
  newQuery => {
    const { code, state } = newQuery;
    console.log(code, "code");
    console.log(state, "state");

    if (code && state && !isWxCallbackProcessed.value) {
      handleWxCallback(code, state);
    }
  },
  { immediate: true }
);
</script>

<template>
  <div class="containers">
    <div class="table_top">
      <el-descriptions
        class="margin-top"
        title=""
        :column="2"
        border
        style="width: 100%"
        :label-width="'200px'"
      >
        <template v-for="(item, index) in tableHeader" :key="index">
          <el-descriptions-item :label="item.label" label-align="center">
            {{ item.value }}
          </el-descriptions-item>
        </template>
      </el-descriptions>
    </div>
    <div class="table_content">
      <el-scrollbar class="scrollbar">
        <el-form ref="formRef" :model="form" :rules="rules">
          <el-descriptions v-if="richFlag" title="" :column="2" border>
            <el-descriptions-item
              v-for="(item, index) in formData"
              :key="index"
              label-align="center"
              :span="
                item.prop === 'phone'
                  ? formData.some(i => i.prop === 'codenumb')
                    ? 1
                    : 2
                  : item.prop === 'codenumb'
                    ? 1
                    : 2
              "
            >
              <template #label>
                <span v-if="item.check" class="star">*</span>{{ item.label }}
              </template>
              <el-form-item
                :prop="item.prop"
                :inline-message="true"
                style="margin-bottom: 0"
                :show-message="true"
                error-placement="right"
              >
                <!-- input输入 -->
                <template v-if="item.type === 'input'">
                  <el-input
                    v-model.trim="form[item.prop]"
                    :placeholder="item.placeholder"
                    :style="{ width: item.width }"
                    :disabled="
                      (item.prop === 'phone' || item.prop === 'idNumber') &&
                      item.isView &&
                      oldData[item.prop]?.length > 0
                    "
                    :maxlength="item.maxLength"
                    :show-word-limit="item.maxLength"
                  >
                    <template
                      v-if="
                        (item.prop === 'phone' || item.prop === 'idNumber') &&
                        form[item.prop]?.length > 0
                      "
                      #suffix
                    >
                      <el-icon
                        v-if="item.isView"
                        style="cursor: pointer"
                        @click="isViewFn(item.prop, index)"
                      >
                        <Hide />
                      </el-icon>
                      <el-icon
                        v-else
                        style="cursor: pointer"
                        @click="isViewFn(item.prop, index)"
                      >
                        <View />
                      </el-icon>
                    </template>
                  </el-input>
                  <div v-if="item.prop === 'account'" class="organ">
                    @{{ organ }}
                  </div>
                  <!-- 获取验证码 -->
                  <div v-if="item.prop === 'codenumb'" class="Vacode">
                    <el-button
                      v-countdown="{
                        value: 60,
                        callback: () => getCaptcha(form.phone, 'phone'),
                        countdownText: 's后重新获取',
                        loadingText: '发送中...'
                      }"
                    >
                      获取验证码
                    </el-button>
                  </div>
                </template>

                <!-- 二维码展示 -->
                <template v-else-if="item.type === 'img'">
                  <span v-if="!form.isBindWx" class="isQR"> 当前未绑定 </span>
                  <div v-else>
                    <span>已绑定,
                      <el-link
                        type="primary"
                        underline="hover"
                        @click="showUnbindDialog = true"
                      >
                        解绑
                      </el-link>
                    </span>
                  </div>
                  <div v-if="!form.isBindWx" class="codeQR">
                    <!-- 只有在没有微信回调参数时才显示二维码 -->
                    <WxQrCode
                      v-if="!hasWxCallbackParams"
                      ref="wxQrCodeRef"
                      :redirectPath="redirectPathWithQuery"
                    />
                    <!-- 有微信回调参数时显示处理中状态 -->
                    <!-- <div v-else class="processing-status">
                      <el-icon class="is-loading">
                        <Loading />
                      </el-icon>
                      <span>正在处理微信绑定...</span>
                    </div> -->
                  </div>
                </template>

                <!-- 示例：上传组件 -->
                <template v-else-if="item.type === 'upload'">
                  <el-upload
                    action="#"
                    :show-file-list="false"
                    class="upload-demo"
                    :http-request="() => {}"
                    accept="image/*,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,"
                    :before-upload="file => beforeUpload(file, item.prop)"
                  >
                    <img :src="uploadImg" alt="">
                  </el-upload>
                  <template
                    v-for="(item2, index2) in formFile[item.prop]"
                    :key="index2"
                  >
                    <FileItem
                      isNeedDelte
                      :data="item2"
                      :index="index2"
                      style="width: 100%; min-width: 130px"
                      @delete="getDeleted(item.prop, index2)"
                    />
                    <!-- <div v-show="item2?.fileName" class="fileOther">
                    <div class="title">附件</div>
                    <img class="link" src="@/assets/login/link.png" alt="" />
                    <div class="fileName">{{ item2?.fileName }}</div>
                    <img
                      class="linkDetele"
                      src="@/assets/login/linkDetele.png"
                      alt=""
                      @click="handleClickDetele(item.prop, index2)"
                    />
                  </div> -->
                  </template>
                  <div class="upload_text">{{ item.text }}</div>
                </template>
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>
        </el-form>
      </el-scrollbar>
      <div class="table_bottom">
        <el-button type="default" @click="cancelForm(route.query?.id)">
          取消
        </el-button>
        <el-button type="primary" @click="submitForm"> 保存 </el-button>
      </div>
    </div>

    <!-- 解绑微信确认对话框 -->
    <el-dialog
      v-model="showUnbindDialog"
      title="解绑微信"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="dialog-content">
        <span>确定要解绑当前微信账号吗？</span>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showUnbindDialog = false">取消</el-button>
          <el-button
            type="primary"
            :loading="unbindWxLoading"
            @click="handleChangeWx"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.scrollbar {
  height: calc(100vh - 348px);
  background-color: #fff;
  margin-bottom: 20px;
}
.containers {
  box-sizing: border-box;
  width: calc(100% - 48px);
  height: 100%;
  background: #f0f2f5;

  .table_top {
    box-sizing: border-box;
    padding: 20px;
    background-color: #fff;
  }

  .table_content {
    white-space: nowrap;
    box-sizing: border-box;
    padding: 20px;
    margin-top: 20px;
    background-color: rgb(255 255 255);

    .star {
      margin-right: 3px;
      color: red;
    }
    .organ {
      margin-left: 20px;
    }
    .el-descriptions-item {
      display: flex;
      align-items: center;
    }

    .Vacode {
      margin-left: 20px;
    }

    .isQR {
      margin-right: 240px;
    }

    .codeQR {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 20px 0;
    }

    .processing-status {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 10px;
      padding: 20px;
      color: #666;
      font-size: 14px;
    }
  }

  .table_bottom {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
}
.upload-demo {
  display: flex;
  align-items: center;
  width: 100%;
}
.fileOther {
  // margin-top: 26px;
  margin-left: 20px;
  display: flex;
  align-items: center;
  line-height: 22px;
  // width: 420px;
  // margin-bottom: 56px;
  .title {
    color: #0e1832;
    font-family: Source Han Sans CN;
    font-size: 16px;
    font-weight: 400;
    height: 24px;
    white-space: nowrap;
  }
  .link {
    width: 20px;
    height: 20px;
    margin: 0 10px;
    cursor: pointer;
  }
  .fileName {
    color: #3876fd;
    font-family: Source Han Sans CN;
    font-size: 16px;
    font-weight: 400;
    height: 24px;
    cursor: pointer;
    // width: 350px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .linkDetele {
    width: 20px;
    height: 20px;
    margin-left: 10px;
    cursor: pointer;
  }
}
:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  background: #e1f5ff;
  //   width: 240px;
}

:deep(.my-label) {
  background: var(--el-color-success-light-9) !important;
}

:deep(.my-content) {
  background: var(--el-color-danger-light-9);
}
:deep(.el-form-item__content) {
  .upload_text {
    display: block;
    font-size: 12px;
    position: relative;
    top: 5px;
    color: #8c939d;
  }
}
.el-link {
  line-height: 1.2;
  margin-bottom: 2px;
}
</style>
