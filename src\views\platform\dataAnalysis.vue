<script setup>
import { ref, reactive, onMounted, onUnmounted } from "vue";
import activeImg from "@/assets/dataAnalysis/active.png";
import courseImg from "@/assets/dataAnalysis/course.png";
import platformImg from "@/assets/dataAnalysis/plateform.jpg";
import institutionImg from "@/assets/dataAnalysis/institution.png";
import userImageImg from "@/assets/dataAnalysis/user.png";
import { urlInfo } from "@/utils/http/base.js";
import { useRouter } from "vue-router";
const router = useRouter();
const loading = ref(false);
const showToast = ref(false);
const toastMessage = ref("");
const chartBars = ref([]);
let chartInterval = null;

// 仪表板数据
const dashboards = reactive([
  {
    id: 1,
    title: "用户行为分析驾驶舱",
    // bgColor: "from-blue-900 to-blue-700",
    bgColor: "bg-black/4",
    status: "99.9%",
    type: "active",
    img: activeImg,
    description: "监测师资队伍结构分布和变化趋势"
  },
  {
    id: 2,
    title: "课程运营驾驶舱",
    // bgColor: "from-teal-900 to-teal-700",
    bgColor: "bg-black/4",
    status: "98.7%",
    type: "course",
    img: courseImg,
    description: "跟踪师资均衡配置和交流轮岗情况"
  },
  {
    id: 3,
    title: "平台运营驾驶舱",
    // bgColor: "from-purple-900 to-purple-700",
    bgColor: "bg-black/4",
    status: "99.2%",
    type: "palteform",
    img: platformImg,
    description: "监控局任干部履职和绩效情况"
  },
  {
    id: 4,
    title: "机构运营驾驶舱",
    // bgColor: "from-blue-900 to-blue-700",
    bgColor: "bg-black/4",
    status: "97.8%",
    type: "institution",
    img: institutionImg,
    description: "分析学校中层管理人员工作状态"
  },
  {
    id: 5,
    title: "用户画像分析驾驶舱",
    // bgColor: "from-blue-900 to-blue-700",
    bgColor: "bg-black/4",
    status: "99.5%",
    type: "userImage",
    img: userImageImg,
    description: "追踪教师专业发展和成长轨迹"
  }
]);

// 生成图表数据
const generateChartData = () => {
  chartBars.value = Array.from({ length: 8 }, () => Math.random() * 60 + 20);
};

// 显示提示消息
const showToastMessage = message => {
  toastMessage.value = message;
  showToast.value = true;
  setTimeout(() => {
    showToast.value = false;
  }, 3000);
};
const urlSwitch = type => {
  switch (type) {
    case "active":
      return urlInfo.activeUrl;
      break;
    case "course":
      return urlInfo.courseUrl;
      break;
    case "palteform":
      return urlInfo.platformUrl;
      break;
    case "institution":
      return urlInfo.institutionUrl;
      break;
    default:
      return urlInfo.userImageUrl;
  }
};
// 处理仪表板点击
const handleDashboardClick = async dashboard => {
  // console.log("🍧dashboard------------------------------>", dashboard);
  // const url = urlSwitch(dashboard.type);
  // window.open(url, "_blank");
  // router.replace({
  //   path: "/data/analysis/bi",
  //   query: { type: dashboard.type }
  // });
  // return
  // // 获取当前host
  const host = `${window.location.origin}${window.location.pathname}#/`;
  const jumpUrl = `${host}data/analysis/bi?type=${dashboard.type}`;
  window.open(jumpUrl, "_blank");
};

// 组件挂载时的初始化
onMounted(() => {
  generateChartData();

  // 定期更新图表数据，模拟实时数据
  chartInterval = setInterval(() => {
    generateChartData();
  }, 3000);
  // 模拟数据加载完成提示
  setTimeout(() => {
    showToastMessage("监测系统已就绪");
  }, 1000);
});

// 组件卸载时清理
onUnmounted(() => {
  if (chartInterval) {
    clearInterval(chartInterval);
  }
});

// 暴露给模板的方法（可选）
defineExpose({
  refreshData: generateChartData,
  navigateToDashboard: handleDashboardClick
});
</script>

<template>
  <div class="min-h-screen bg-gray-50 p-6">
    <div class="mx-auto">
      <!-- Header -->
      <div class="mb-8">
        <h1 class="text-2xl font-bold text-gray-800 mb-6">数据分析</h1>

        <!-- Tab -->
        <div class="flex items-center mb-6">
          <div class="flex items-center space-x-1">
            <div class="w-1 h-6 bg-blue-500 rounded-full" />
            <span class="text-lg font-medium text-gray-700 ml-2">驾驶舱</span>
          </div>
        </div>
      </div>

      <!-- Dashboard Grid -->
      <div class="grid-container">
        <div
          class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
        >
          <div
            v-for="dashboard in dashboards"
            :key="dashboard.id"
            class="group cursor-pointer hover:shadow-lg transition-all duration-300 border-0 bg-white rounded-xl overflow-hidden shadow-sm"
            @click="handleDashboardClick(dashboard)"
          >
            <div class="p-0">
              <div class="relative">
                <!-- Dashboard Preview -->
                <div
                  :class="`h-48 bg-gradient-to-br ${dashboard.bgColor} relative overflow-hidden`"
                >
                  <!-- Simulated Dashboard Content -->
                  <div class="absolute inset-0 img-container">
                    <img :src="dashboard.img" alt="" class="data-img">
                  </div>

                  <!-- Hover Overlay -->
                  <div
                    class="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center"
                  >
                    <div
                      class="text-white text-sm font-medium bg-black/30 px-3 py-1 rounded-full backdrop-blur-sm"
                    >
                      点击进入
                    </div>
                  </div>
                </div>

                <!-- Title -->
                <div class="p-4">
                  <h3
                    class="text-sm font-medium text-gray-800 text-center leading-tight font-title"
                  >
                    {{ dashboard.title }}
                  </h3>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <Teleport to="body">
        <div
          v-if="loading"
          class="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
        >
          <div
            class="bg-white rounded-lg p-6 flex items-center space-x-3 shadow-xl"
          >
            <div
              class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"
            />
            <span class="text-gray-700">正在加载驾驶舱...</span>
          </div>
        </div>
      </Teleport>
    </div>
  </div>
</template>

<style scoped>
/* 自定义动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.group:hover .bg-blue-400\/50 {
  animation: fadeInUp 0.3s ease-out;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式网格调整 */
@media (max-width: 640px) {
  .grid {
    grid-template-columns: 1fr;
  }
}

@media (min-width: 641px) and (max-width: 1024px) {
  .grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1025px) and (max-width: 1280px) {
  .grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1281px) {
  .grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 卡片悬停效果增强 */
.group:hover {
  transform: translateY(-2px);
}

/* 加载动画优化 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 脉冲动画 */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Ping 动画 */
@keyframes ping {
  75%,
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

.animate-ping {
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}
.img-container {
  width: 100%;
  height: 100%;
  border-radius: 5px;
  box-sizing: border-box;
  padding: 14px 30px;
}

.data-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 5px;
}
.font-title {
  font-weight: bold;
}
.grid-container {
  width: 80%;
}
</style>
