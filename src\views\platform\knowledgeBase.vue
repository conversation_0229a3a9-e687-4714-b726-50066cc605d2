<script setup>
import { ref, onMounted } from "vue";
import {
  dataStageList,
  TextbookList,
  dataCatalogList,
  knowledgePointList
} from "@/api/knowledgePoints.js";
import { requestTo } from "@/utils/http/tool";
import { PlusPopover } from "plus-pro-components";
import { useRole } from "./utils/knowledgeBase.jsx";

const { primarySchool, juniorHighSchool, seniorHighSchool, middleSchool } =
  useRole();
onMounted(() => {
  getKList();
});
const dataStage = ref([]);
const activeName = ref("小学");
const dataContent = ref([
  {
    name: "教材",
    id: 1
  },
  {
    name: "知识点",
    id: 2
  }
]);
const getKList = async () => {
  const [err, res] = await requestTo(dataStageList());
  dataStage.value = res;
  activeName.value = res[0]?.id;

  // 初始页面默认值
  await subjectListClick(subjectList.value[0], res[0]);
};

const dataList = ref([]);
const dataList2 = ref([]);

const defaultProps = {
  children: "children",
  label: "name"
};
const defaultProps2 = {
  children: "children",
  label: "name"
};

// 获取学科列表
const subjectList = ref(primarySchool);
const getXueList = value => {
  active3.value = "";
  switch (value.props.name) {
    case 2:
      return (subjectList.value = primarySchool);
    case 3:
      return (subjectList.value = juniorHighSchool);
    case 4:
      return (subjectList.value = seniorHighSchool);
    case 6:
      return (subjectList.value = middleSchool);
    default:
      return;
  }
};
// 获取版本
const versionList = ref([]);
const btnStr = ref("请选择");
const subjectListValue = ref([]);
const active1 = ref();
const active2 = ref();
const active3 = ref(1);
const subjectListClick = async (items, item) => {
  versionList.value = [];
  ereaList.value = [];
  dataList.value = [];
  dataList2.value = [];
  valueVersion1.value = "请选择";
  subjectListValue.value = [items, item];
  const [err, res] = await requestTo(
    TextbookList({ stageId: item.id, subjectId: items.id })
  );
  versionList.value = res;
  active3.value = items.id;
  if (res.length > 0) {
    handversionListClick(res[0]);
  }
  btnStr.value = `${item.name}${items.name}`;
  if (activeName2.value === 2) {
    knowledgePoints();
  }
};
// 获取册别
const ereaList = ref([]);
const valueVersion = ref("");
const handversionListClick = async item => {
  ereaList.value = item.textbooks;
  valueVersion.value = item;
  active1.value = item.id;
  if (item.textbooks.length > 0) {
    handereaListListClick(item.textbooks[0]);
  }
};
// 列表回显
const valueVersion1 = ref("");
const valueVersion2 = ref();
const handereaListListClick = async item => {
  valueVersion1.value = `${valueVersion.value.name}${item.volume}`;
  valueVersion2.value = item;
  active2.value = item.id;
  Materials();
};
// 回显节点
const activeName2 = ref(1);
const getXueList2 = value => {
  if (value.props.name === 1 && versionList.value.length > 0) {
    Materials();
  } else if (value.props.name === 2) {
    knowledgePoints();
  }
};
// 教材列表
const Materials = async () => {
  dataList.value = [];
  if (!valueVersion2.value?.id) return;
  const [err, res] = await requestTo(
    dataCatalogList({ textbookId: valueVersion2.value?.id })
  );
  if (!valueVersion1.value) {
    dataList.value = res;
  } else {
    dataList.value = [
      {
        name: valueVersion1.value,
        id: valueVersion.value.id,
        children: [...res]
      }
    ];
  }
};
// 知识点列表
const knowledgePoints = async () => {
  dataList2.value = [];
  if (subjectListValue.value.length === 0) return;
  const [err, res] = await requestTo(
    knowledgePointList({
      stageId: subjectListValue.value[1].id,
      subjectId: subjectListValue.value[0].id
    })
  );
  dataList2.value = res;
};
</script>

<template>
  <div class="knowledge-base">
    <div class="con_search">
      <div>
        <PlusPopover
          placement="bottom-start"
          trigger="hover"
          title=""
          :width="500"
        >
          <template v-if="dataStage.length > 0">
            <el-tabs
              v-model="activeName"
              class="demo-tabs"
              @tab-click="getXueList"
            >
              <el-tab-pane
                v-for="item in dataStage"
                :key="item.id"
                :label="item.name"
                :name="item.id"
              >
                <div class="box-item-content">
                  <div
                    v-for="items in subjectList"
                    :key="items.id"
                    class="content-item"
                    :class="active3 === items.id ? 'active1' : ''"
                    @click="subjectListClick(items, item)"
                  >
                    {{ items.name }}
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </template>
          <template v-else>暂无数据</template>
          <template #reference>
            <el-button type="primary" class="mgb-10 mgr-10">
              {{ btnStr || "请选择" }}
            </el-button>
          </template>
        </PlusPopover>
      </div>
      <div v-if="activeName2 !== 2">
        <PlusPopover
          placement="bottom-start"
          trigger="hover"
          title=""
          :width="versionList.length > 30 ? 800 : 500"
        >
          <div class="box-item-content2">
            <div class="content-item2">
              <div class="font-bold ht-30">版本</div>
              <template v-if="versionList.length > 0">
                <div
                  v-for="item in versionList"
                  :key="item.id"
                  class="content-box"
                  :class="active1 === item.id ? 'active' : ''"
                  @click="handversionListClick(item)"
                >
                  {{ item.name }}
                </div>
              </template>
              <template v-else>
                <div style="color: #919191">暂无数据</div>
              </template>
            </div>
            <div class="content-item2">
              <div class="font-bold ht-30">册别</div>
              <template v-if="versionList.length > 0">
                <div
                  v-for="item in ereaList"
                  :key="item.id"
                  class="content-box"
                  :class="active2 === item.id ? 'active' : ''"
                  @click="handereaListListClick(item)"
                >
                  {{ item.volume }}
                </div>
              </template>
              <template v-else>
                <div style="color: #919191">暂无数据</div>
              </template>
            </div>
          </div>
          <template #reference>
            <el-button type="primary" class="mgb-10 mgr-10">
              {{ valueVersion1 || "请选择" }}
            </el-button>
          </template>
        </PlusPopover>
      </div>
    </div>
    <div class="con_content">
      <el-scrollbar height="100% background-color: red">
        <el-tabs
          v-model="activeName2"
          class="demo-tabs"
          @tab-click="getXueList2"
        >
          <el-tab-pane
            v-for="item in dataContent"
            :key="item.id"
            :label="item.name"
            :name="item.id"
          >
            <el-tree
              v-if="activeName2 === 1"
              style="max-width: 600px"
              :data="dataList"
              :props="defaultProps"
            />
            <el-tree
              v-if="activeName2 === 2"
              style="max-width: 600px"
              :data="dataList2"
              :props="defaultProps2"
            />
          </el-tab-pane>
        </el-tabs>
      </el-scrollbar>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.knowledge-base {
  display: flex;
  flex-direction: column;
  .con_search {
    background-color: #fff;
    padding: 10px 20px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    width: 100%;
    height: fit-content;
    gap: 40px;
  }
  .con_content {
    width: 100%;
    height: calc(100vh - 19vh);
    background-color: #fff;
    padding: 20px;
  }
}
.box-item-content {
  display: flex;
  width: 100%;
  height: 100%;
  padding: 10px;
  // background-color: red;
  flex-wrap: wrap;
  gap: 16px;
  padding: 16px;
}
.content-item {
  flex: 0 0 calc(33.333% - 10.666px);
  cursor: pointer;
}
.content-item:hover {
  color: rgb(0, 162, 255);
}
.box-item-content2 {
  .content-item2 {
    width: 100%;
    // background-color: rgba(255, 0, 0, 0.438);
    display: flex;
    flex-wrap: wrap;

    .content-box {
      padding: 2px 10px;
      margin: 3px;
      border: 1px solid #e3e3e3;
      border-radius: 4px;
      cursor: pointer;
    }
    .ht-30 {
      height: 30px;
      width: 100%;
      line-height: 30px;
    }
  }
}

:deep(.el-tabs__nav-wrap::after) {
  display: none !important;
}

.active {
  background-color: #48a6ff;
  color: #fff;
}
.active1 {
  color: rgb(0, 162, 255) !important;
  font-weight: 600;
}
</style>
