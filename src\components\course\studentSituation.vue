<script setup>
import { onMounted, ref } from "vue";
import { formatTime } from "@/utils/index";
import {
  studentSituationFindAll,
  refundSubByStudent,
  getBuyMaterialCount
} from "@/api/course.js";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox } from "element-plus";
import { View, Hide } from "@element-plus/icons-vue";
import { useRouter, useRoute } from "vue-router";
import { decrypt, encryption } from "@/utils/SM4.js";
import { isEmpty, to } from "@iceywu/utils";
const props = defineProps({
  maxPeopleNumber: {
    type: Number,
    default: 0
  },
  purchaseNumber: {
    type: Number,
    default: 0
  },
  minPeopleNumber: {
    type: Number,
    default: 0
  }
});
const emit = defineEmits(["tabShow", "homework", "relatedOrder"]);
const router = useRouter();
const route = useRoute();
onMounted(() => {
  getTableList();
  getBuyMaterialCountApi();
  // tableData.value = Array.from({ length: 20 }).fill({
  //   completed_at: "0",
  //   pending_review: 1,
  //   createdAt: 172627952334,
  //   name: "2024-09-14习题任务",
  //   termNumber: 1,
  //   courseTypeName: "手工",
  //   state: 1,
  //   organizationName: "2024-09-14习题任务",
  //   freeze: 1
  // });
});
// 表格数据
const tableData = ref([]);
const params = ref({
  page: 1,
  size: 15,
  sort: "createdAt,desc",
  totalElements: 0
});
const buyersNumber = ref(0);
const getBuyMaterialCountApi = async () => {
  const [err, res] = await to(
    getBuyMaterialCount({ coursePeriodId: +route.query.periodId })
  );
  if (res.code === 200) {
    buyersNumber.value = res.data.count || 0;
  }
};
// 获取列表信息
const getListLoading = ref(false);
const getTableList = async data => {
  if (getListLoading.value) {
    return;
  }
  getListLoading.value = true;
  let paramsData = {
    page: params.value.page - 1,
    size: params.value.size,
    sort: params.value.sort,
    coursePeriodId: route.query.periodId
  };
  console.log("🍧-----paramsData-----", paramsData);
  const [err, result] = await requestTo(studentSituationFindAll(paramsData));
  // console.log("🎁-----result-----", result);
  if (result) {
    tableData.value = result?.content;

    params.value.totalElements = result.totalElements || 0;
  } else {
    console.log("没有数据");
  }
  if (err) {
    ElMessage.error(err);
  }
  getListLoading.value = false;
};
//分页
const handleCurrentChange = e => {
  params.value.page = e;
  getTableList();
};
const handleSizeChange = val => {
  params.value.page = 1;
  params.value.size = val;
  getTableList();
};
// 查看实践感悟情况
const workLookEvt = row => {
  let homeworkData = {
    studentId: row?.studentId || "",
    homeworkId: row?.homeworkId || "",
    id: row?.id || "",
    showcontent: "homework"
  };
  emit("tabShow", false);
  emit("homework", homeworkData);
};
// 查看关联订单
const associationEvt = row => {
  // console.log('🐳row------------------------------>',row);
  // return
  router.replace({
    path: "/course/orderManagement/orderDetail",
    query: {
      id: row?.ordersId || "",
      type: "course",
      periodId: route.query.periodId
    }
  });
  // let orderData = {
  //   ordersId: row?.ordersId || "",
  //   id: row?.id || "",
  //   showcontent: "order"
  // };
  // emit("tabShow", false);
  // emit("relatedOrder", orderData);
};
// 退单
const chargebackEvt = row => {
  ElMessageBox.confirm(`确定要退单吗？`, "确认退单", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(() => {
      isChargebackApi(row);
    })
    .catch(() => {
      // ElMessage({
      //   type: 'info',
      //   message: 'Delete canceled',
      // })
    });
};
const eye_phone = id => {
  const item = tableData.value.find(item => item.id === id);
  if (item) {
    item.type_phone = !item.type_phone;
  }
};
const isChargebackApi = async row => {
  const operateLog = {
    operateLogType: "ORDER_MANAGEMENT",
    operateType: `退了“${row.coursePeriodName}”课期中的${row.studentName}学生的订单`
  };
  const params = {
    studentId: row?.studentId || "",
    ordersId: row?.ordersId || ""
  };
  const { code } = await refundSubByStudent(params, operateLog);
  if (code === 200) {
    ElMessage({
      type: "success",
      message: "退单成功"
    });
    getTableList();
  } else {
    ElMessage({
      type: "error",
      message: "退单失败"
    });
  }
};
//处理选择规格字符串
const buyGroupToString = str => {
  // console.log(str)
  try {
    // 尝试将字符串作为 JSON 解析
    return JSON.parse(str).join("+");
  } catch (error) {
    // 若解析失败，直接返回原字符串
    return str;
  }
};
</script>

<template>
  <div class="containers">
    <div class="con_table">
      <div class="people">
        <div class="account">
          报名人数<span>{{ params.totalElements || 0 }}</span>
        </div>
        <div class="account">
          购买材料费人数<span>{{ buyersNumber || 0 }}</span>
        </div>
        <div class="account">
          人数下限<span>{{
            minPeopleNumber === 0 || isEmpty(minPeopleNumber)
              ? "--"
              : minPeopleNumber
          }}</span>
        </div>
        <div class="account">
          人数上限<span>{{
            maxPeopleNumber === 0 || isEmpty(maxPeopleNumber)
              ? "--"
              : maxPeopleNumber
          }}</span>
        </div>
      </div>
      <el-table
        :data="tableData"
        table-layout="fixed"
        :header-cell-style="{ backgroundColor: '#fafafa', color: '#565353' }"
        highlight-current-row
        :style="{ height: '100%' }"
      >
        <el-table-column
          prop="studentName"
          label="姓名"
          align="left"
          width="130px"
          fixed
        >
          <template #default="scope">
            <div>
              {{ scope.row.studentName || "--" }}
            </div>
          </template>
        </el-table-column>

        <el-table-column width="130px" prop="parentName" label="家长">
          <template #default="scope">
            <div>
              {{ scope.row.parentName || "--" }}
              <!-- {{ formatTime(scope.row?.createdAt, "YYYY-MM-DD HH:mm") || "--" }} -->
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="parentPhone"
          label="家长电话"
          align="left"
          width="230"
        >
          <template #default="scope">
            <div class="eye_style">
              {{
                scope.row.parentPhoneCt
                  ? scope.row.type_phone
                    ? decrypt(scope.row.parentPhone)
                    : scope.row.parentPhoneCt
                  : "--"
              }}
              <div
                v-if="scope.row.parentPhoneCt"
                class="eye"
                @click="eye_phone(scope.row.id, scope.row.parentPhone)"
              >
                <el-icon v-if="!scope.row.type_phone"><Hide /></el-icon>
                <el-icon v-else><View /></el-icon>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="payTime"
          label="付款时间"
          align="left"
          min-width="200"
        >
          <template #default="scope">
            <div>
              {{
                formatTime(scope.row?.payTime, "YYYY-MM-DD HH:mm:ss") || "--"
              }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          width="200"
          show-overflow-tooltip
          prop="material"
          label="是否购买材料费"
          align="left"
        >
          <template #default="scope">
            {{ scope.row?.material === true ? "是" : "否" }}
          </template>
        </el-table-column>
        <el-table-column prop="signIn" label="签到情况" align="left">
          <template #default="scope">
            <div>
              {{ scope.row?.signIn === true ? "已签到" : "未签到" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="homeworkId"
          label="实践感悟情况"
          align="left"
          fixed="right"
        >
          <template #default="{ row }">
            <div class="option">
              <div
                v-if="row.homeworkId"
                class="btnse1"
                @click="workLookEvt(row)"
              >
                查看
              </div>
              <div v-else>未交</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" align="left" width="200px">
          <template #default="{ row }">
            <div class="option">
              <div class="btnse" @click="associationEvt(row)">查看关联订单</div>
              <div
                v-if="
                  row.subOrdersStatus === 'COMPLETED' ||
                  row.subOrdersStatus === 'PAID'
                "
                class="btnse"
              >
                <div @click="chargebackEvt(row)">退单</div>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="con_pagination">
      <!-- 分页 -->
      <el-pagination
        v-model:current-page="params.page"
        v-model:page-size="params.size"
        class="pagination"
        background
        :page-sizes="[15, 30, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="params.totalElements"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.containers {
  height: 100%;
  display: flex;
  flex-direction: column;
  //   box-sizing: border-box;
  //   width: calc(100% - 48px);
  //   height: 100%;
  //   padding: 24px;
  //   background: #fff;

  .con_search {
    display: flex;
    align-items: center;
    width: 100%;
    height: fit-content;
    margin-bottom: 12px;
  }

  .con_table {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0;
    // width: calc(100% - 45px);
    // margin-bottom: 24px;
    // margin-left: 25px;
    // width: calc(100% - 25px);
    width: 100%;
    margin: 10px 0 12px 0;
    .people {
      display: flex;
      margin-bottom: 20px;
      .account {
        margin-right: 60px;
        span {
          margin-left: 30px;
          font-weight: bold;
        }
      }
    }

    .option {
      display: flex;

      .btnse {
        display: flex;
        margin-left: 16px;
        color: #409eff;
        cursor: pointer;
      }
      .btnse1 {
        display: flex;
        // margin-left: 16px;
        color: #409eff;
        cursor: pointer;
      }
    }
  }

  .con_pagination {
    display: flex;
    justify-content: flex-end;
    width: 100%;
  }
  .eye_style {
    display: flex;
    align-items: center;
    // justify-content: center;
    .eye {
      margin-left: 20px;
      cursor: pointer;
      :hover {
        color: #409eff;
      }
    }
  }
}
</style>
