import { http } from "@/utils/http";

// 师资审核
// 列表查询
export const findall = params => {
  return http.request(
    "get",
    "/platform/expertApply/findAll",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};

// 根据id查询审核详情
export const findSnapshotByApplyId = params => {
  return http.request(
    "get",
    "/platform/expertApply/findSnapshotByApplyId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};

// 根据申请Id修改详情
export const findUpdateDetailByApplyId = params => {
  return http.request(
    "get",
    "/platform/expertApply/findUpdateDetailByApplyId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};

// 根据applyId查询详情
export const findByExpertApplyId = params => {
  return http.request(
    "get",
    "/platform/expertApply/findByExpertApplyId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};

//审核申请
export const reviewApplication = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/expertApply/audit",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "TEACHER_DATABASE",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
