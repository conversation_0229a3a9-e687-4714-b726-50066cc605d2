import { http } from "@/utils/http";
/** 局端列表查询 */
export const localEndFindAll = params => {
  return http.request(
    "get",
    "/platform/educationBureau/findAll",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 是否冻结
export const localEndIsFreeze = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/educationBureau/isFreeze",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ACCOUNT_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
// 新增
export const localEndAdd = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/educationBureau/save",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ACCOUNT_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
// 修改
export const localEndUpdate = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/educationBureau/update",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ACCOUNT_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
// 重置密码
export const localEndPassword = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/educationBureau/resetPassword",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ACCOUNT_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
/** 局端列表查询 */
export const localEndFindById = params => {
  return http.request(
    "get",
    "/platform/educationBureau/findById",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
//新增局端日志
export const operateLogSaveLocalEnd = data => {
  return http.request(
    "post",
    "/educationBureau/operateLog/saveBureauOperateLog",
    { data },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
// 局端课程审核
export const localEndApplyReview = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/educationBureau/apply/review",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
/** 局端分页查询审核 */
export const localEndFindReview = params => {
  return http.request(
    "get",
    "/educationBureau/apply/findReview",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
/** 局端根据ApplyId查询详情 */
export const localEndFindByApplyId = params => {
  return http.request(
    "get",
    "/educationBureau/apply/findByApplyId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
