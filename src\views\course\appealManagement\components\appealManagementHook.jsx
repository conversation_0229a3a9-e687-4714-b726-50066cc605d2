import { onMounted, ref, computed, h } from "vue";
import {
  ElMessage,
  ElMessageBox,
  ElImage,
  ElButton,
  ElDialog,
  ElTag,
  ElTooltip
} from "element-plus";
import { requestTo } from "@/utils/http/tool";
import {
  appealHandle, // 分页查询申诉
  appealHandleFind, // 处理申诉
  appealHandleId // 查询申诉详情
} from "@/api/appealManagement.js";
import { Warning } from "@element-plus/icons-vue";
import { formatTime } from "@/utils/index";
import { render } from "nprogress";
import { useRouter } from "vue-router";

export const useAppealManagement = () => {
  const router = useRouter();
  // tab选项
  const activeTab = ref(0);
  const tabOptions = ref([
    { id: 0, name: "待处理", value: "PENDING" },
    { id: 1, name: "已处理", value: "PROCESSED" }
  ]);

  // 控制批量按钮显示
  const appealObj = ref({
    tabId: 0,
    state: "PENDING",
    showBatchAudit: true
  });

  // 搜索表单
  const searchForm = ref({
    content: ""
  });

  // 表格数据
  const tableData = ref([]);
  const loading = ref(false);

  // 分页
  const pagination = ref({
    current: 1,
    pageSize: 10,
    total: 0
  });

  // 多选
  const selectedRows = ref([]);
  const isSelectionMode = ref(false);

  // 详情弹窗
  const appealDetailVisible = ref(false);
  const currentAppealDetail = ref(null);

  // 处理弹窗
  const showAuditDialog = ref(false);
  const currentAuditData = ref(null);

  // 搜索表单配置
  const searchColumns = ref([
    {
      label: "内容",
      prop: "content",
      valueType: "input",
      fieldProps: {
        placeholder: "请输入内容搜索",
        clearable: true
      }
    }
  ]);

  // 渲染处理状态
  const renderProcessingStatus = row => {
    if (!row) return h("span", "暂无状态");

    const tip = row.repliesContent || "暂无处理意见";

    return h(
      ElTooltip,
      {
        content: tip,
        placement: "top",
        effect: "light",
        showAfter: 100,
        popperStyle: {
          backgroundColor: "#fff",
          color: "#303133",
          border: "1px solid #ebeef5",
          maxWidth: "320px"
        }
      },
      {
        default: () =>
          h(
            "div",
            {
              class: "processing-status",
              style: {
                display: "inline-flex",
                alignItems: "center",
                gap: "4px",
                color: "#409eff",
                whiteSpace: "nowrap",
                cursor: "default"
              }
            },
            [
              h("span", { style: { lineHeight: "20px" } }, "已处理"),
              h(Warning, {
                style: {
                  width: "18px",
                  height: "18px",
                  color: "#f56c6c",
                  flex: "none",
                  verticalAlign: "middle"
                }
              })
            ]
          )
      }
    );
  };

  // 申诉详情
  const listImg = ["jpg", "jpeg", "png", "gif", "bmp", "webp", "svg"];
  const handleViewAppealDetail = async row => {
    const [err, res] = await requestTo(appealHandleId({ id: row.id }));
    if (!err && res) {
      currentAppealDetail.value = { ...res, images: [], videos: [] };
      if (currentAppealDetail.value?.files?.length > 0) {
        currentAppealDetail.value.files.map(item => {
          let list = item.uploadFile.url.split(".");
          if (listImg.includes(list[list.length - 1])) {
            currentAppealDetail.value.images.push(item.uploadFile.url);
          } else {
            currentAppealDetail.value.videos.push(item.uploadFile.url);
          }
        });
      }
      appealDetailVisible.value = true;
    } else {
      ElMessage.error("获取详情失败");
    }
  };
  const handleCloseAppealDetail = () => {
    appealDetailVisible.value = false;
    currentAppealDetail.value = null;
  };

  // 渲染申诉内容摘要
  const renderAppealContentSummary = row => {
    if (!row) return h("span", "暂无内容");

    const elements = [];

    // 添加文字内容
    if (row) {
      elements.push(h("div", { class: "appeal-text-summary" }, row.content));
    }

    // 添加查看详情按钮
    elements.push(
      h(
        ElButton,
        {
          type: "text",
          size: "small",
          style: { marginTop: "4px" },
          onClick: () => handleViewAppealDetail(row) // 传 row
        },
        () => "查看详情"
      )
    );

    return h("div", { class: "appeal-content-summary" }, elements);
  };

  // 单条处理：点击处理按钮只弹窗
  const handleProcess = row => {
    // 始终赋值，避免响应式丢失
    currentAuditData.value = { ...row };
    showAuditDialog.value = false;
    // nextTick保证弹窗关闭后再打开，避免同一数据多次点击不弹窗
    setTimeout(() => {
      showAuditDialog.value = true;
    }, 0);
  };
  const actionButton = row => {
    const buttons = [];

    // 根据 activeTab 决定第一个按钮的内容
    if (activeTab.value === 1) {
      // 已处理tab - 显示已处理状态
      buttons.push(renderProcessingStatus(row));
    } else {
      // 待处理tab - 显示处理按钮
      buttons.push(
        h(
          ElButton,
          {
            type: "text",
            size: "small",
            style: {
              padding: "4px 0px",
              fontSize: "14px",
              height: "24px",
              lineHeight: "1"
            },
            onClick: () => handleProcess(row)
          },
          () => "处理"
        )
      );
    }

    // 其他按钮保持不变
    buttons.push(
      h(
        ElButton,
        {
          type: "text",
          size: "small",
          style: {
            padding: "4px 0px",
            fontSize: "14px",
            height: "24px",
            lineHeight: "1"
          },
          onClick: () => handleViewAppealDetail(row)
        },
        () => "申诉详情"
      )
    );

    return h(
      "div",
      {
        style: {
          display: "inline-flex",
          alignItems: "center",
          gap: "4px",
          whiteSpace: "nowrap",
          justifyContent: "center",
          fontSize: "14px"
        }
      },
      buttons
    );
  };

  // 表格列配置
  const columns = computed(() => [
    {
      label: "申诉人",
      prop: "appealName",
      width: 100,
      align: "center"
    },
    {
      label: "申诉内容",
      prop: "content",
      minWidth: 300,
      showOverflowTooltip: false
      // render: (cellValue, row) => renderAppealContentSummary(row.row)
    },
    {
      label: "申诉订单",
      prop: "orderNo",
      width: 180,
      align: "center",
      render: (h, scope) => {
        const row = scope.row;
        const courseName = row.orderNo || "";
        const courseId = row.ordersId || "";
        return (
          <el-link
            underline={false}
            target="_blank"
            onClick={e => {
              e.preventDefault();
              router.push({
                path: "/appeal/management/orderDetails",
                query: { id: row.ordersId || "" }
              });
            }}
          >
            {courseName}
          </el-link>
        );
      }
    },
    {
      label: "课期名称",
      prop: "coursePeriodName",
      width: 180,
      render: (h, scope) => {
        const row = scope.row;
        const courseName = row.coursePeriodName || "";
        const courseId = row.coursePeriodId || "";
        return (
          <el-link
            underline={false}
            target="_blank"
            onClick={e => {
              e.preventDefault();
              router.push({
                path: "/appeal/management/courseDetail",
                query: { periodId: courseId, text: "course" || "" }
              });
            }}
          >
            {courseName}
          </el-link>
        );
      }
    },
    {
      label: "申诉时间",
      prop: "createdAt",
      width: 160,
      align: "center",
      render: (cellValue, row) => {
        return formatTime(row.row.createdAt, "YYYY-MM-DD HH:mm:ss") || "--";
      }
    },
    {
      label: "操作",
      prop: "operation",
      width: 160,
      align: "center",
      render: (cellValue, row) => actionButton(row.row)
    }
  ]);

  // 加载表格数据
  const loadTableData = async () => {
    loading.value = true;
    try {
      const params = {
        page: pagination.value.current - 1,
        size: pagination.value.pageSize,
        appealStatus: tabOptions.value[activeTab.value].value,
        sort: "createdAt,desc"
      };
      if (searchForm.value.content && searchForm.value.content.trim()) {
        params.content = searchForm.value.content.trim();
      }
      const [err, res] = await requestTo(appealHandle(params));
      if (!err && res.content) {
        tableData.value = res.content;
        pagination.value.total = res.totalElements || 0;
      } else {
        tableData.value = [];
        pagination.value.total = 0;
      }
    } finally {
      loading.value = false;
    }
  };

  // tab切换
  const handleTabClick = (item, index) => {
    activeTab.value = item.props ? item.props.name : index;
    if (activeTab.value === 0) {
      appealObj.value.tabId = 0;
      appealObj.value.state = "PENDING";
      appealObj.value.showBatchAudit = true;
    } else {
      appealObj.value.tabId = 1;
      appealObj.value.state = "PROCESSED";
      appealObj.value.showBatchAudit = false;
    }
    isSelectionMode.value = false;
    selectedRows.value = [];
    pagination.value.current = 1;
    loadTableData();
  };

  // 搜索
  const handleSearch = () => {
    pagination.value.current = 1;
    loadTableData();
  };

  // 重置
  const handleReset = () => {
    searchForm.value = { content: "" };
    pagination.value.current = 1;
    loadTableData();
  };

  // 分页
  const handlePageChange = page => {
    pagination.value.current = page;
    loadTableData();
  };
  const handleSizeChange = size => {
    pagination.value.pageSize = size;
    pagination.value.current = 1;
    loadTableData();
  };

  // 多选
  const handleSelectionChange = selection => {
    selectedRows.value = selection;
  };

  // 批量处理
  const handAry = ref([]);
  const handleBatchProcess = async () => {
    if (selectedRows.value.length === 0) {
      ElMessage.warning("请先选择要处理的申诉记录");
      return;
    }
    handAry.value = [];
    showAuditDialog.value = true;
    handAry.value = selectedRows.value.map(row => row.id);
  };

  // 弹窗点击确定时调用处理接口
  const handleAuditSubmit = async processingOpinion => {
    if (!processingOpinion) return;
    try {
      const operateLog = {
        operateLogType: "APPEAL",
        operateType:
          handAry.value.length > 0
            ? `批量处理了${handAry.value?.length}条申诉要求`
            : `处理了${currentAuditData.value?.appealName}的申诉要求`
      };
      const data = await appealHandleFind(
        {
          ids:
            handAry.value.length > 0
              ? handAry.value
              : [currentAuditData.value?.id],
          reason: processingOpinion || "未填写处理意见"
        },
        operateLog
      );
      if (data.code === 200) {
        ElMessage.success("处理成功");
        showAuditDialog.value = false;
        loadTableData();
      } else {
        ElMessage.error("处理失败");
        showAuditDialog.value = false;
      }
    } catch {
      ElMessage.error("处理失败");
      showAuditDialog.value = false;
    }
  };

  // 批量模式
  const handleEnableBatchMode = () => {
    isSelectionMode.value = true;
    selectedRows.value = [];
  };
  const handleCancelBatchMode = () => {
    isSelectionMode.value = false;
    selectedRows.value = [];
  };

  onMounted(() => {
    loadTableData();
  });

  return {
    activeTab,
    tabOptions,
    appealObj,
    searchForm,
    tableData,
    loading,
    pagination,
    selectedRows,
    isSelectionMode,
    searchColumns,
    columns,
    // actionBar,
    appealDetailVisible,
    currentAppealDetail,
    showAuditDialog,
    currentAuditData,
    handleTabClick,
    loadTableData,
    handleSearch,
    handleReset,
    handleProcess,
    handleEnableBatchMode,
    handleCancelBatchMode,
    handleBatchProcess,
    handleSelectionChange,
    handlePageChange,
    handleSizeChange,
    handleViewAppealDetail,
    handleCloseAppealDetail,
    handleAuditSubmit
  };
};
