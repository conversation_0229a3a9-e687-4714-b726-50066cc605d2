<script setup>
import { ref, defineEmits, computed } from "vue";
import { ElMessage } from "element-plus";

const props = defineProps({
  title: {
    type: String,
    default: "驳回理由"
  },
  dialogFormVisible: {
    type: Boolean,
    default: false
  },
  textLeftBtn: {
    type: String,
    default: "取消"
  },
  textRightBtn: {
    type: String,
    default: "确定驳回"
  },
  // 审核类型，用于区分不同的驳回理由列表
  applyType: {
    type: String,
    default: ""
  }
});

const emit = defineEmits(["reset", "update:dialogFormVisible", "confirm"]);

const reason = ref("");
const getListLoading = ref(false);
const selectedReasons = ref([]); // 用于跟踪已选择的理由

// 根据审核类型动态设置驳回理由列表
const reasonList = computed(() => {
  if (props.applyType === "COURSE_PERIOD_POSTPONE") {
    // 改期审核的驳回理由
    return [
      { id: 0, name: "不符合课程改期政策" },
      { id: 1, name: "影响用户体验" },
      { id: 2, name: "改期通知时间太短" }
    ];
  } else {
    // 默认驳回理由
    return [
      { id: 0, name: "课程信息不完整" },
      { id: 1, name: "课程内容质量问题" },
      { id: 2, name: "课程安排不合理" },
      { id: 3, name: "资质证明不齐全" },
      { id: 4, name: "课程涉嫌侵权或违规" },
      { id: 5, name: "课程定价不合理" },
      { id: 6, name: "课程存在安全隐患" },
      { id: 7, name: "营销宣传过度" }
    ];
  }
});

// 使用 computed 属性代理 prop
const localVisible = computed({
  get() {
    return props.dialogFormVisible;
  },
  set(value) {
    emit("update:dialogFormVisible", value);
  }
});

// 选择理由事件
const selectReasonEvt = (item, index) => {
  const isSelected = selectedReasons.value.some(
    selected => selected.id === item.id
  );

  if (isSelected) {
    // 如果已选择，则移除
    const index = selectedReasons.value.findIndex(
      selected => selected.id === item.id
    );
    selectedReasons.value.splice(index, 1);
  } else {
    // 如果未选择，则添加
    selectedReasons.value.push(item);
  }

  // 更新文本框内容为所有已选择理由的连接
  reason.value = selectedReasons.value
    .map(reasonItem => reasonItem.name)
    .join("，");
};

// 处理理由输入
const handleReasonInput = value => {
  reason.value = value;
  // 检查哪些预设理由在输入框中（包含逗号）
  selectedReasons.value = reasonList.value.filter(reason =>
    value.includes(reason.name + "，")
  );
};

// 确认按钮点击事件
const btnOKClick = async () => {
  if (!reason.value.trim()) {
    ElMessage.warning("请输入或选择驳回理由");
    return;
  }

  if (getListLoading.value) {
    return;
  }

  getListLoading.value = true;

  try {
    // 触发确认事件，传递驳回理由
    emit("confirm", {
      reason: reason.value
    });
  } catch (error) {
    console.error("驳回失败：", error);
  } finally {
    getListLoading.value = false;
  }
};

// 取消按钮点击事件
const cancel = () => {
  selectedReasons.value = [];
  reason.value = "";
  emit("reset");
};

// 弹窗关闭事件
const handleClose = () => {
  selectedReasons.value = [];
  reason.value = "";
};
</script>

<template>
  <el-dialog
    v-model="localVisible"
    :title="title"
    width="600"
    @close="handleClose"
  >
    <div class="content">
      <div class="reject-form">
        <div class="form-item">
          <div class="reason-tags">
            <div class="reason-tags-title">选择驳回理由：</div>
            <div class="reason-tags-content">
              <div
                v-for="(item, index) in reasonList"
                :key="index"
                :class="
                  selectedReasons.some(selected => selected.id === item.id)
                    ? 'active-item'
                    : 'select-item'
                "
                @click="selectReasonEvt(item, index)"
              >
                {{ item.name }}
              </div>
            </div>
          </div>
        </div>

        <div class="form-item">
          <label class="form-label">
            <span style="color: #f56c6c; margin-right: 4px">*</span>
            驳回理由：
          </label>
          <el-input
            v-model="reason"
            placeholder="请输入或选择驳回理由"
            type="textarea"
            resize="none"
            maxlength="200"
            show-word-limit
            :rows="4"
            @input="handleReasonInput"
          />
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">
          {{ textLeftBtn }}
        </el-button>
        <el-button :loading="getListLoading" type="danger" @click="btnOKClick">
          {{ textRightBtn }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.reject-form {
  width: 100%;
  margin: 0 auto;

  .form-item {
    margin-bottom: 20px;

    .form-label {
      display: block;
      margin-bottom: 8px;
      font-size: 14px;
      color: #333;
      font-weight: 500;
    }

    :deep(.el-textarea__inner) {
      min-height: 80px;
    }

    .reason-tags {
      margin-bottom: 20px;

      .reason-tags-title {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 10px;
        color: #333;
      }

      .reason-tags-content {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
      }

      .select-item {
        padding: 6px 12px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        cursor: pointer;
        font-size: 13px;
        transition: all 0.3s;
        background-color: #fff;

        &:hover {
          color: #409eff;
          border-color: #c6e2ff;
          background-color: #ecf5ff;
        }
      }

      .active-item {
        padding: 6px 12px;
        background-color: #409eff;
        color: #fff;
        border-radius: 4px;
        font-size: 13px;
        cursor: pointer;
        border: 1px solid #409eff;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
