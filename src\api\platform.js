import { http } from "@/utils/http";

/** 平台列表查询 */
export const platformFindAll = params => {
  return http.request(
    "get",
    "/platform/admin/findAll",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 是否冻结
export const platformIsFreeze = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/admin/isFreeze",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ACCOUNT_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
// 新增
export const platformAdd = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/admin/save",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ACCOUNT_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
// 修改
export const platformUpdate = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/admin/update",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ACCOUNT_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
// 重置密码
export const platformResetPassword = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/admin/resetPassword",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ACCOUNT_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
// 根据id查详情
export const platformfindById = params => {
  return http.request(
    "get",
    "/platform/admin/findById",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 查角色
export const platformRole = params => {
  return http.request(
    "get",
    "/platform/role/findAllNotPage",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 验证账号
export const verifyUsername = data => {
  return http.request(
    "post",
    "/platform/admin/verifyUsername",
    { data },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
// 验证手机号
export const verifyPhone = data => {
  return http.request(
    "post",
    "/platform/admin/verifyPhone",
    { data },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
