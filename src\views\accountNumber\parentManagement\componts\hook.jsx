import { onMounted, reactive, ref, onActivated } from "vue";
import {
  parentFindById,
  parentFindStudent,
  parentIsFreeze
} from "@/api/parentManage.js";
import { requestTo } from "@/utils/http/tool";
import { Hide, View } from "@element-plus/icons-vue";
import { useRouter, useRoute } from "vue-router";
import { decrypt, encryption } from "@/utils/SM4.js";
import { ElMessage, ElMessageBox } from "element-plus";
import dayjs from "dayjs";

export function useRole() {
  const columns = [
    {
      label: "学生ID",
      prop: "id",
      width: 90,
      formatter: ({ id }) => {
        return id || "--";
      }
    },
    {
      label: "学生姓名",
      prop: "name",
      width: 200,
      formatter: ({ name }) => {
        return name || "--";
      }
    },
    {
      label: "学校名称",
      prop: "school",
      minWidth: 90,
      formatter: ({ school }) => {
        return school || "--";
      }
    },
    {
      label: "证件类型",
      prop: "idType",
      minWidth: 90,
      formatter: ({ idType }) => {
        return idType || "--";
      }
    },
    {
      label: "证件号",
      prop: "idNumber",
      width: 200,
      cellRenderer: ({ row }) =>
        row.idNumber
? (
          <div style="display: flex;">
            {row.isName === true
? (
              <div style="width: 180px">{idNumberAdd(row.idNumberCt)}</div>
            )
: (
              <div style="width: 180px">{row.idNumber}</div>
            )}
            {row.isName === true
? (
              <el-icon
                style=" margin-left: 8px; margin-top: 4px;"
                onClick={() => imgAdd1(row)}
              >
                <View />
              </el-icon>
            )
: (
              <el-icon
                style=" margin-left: 8px; margin-top: 4px;"
                onClick={() => imgAdd1(row)}
              >
                <Hide />
              </el-icon>
            )}
          </div>
        )
: (
          <div>--</div>
        )
    },
    {
      label: "创建时间",
      width: 180,
      prop: "createdAt",
      formatter: ({ createdAt }) => {
        return createdAt
          ? dayjs(createdAt).format("YYYY-MM-DD HH:mm:ss")
          : "--";
      }
    },
    {
      label: "操作",
      fixed: "right",
      width: 120,
      slot: "operation"
    }
  ];

  const tableHeader = ref([
    {
      label: "家长ID",
      type: "text",
      prop: "id",
      placeholder: "",
      width: "200px"
    },
    {
      label: "创建时间",
      type: "createdAt",
      prop: "createdAt",
      placeholder: "",
      width: "200px"
    },
    {
      label: "家长姓名",
      type: "text",
      prop: "name",
      placeholder: "",
      width: "200px"
    },
    {
      label: "家长手机号",
      type: "phone",
      isEye: true,
      prop: "phone",
      prop1: "phoneCt",
      placeholder: "",
      width: "200px"
    }
  ]);

  const router = useRouter();
  const route = useRoute();
  const indexList = ref({});
  const dataList = ref([]);
  const loadingTable = ref(false);
  const iconteyp = ref(false);
  const pagination = {
    total: 0,
    pageSize: 0,
    currentPage: 0,
    background: true
  };

  // 每页多少条
  async function handleSizeChange(val) {
    // console.log("🍧-----text---每页多少条--", text);
  }
  // 前往页数
  async function handleCurrentChange(val) {
    // console.log("🦄-----text---前往页数--", text);
  }

  // 详情
  const openDialog = (text, val) => {
    router.push({
      path: "/parentManagement/componts/childrenDetails",
      query: { id: val.id }
    });
  };
  // 家长手机号
  function imgAdd(val) {
    iconteyp.value = !iconteyp.value;
  }
  const type = val => {
    if (!val) {
      return "--";
    } else {
      return decrypt(val);
    }
  };
  // 证件号
  function imgAdd1(val) {
    dataList.value.map(it => {
      if (it.id === val.id) {
        it.isName = !val.isName;
      }
    });
  }
  function idNumberAdd(val) {
    if (val) {
      return decrypt(val);
    } else {
      return "--";
    }
  }

  // 家长详情
  const startAdd = async val => {
    const paramsArg = {
      id: val.id
    };
    const [err, res] = await requestTo(parentFindById(paramsArg));
    if (res) {
      indexList.value = res;
    }
    if (err) {
    }
  };
  // 关联子女
  const startAddOne = async val => {
    const paramsArg = {
      parentId: val.id
    };
    const [err, res] = await requestTo(parentFindStudent(paramsArg));
    if (res) {
      dataList.value = res;
    }
    if (err) {
    }
  };

  // 冻结/解冻
  const isFreezeApi = async row => {
    let freezeText = `确定要解冻该账号吗？`;
    try {
      await ElMessageBox.confirm(freezeText, "确定解冻", {
        confirmButtonText: "确定",
        cancelButtonText: "取消"
      });
      const paramsArg = {
        id: indexList.value.id,
        freeze: !indexList.value.freeze
      };
      const operateLog = {
        operateLogType: "PARENT_MANAGEMENT",
        operateType: "解冻了机构"
      };
      const { code } = await parentIsFreeze(paramsArg, operateLog);
      console.log("🎁-----paramsArg, operateLog-----", paramsArg, operateLog);
      if (code === 200) {
        let teId = route.query;
        startAdd(teId);
        startAddOne(teId);

        ElMessage({
          type: "success",
          message: "解冻成功"
        });
      } else {
        ElMessage({
          type: "error",
          message: "解冻失败"
        });
      }
    } catch (error) {
      // console.log("操作取消");
    }
  };

  onActivated(() => {
    let teId = route.query;
    startAdd(teId);
    startAddOne(teId);
  });

  onMounted(() => {
    let teId = route.query;
    startAdd(teId);
    startAddOne(teId);
  });

  return {
    indexList,
    pagination,
    dataList,
    columns,
    iconteyp,
    imgAdd,
    type,
    openDialog,
    handleCurrentChange,
    handleSizeChange,
    loadingTable,
    tableHeader,
    isFreezeApi
  };
}
