<script setup>
import { ref, reactive, onMounted } from "vue";
import { findModelChildrenByParentId } from "@/api/apiPlatform.js";
import { requestTo } from "@/utils/http/tool";
import { uploadFile } from "@/utils/upload/upload.js";

const startAdd = async () => {
  const paramsArg = {
    parentId: 1
  };
  const [err, res] = await requestTo(findModelChildrenByParentId(paramsArg));
  if (res) {
    data.value = res;
  }
  if (err) {
  }
};

const data = ref();
const props = {
  value: "id",
  label: "name",
  children: "children"
};

// 上传学校数据
const uploadData = async file => {
  console.log("🎉-----file--上传学校数据---", file);
  let res = await uploadFile(file);
  console.log("🌳-----res-----", res);
};

onMounted(() => {
  startAdd();
});
</script>

<template>
  <div class="commonapp">
    <el-scrollbar class="scrollbar">
      <el-tree :data="data" :props="props" />
    </el-scrollbar>
  </div>
</template>

<style lang="scss" scoped>
.commonapp {
  height: 100%;
  padding-bottom: 18px;
  .scrollbar {
    height: calc(100vh - 209px);
    background-color: #fff;
  }
}
</style>
