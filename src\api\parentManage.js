import { http } from "@/utils/http";

/*  家长管理  */
// 是否冻结
export const parentIsFreeze = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/parent/isFreeze",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "PARENT_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};

// 根据id查询
export const parentFindById = params => {
  return http.request(
    "get",
    "/platform/parent/findById",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};

// 查询子女
export const parentFindStudent = params => {
  return http.request(
    "get",
    "/platform/parent/findStudent",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};

// 分页查询
export const parentFindAll = params => {
  return http.request(
    "get",
    "/platform/parent/findAll",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};

/*  家长管理 \ 订单管理  */
// 分页查询
export const ordersFindAll = params => {
  return http.request(
    "get",
    "/platform/orders/findAll",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 查询子订单详情
export const ordersGetOrderDetails = params => {
  return http.request(
    "get",
    "/platform/orders/getOrderDetails",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};

// 退款
export const ordersRefund = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/orders/refund",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "PARENT_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
