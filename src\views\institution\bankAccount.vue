<script setup>
import { onMounted, ref } from "vue";
import { getBankAccount } from "@/api/institution";
import { ElMessage } from "element-plus";
import { useRoute } from "vue-router";

// 表单数据
const form = ref({
  corporateName: "",
  corporateAddress: "",
  corporateTaxID: "",
  bankName: "",
  bankAddress: "",
  bankAccount: ""
});

// 加载状态
const loading = ref(false);
const route = useRoute();

// 账户信息展示数据
const accountInfo = ref([
  {
    label: "公司名称",
    value: "",
    key: "corporateName"
  },
  {
    label: "公司地址",
    value: "",
    key: "corporateAddress"
  },
  {
    label: "公司税号",
    value: "",
    key: "corporateTaxID"
  },
  {
    label: "开户银行名称",
    value: "",
    key: "bankName"
  },
  {
    label: "开户银行地址",
    value: "",
    key: "bankAddress"
  },
  {
    label: "银行账户",
    value: "",
    key: "bankAccount"
  }
]);

onMounted(() => {
  getAccount();
});

// 获取银行账户信息
const getAccount = async () => {
  try {
    const params = {
      organizationId: route.query.id
    };
    loading.value = true;
    const { code, data, msg } = await getBankAccount(params);
    if (code === 200 && data) {
      // 更新展示数据
      accountInfo.value.forEach(item => {
        item.value = data[item.key] || "";
      });

      // 保存原始数据
      Object.keys(form.value).forEach(key => {
        form.value[key] = data[key] || "";
      });
    } else if (code === 200 && !data) {
      // 处理数据为空的情况
      console.log("银行账户信息为空");
      // 清空展示数据
      accountInfo.value.forEach(item => {
        item.value = "";
      });
      // 清空表单数据
      Object.keys(form.value).forEach(key => {
        form.value[key] = "";
      });
    } else {
      ElMessage({
        message: msg || "获取银行账户信息失败",
        type: "error"
      });
    }
  } catch (error) {
    console.log("🚨-----error-----", error);
    ElMessage({
      message: "获取银行账户信息失败",
      type: "error"
    });
  } finally {
    loading.value = false;
  }
};
</script>

<template>
  <div>
    <!-- 银行账户信息展示 -->
    <el-card v-loading="loading" style="min-height: 88vh">
      <div class="card-header">
        <h2 class="page-title">银行账户信息</h2>
      </div>

      <div class="info-sections">
        <!-- 公司信息区 -->
        <div class="info-section">
          <div class="section-header">
            <span style="margin-left: 10px">公司信息</span>
          </div>
          <el-descriptions :column="1" border class="descriptions-content">
            <el-descriptions-item
              v-for="item in accountInfo.slice(0, 3)"
              :key="item.key"
              :label="item.label"
              label-width="120px"
              label-class-name="custom-label"
              class-name="custom-content"
              label-align="center"
            >
              {{ item.value || "--" }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 银行信息区 -->
        <div class="info-section">
          <div class="section-header">
            <span style="margin-left: 10px">银行信息</span>
          </div>
          <el-descriptions :column="1" border class="descriptions-content">
            <el-descriptions-item
              v-for="item in accountInfo.slice(3)"
              :key="item.key"
              :label="item.label"
              label-width="120px"
              label-class-name="custom-label"
              class-name="custom-content"
              label-align="center"
            >
              {{ item.value || "--" }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-card>
  </div>
</template>

<style scoped lang="scss">
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 20px;
}

.page-title {
  font-size: 20px;
  color: #303133;
  margin: 0;
}

.info-sections {
  display: flex;
  flex-direction: column;
  gap: 30px;
  padding: 0 20px;
  margin-bottom: 20px;
}

.info-section {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  border: 1px solid #ebeef5;
}

.section-header {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  background-color: #ebf4fc;
  border-radius: 4px 4px 0 0;
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.descriptions-content {
  padding: 0;
}

// 调整 el-descriptions 样式
:deep(.el-descriptions__label) {
  text-align: right;
  margin-right: 20px;
  font-weight: bold;
  color: #606266;
}

:deep(.el-descriptions__cell) {
  padding: 12px !important;
}

:deep(.custom-label) {
  background-color: #f5f7fa;
  padding: 8px 12px;
}

:deep(.custom-content) {
  padding: 12px 15px;
}

:deep(.el-descriptions__body) {
  width: 100%;
}

:deep(.el-descriptions) {
  width: 100%;
  margin-bottom: 0;
}

:deep(.el-descriptions__table) {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

:deep(.el-card) {
  border-radius: 4px;
  border: 1px solid #ebeef5;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

:deep(.el-card__body) {
  padding: 20px 0;
}

@media screen and (min-width: 992px) {
  .info-sections {
    flex-direction: row;
    align-items: stretch;

    .info-section {
      flex: 1;
      margin: 0 10px;

      &:first-child {
        margin-left: 0;
      }

      &:last-child {
        margin-right: 0;
      }
    }
  }
}
</style>
