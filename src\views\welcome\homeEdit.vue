<script setup>
import { ref, onMounted, watch, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import uploadImg from "@/assets/login/upload1.png";
import { adminFindById } from "@/api/institution.js";
import { uploadFile } from "@/utils/upload/upload";
import { Hide, View, Loading } from "@element-plus/icons-vue";
import { decrypt, encryption } from "@/utils/SM4.js";
import { compareObjects, debounce } from "@iceywu/utils";
import { editteaInfo, verifyUsername, verifyPhone } from "@/api/institution";
import { getPhonecode } from "@/api/leaderLecturer.js";
import { useUserStoreHook } from "@/store/modules/user";
import { getBindCodePlatform, getUnbindPlatform } from "@/api/user.js";
import WxQrCode from "@/components/WxQrCode/index.vue";

const router = useRouter();
const route = useRoute();
const userInfIdData = ref(null);
const richFlag = ref(false);
const isCaptchaDisabled = ref(false);
const isCaptchaLoading = ref(false);
const userStore = useUserStoreHook();

// 微信相关变量
const wxQrCodeRef = ref(null);
const showUnbindDialog = ref(false);
const isWxCallbackProcessed = ref(false);
const unbindWxLoading = ref(false);

onMounted(() => {
  userInfIdData.value = route.query.id;
  getUserinfo();
  // console.log(">>>>>>>>>", route.matched);
  // richFlag.value = true;
  // console.log("🎁-----useUserStoreHook()-----", userInfIdData.value);
});
const formFile = ref({
  institutionLicense: [],
  qualificationDocuments: []
});
// 表单
const form = ref({
  name: "",
  account: "",
  iphone: "",
  codenumb: "",
  email: "",
  Identity: "",
  fileUrl: "",
  files: [],
  phone: "",
  code: "",
  institutionLicense: [],
  qualificationDocuments: [],
  isBindWx: null
});
// 提交
const formRef = ref(null);

const formData = ref([
  {
    label: "姓名",
    type: "input",
    prop: "name",
    show: true,
    check: true,
    placeholder: "请输入姓名",
    width: "400px",
    maxLength: 10
  },
  {
    label: "账号",
    type: "input",
    prop: "account",
    show: true,
    check: true,
    placeholder: "请输入账号",
    width: "400px",
    maxLength: 20
  },
  {
    label: "手机号",
    type: "input",
    prop: "phone",
    show: true,
    check: true,
    isView: true,
    placeholder: "请输入手机号",
    width: "400px",
    maxLength: 11
  },
  {
    label: "邮箱",
    type: "input",
    show: true,
    prop: "email",
    placeholder: "请输入邮箱",
    width: "400px",
    maxLength: 30
  },
  {
    label: "身份证号",
    type: "input",
    prop: "idNumber",
    show: true,
    isView: true,
    placeholder: "请输入身份证号",
    width: "400px",
    maxLength: 18
  },
  {
    label: "微信绑定",
    type: "img",
    prop: "isBindWx",
    show: true,
    url: "",
    width: "400px",
    height: "120px"
  }
]);
// 身份证号验证
// const validateIdNumber = (rule, value, callback) => {
//   // console.log("🦄-----value-----", value);
//   // return;
//   // console.log("🦄-----value-----", value, newData.value.idNumber);
//   // console.log(decrypt(newData.value.idNumberCt));
//   if (
//     newData.value.idNumberCt &&
//     (value === newData.value.idNumber ||
//       value === decrypt(newData.value.idNumberCt))
//   ) {
//     callback();
//     return;
//   }
//   const idNumberPattern = /(^\d{15}$)|(^\d{18}$)|(^\d{17}([\dX])$)/i;
//   if (value && !idNumberPattern.test(value)) {
//     callback(new Error("请输入有效的身份证号"));
//   } else {
//     callback();
//   }
// };

// 自定义电话号码校验方法
const validatePhoneNumber = async (rule, value, callback) => {
  if (
    newData.value.phoneCt &&
    (value == newData.value.phone || value == decrypt(newData.value.phoneCt))
  ) {
    if (formData.value[3].label === "验证码") {
      formData.value.splice(3, 1);
    }
    callback();
    return;
  }
  const phoneRegex = /^1[3-9]\d{9}$/; // 匹配中国大陆手机号码
  // return;
  if (!value) {
    isCaptchaDisabled.value = true;
    return callback(new Error("手机号不能为空"));
  } else if (!phoneRegex.test(value)) {
    isCaptchaDisabled.value = true;
    return callback(new Error("请输入有效的手机号码"));
  } else {
    isCaptchaDisabled.value = false;

    if (formData.value[3].label !== "验证码") {
      // 往数组指定位置添加验证码字段
      formData.value.splice(3, 0, {
        label: "验证码",
        type: "input",
        prop: "code",
        span: 1,
        placeholder: "请输入验证码",
        width: "400px",
        check: true
      });

      // 如果form中没有code字段，初始化它
      if (!form.value.code) {
        form.value.code = "";
      }
    }

    if (value == decrypt(newData.value.phoneCt)) {
      callback();
    } else {
      try {
        const response = await verifyPhone({ phone: encryption(value) });
        // console.log("🌈-----response-----", response);
        if (response.code === 70008) {
          isCaptchaDisabled.value = true;
          callback(new Error("手机号已存在"));
        } else {
          callback();
        }
      } catch (error) {
        console.log("🌈-----error-----", error);
      }
    }
  }
};
// 邮箱验证
const validateEmail = (rule, value, callback) => {
  const emailPattern = /^[a-z0-9._-]+@[a-z0-9.-]+\.[a-z]{2,6}$/i;
  if (value && !emailPattern.test(value)) {
    callback(new Error("请输入有效的邮箱地址"));
  } else {
    callback();
  }
};
// 自定义账号校验方法
const validateAccount = async (rule, value, callback) => {
  if (!value) {
    callback(new Error("账号不能为空"));
  } else {
    try {
      const response = await verifyUsername({ username: value });
      console.log("🌈-----response-----", response);
      if (response.code === 10016) {
        callback(new Error("账号已存在"));
      } else {
        callback();
      }
    } catch (error) {
      console.log("🌈-----error-----", error);
    }
    // callback();
  }
};
const ID_NUMBER_REGEX = /(^\d{17}([0-9X])$)/i;
const CHECK_CODES = ["1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2"];
const FACTORS = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
const validateIdNumber = (rule, value, callback) => {
  if (!value) {
    callback();
    return;
  }

  if (value.includes("*")) {
    if (!formData.value.find(f => f.prop === "idNumber").isView) {
      return callback(new Error("身份证号格式不正确"));
    }
    if (newData.value["idNumber"]) value = decrypt(newData.value["idNumberCt"]);
  }

  if (!ID_NUMBER_REGEX.test(value)) {
    return callback(new Error("身份证号格式不正确"));
  }
  if (!value.length === 18 || !isValidChineseIDChecksum(value)) {
    return callback(new Error("身份证号不正确"));
  }
  newData.value["idNumber"] =
    value.substring(0, 3) + "***********" + value.substr(value.length - 4);
  newData.value["idNumberCt"] = encryption(value);
  callback();
};
function isValidChineseIDChecksum(idNumber) {
  let sum = 0;
  for (let i = 0; i < 17; i++) {
    const digit = parseInt(idNumber[i], 10);
    if (isNaN(digit)) {
      return false; // 遇到非数字字符返回 false
    }
    sum += digit * FACTORS[i];
  }

  const index = sum % 11;
  const expectedCheckCode = CHECK_CODES[index];
  return expectedCheckCode.toUpperCase() === idNumber[17].toUpperCase();
}
// 校验规则
const rules = ref({
  name: [
    { required: true, message: "姓名不能为空", trigger: "blur" },
    { min: 2, max: 10, message: "姓名长度应在2-10个字符之间", trigger: "blur" }
  ],
  account: [{ required: true, validator: validateAccount, trigger: "blur" }],
  phone: [{ required: true, validator: validatePhoneNumber, trigger: "blur" }],
  email: [{ validator: validateEmail, trigger: "blur" }],
  code: [{ required: true, message: "验证码不能为空", trigger: "blur" }],
  idNumber: [{ validator: validateIdNumber, trigger: ["blur", "change"] }]
});
const submitForm = () => {
  formRef.value.validate(async valid => {
    if (valid) {
      console.log("表单数据:", form.value);
      addbase();
    } else {
      console.log("表单校验失败");
    }
  });
};
const getListLoading = ref(false);
const addbase = async () => {
  if (getListLoading.value) return;
  getListLoading.value = true;
  let paramsData = {};
  for (const paramsDataKey in form.value) {
    let isArray = Array.isArray(form.value[paramsDataKey]);
    if (isArray) {
      if (form.value[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    } else {
      if (form.value[paramsDataKey]) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    }
  }
  paramsData = compareObjects(newData.value, form.value);
  // 处理手机号
  if (paramsData.phone) {
    // 判断手机号是否已经是掩码状态
    if (
      newData.value.phoneCt &&
      (form.value.phone === newData.value.phone ||
        form.value.phone === decrypt(newData.value.phoneCt))
    ) {
      // 如果手机号未修改，不需要上传手机号参数和验证码
      delete paramsData.phone;
      delete paramsData.code;
    } else {
      // 如果手机号有修改，需要加密并验证码
      paramsData.phone = encryption(form.value.phone);
      if (!form.value.code) {
        ElMessage({
          type: "error",
          message: "修改手机号需要验证码"
        });
        getListLoading.value = false;
        return;
      }
      paramsData.code = form.value.code;
    }
  }

  // 处理身份证号 - 确保身份证号总是被包含在参数中
  if (form.value.idNumber && form.value.idNumber.trim() !== "") {
    const idNumberIndex = formData.value.findIndex(
      item => item.prop === "idNumber"
    );

    // 判断身份证号当前显示状态和是否修改
    if (idNumberIndex !== -1 && formData.value[idNumberIndex].isView) {
      // 如果是掩码状态，使用原始的加密数据
      if (newData.value.idNumberCt) {
        paramsData.idNumber = newData.value.idNumberCt;
      } else {
        paramsData.idNumber = encryption(form.value.idNumber);
      }
    } else if (
      form.value.idNumber === decrypt(newData.value.idNumberCt || "")
    ) {
      // 如果是已解密但未修改，使用原始加密数据
      paramsData.idNumber = newData.value.idNumberCt;
    } else {
      // 否则加密新输入的身份证号
      paramsData.idNumber = encryption(form.value.idNumber);
    }
  } else {
    // 如果身份证号为空，则从参数中删除
    delete paramsData.idNumber;
  }

  // 确保email有值时总是被包含在参数中
  if (form.value.email && form.value.email.trim() !== "") {
    paramsData.email = form.value.email;
  } else {
    delete paramsData.email;
  }
  // 创建一个新的对象，排除email和idNumber
  const paramsForComparison = { ...paramsData };
  delete paramsForComparison.email;
  delete paramsForComparison.idNumber;

  if (Object.keys(paramsForComparison).length === 0) {
    // 如果除了email和idNumber之外没有其他修改，则允许提交
    // 但如果email和idNumber也都没有值，才提示未修改
    if (!paramsData.email && !paramsData.idNumber) {
      ElMessage({
        type: "warning",
        message: "未修改任何信息"
      });
      getListLoading.value = false;
      return;
    }
  }
  paramsData.id = userInfIdData.value;
  // paramsData.roleIds = '';
  const operateLog = {
    operateLogType: "ACCOUNT_MANAGEMENT",
    operateType: "编辑了首页信息",
    operatorTarget: form.value.name
  };
  console.log("🍪-----paramsData-----", paramsData);
  // return;
  try {
    const { code, msg } = await editteaInfo(paramsData, operateLog);
    if (code === 200) {
      ElMessage({
        type: "success",
        message: "编辑成功"
      });
      getListLoading.value = false;
      cancelForm();
      userStore.updateUserInfo(form.value);
    } else {
      ElMessage({
        type: "error",
        message: msg
      });
      getListLoading.value = false;
    }
  } catch (error) {
    console.error("编辑失败:", error);
    ElMessage({
      type: "error",
      message: "网络错误，请稍后重试"
    });
    getListLoading.value = false;
  }
};
// 获取验证码
const getCaptcha = async phoneCode => {
  const phoneRegex = /^1[3-9]\d{9}$/; // 匹配中国大陆手机号码
  if (!phoneCode) {
    isCaptchaDisabled.value = true;
    ElMessage({ message: "手机号不能为空" });
    return;
  }
  if (!phoneRegex.test(phoneCode)) {
    isCaptchaDisabled.value = true;
    ElMessage({ message: "电话号码格式不正确", type: "warning" });
    return;
  }
  const params = {
    phone: encryption(phoneCode),
    codeType: "VERIFICATION_CODE"
  };
  isCaptchaDisabled.value = false;
  isCaptchaLoading.value = true;
  try {
    const { code, msg } = await getPhonecode(params);
    if (code == 200) {
      ElMessage({
        message: "验证码已发送",
        type: "success"
      });
    }
  } finally {
    isCaptchaLoading.value = false;
  }
};
// 取消
const cancelForm = () => {
  router.push({
    path: "/welcome"
  });
};

const newData = ref();
// 获取用户信息
const getUserinfo = async data => {
  try {
    const { code, data, msg } = await adminFindById({
      id: userInfIdData.value
    });
    console.log("🎁-----data-----", code, data, msg);
    if (code == 200 && data) {
      form.value = data;
      form.value.phone = data.phone;
      form.value.idNumber = data.idNumber;
      newData.value = JSON.parse(JSON.stringify(data));
      richFlag.value = true;
    }
  } catch (error) {
    console.log("catch-----error-----", error);
  }
  console.log("🐳-----formFile.value-----", formFile.value);
};
const isViewFn = (val, index) => {
  formData.value[index].isView = !formData.value[index].isView;
  console.error(form.value[val], formData.value[index].isView);
  if (val === "idNumber") {
    if (formData.value[index].isView) {
      // 切换为显示掩码数据
      form.value[val] = newData.value[val];
      console.error(newData.value[val]);
    } else {
      // 切换为显示解密数据
      // newData.value[val + "Ct"] = form.value[val];
      form.value[val] = decrypt(newData.value[val + "Ct"]);
    }
  } else if (val === "phone") {
    if (formData.value[index].isView) {
      // 切换为显示掩码数据
      form.value[val] = newData.value[val];
    } else {
      // 切换为显示解密数据
      form.value[val] = decrypt(newData.value[val + "Ct"]);
    }
  }
};

// 微信回调处理
const handleWxCallback = (code, state) => {
  // 检查是否已经处理过此次绑定请求
  const processedKey = `wx_bind_processed_${code}_${state}`;
  if (sessionStorage.getItem(processedKey)) {
    console.log("已处理过此绑定请求，不再重复处理");
    isWxCallbackProcessed.value = true;
    return;
  }

  const storedState = sessionStorage.getItem("wx_login_state");

  // 状态码 是否一致
  if (state !== storedState) {
    ElMessage.error("微信状态不一致，请重新扫码");
    isWxCallbackProcessed.value = true; // 标记为已处理
    return;
  }

  console.log(form.value.isBindWx, "isbind");

  // 状态码一致，处理微信绑定
  if (form.value.isBindWx) {
    // 如果当前已绑定微信，这里可以处理解绑逻辑（如果需要的话）
    ElMessage.info("当前账号已绑定微信");
    isWxCallbackProcessed.value = true; // 标记为已处理
  } else {
    // 如果当前未绑定微信，处理绑定逻辑
    bindCode(code, processedKey);
  }
};

// 绑定账号
const bindCode = async (code, processedKey) => {
  console.log(code, "code");
  const params = {
    code: code,
    userId: route.query?.id,
    userType: "PLATFORM_ADMIN"
  };

  try {
    const res = await getBindCodePlatform(params, {
      operateLogType: "ACCOUNT_MANAGEMENT",
      operateType: `对账号“${form.value.account}”完成微信绑定操作`
    });
    if (res.code === 200) {
      ElMessage.success("绑定成功");
      // 记录已处理状态到会话存储
      sessionStorage.setItem(processedKey, "true");

      // 重新获取用户信息
      await getUserinfo();

      // 重置二维码组件状态
      if (wxQrCodeRef.value) {
        wxQrCodeRef.value.resetInit();
      }

      // 标记为已处理
      isWxCallbackProcessed.value = true;
    } else {
      ElMessage.warning(res.msg);
      isWxCallbackProcessed.value = true; // 标记为已处理
    }
  } catch (err) {
    isWxCallbackProcessed.value = true; // 标记为已处理
    throw new Error(err);
  }
};

// 解绑微信
const handleChangeWx = debounce(
  async () => {
    if (unbindWxLoading.value) return;
    unbindWxLoading.value = true;
    try {
      const params = {
        userId: route.query?.id,
        userType: "PLATFORM_ADMIN"
      };

      const res = await getUnbindPlatform(params, {
        operateLogType: "ACCOUNT_MANAGEMENT",
        operateType: `对账号“${form.value.account}”完成微信解绑操作`
      });

      if (res.code === 200) {
        ElMessage({
          type: "success",
          message: "微信解绑成功"
        });

        // 关闭对话框
        showUnbindDialog.value = false;

        await getUserinfo();
      }
      console.log("微信解绑完成");
    } catch (error) {
      ElMessage({
        type: "error",
        message: "微信解绑失败，请重试"
      });
    } finally {
      unbindWxLoading.value = false;
    }
  },
  1000,
  { immediate: true }
);

// 构建包含当前 query 参数的重定向路径
const redirectPathWithQuery = computed(() => {
  const currentPath = route.path;
  const queryParams = new URLSearchParams();

  // 将当前的 query 参数添加到 URLSearchParams 中，排除微信回调参数
  Object.keys(route.query).forEach(key => {
    // 排除微信回调相关的参数
    if (key !== "code" && key !== "state") {
      if (route.query[key] !== null && route.query[key] !== undefined) {
        queryParams.append(key, route.query[key]);
      }
    }
  });

  // 如果有 query 参数，则构建完整路径
  const queryString = queryParams.toString();
  return queryString ? `${currentPath}?${queryString}` : currentPath;
});

// 检测是否有微信回调参数且未处理
const hasWxCallbackParams = computed(() => {
  return !!(
    route.query.code &&
    route.query.state &&
    !isWxCallbackProcessed.value
  );
});

// 监听路由参数变化
watch(
  () => route.query,
  newQuery => {
    const { code, state } = newQuery;
    console.log(code, "code");
    console.log(state, "state");

    if (code && state && !isWxCallbackProcessed.value) {
      handleWxCallback(code, state);
    }
  },
  { immediate: true }
);
const testFn = () => {
  // 获取当前路由的全路径，使用window
  const currentPath = window.location.href;
  console.log("🍪-----currentPath-----", currentPath);
  // 构建包含当前 query 参数的重定向路径
  const redirectPath = `${currentPath}&code=081V9f1w3tpab53Krn2w3VAQny3V9f1I&state=61073`;
  console.log("🐳-----redirectPath-----", redirectPath);
  window.location = redirectPath;
};
</script>

<template>
  <div class="containers">
    <div class="table_content">
      <el-form ref="formRef" :rules="rules" :model="form" style="flex-grow: 1">
        <el-descriptions title="" :column="2" border>
          <el-descriptions-item
            v-for="(item, index) in formData"
            :key="index"
            label-width="180"
            label-align="center"
            label-class-name="my-label"
            :span="
              item.prop === 'phone'
                ? formData.some(i => i.prop === 'code')
                  ? 1
                  : 2
                : item.prop === 'code'
                  ? 1
                  : 2
            "
          >
            <template #label>
              <span v-if="item.check" class="star">*</span>
              {{ item.label }}
            </template>
            <el-form-item
              v-if="richFlag"
              :prop="item.prop"
              :inline-message="item.check"
              style="margin-bottom: 0"
              :show-message="true"
              error-placement="right"
            >
              <!-- input输入 -->
              <template v-if="item.type === 'input'">
                <el-input
                  v-model.trim="form[item.prop]"
                  :placeholder="item.placeholder"
                  :style="{ width: item.width }"
                  :disabled="
                    (item.prop === 'phone' || item.prop === 'idNumber') &&
                    item.isView &&
                    newData[item.prop]?.length > 0
                  "
                  :maxlength="item.maxLength"
                  :show-word-limit="item.maxLength"
                >
                  <template
                    v-if="
                      (item.prop === 'phone' || item.prop === 'idNumber') &&
                      form[item.prop]?.length > 0
                    "
                    #suffix
                  >
                    <span v-if="form[item.prop]?.length > 0">
                      <el-icon
                        v-if="item.isView"
                        style="cursor: pointer"
                        @click="isViewFn(item.prop, index)"
                      >
                        <Hide />
                      </el-icon>
                      <el-icon
                        v-else
                        style="cursor: pointer"
                        @click="isViewFn(item.prop, index)"
                      >
                        <View />
                      </el-icon>
                    </span>
                  </template>
                </el-input>
                <!-- 获取验证码 -->
                <div v-if="item.prop === 'code'" class="Vacode">
                  <el-button
                    v-countdown="{
                      value: 60,
                      callback: () => getCaptcha(form.phone),
                      countdownText: 's后重新获取',
                      loadingText: '发送中...'
                    }"
                  >
                    获取验证码
                  </el-button>
                </div>
              </template>

              <!-- 二维码展示 -->
              <template v-else-if="item.type === 'img'">
                <span v-if="!form.isBindWx" class="isQR"> 当前未绑定 </span>
                <div v-else>
                  <span>已绑定,
                    <el-link
                      type="primary"
                      underline="hover"
                      @click="showUnbindDialog = true"
                    >
                      解绑
                    </el-link>
                  </span>
                </div>
                <div v-if="!form.isBindWx" class="codeQR">
                  <!-- 只有在没有微信回调参数时才显示二维码 -->
                  <WxQrCode
                    v-if="!hasWxCallbackParams"
                    ref="wxQrCodeRef"
                    :redirectPath="redirectPathWithQuery"
                  />
                  <!-- 有微信回调参数时显示处理中状态 -->
                  <div v-else class="processing-status">
                    <el-icon class="is-loading">
                      <Loading />
                    </el-icon>
                    <span>正在处理微信绑定...</span>
                  </div>
                </div>
              </template>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
      </el-form>
      <div class="table_bottom">
        <el-button type="default" @click="cancelForm"> 取消 </el-button>
        <el-button type="primary" :loading="getListLoading" @click="submitForm">
          保存
        </el-button>
        <!-- <el-button type="primary" @click="testFn">
          重定向测试
        </el-button> -->
      </div>
    </div>

    <!-- 解绑微信确认对话框 -->
    <el-dialog
      v-model="showUnbindDialog"
      title="解绑微信"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="dialog-content">
        <span>确定要解绑微信吗？</span>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showUnbindDialog = false">取消</el-button>
          <el-button
            type="primary"
            :loading="unbindWxLoading"
            @click="handleChangeWx"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.containers {
  display: flex;
  flex-direction: column;
  height: 100%;
  //   padding: 24px;
  background: #f0f2f5;

  .table_top {
    box-sizing: border-box;
    padding: 20px;
    background-color: #fff;
  }

  .table_content {
    display: flex;
    flex-direction: column;
    min-height: 86vh;
    padding: 20px;
    margin-top: 20px;
    background-color: rgb(255 255 255);

    .star {
      margin-right: 3px;
      color: red;
    }

    .el-descriptions-item {
      display: flex;
      align-items: center;
    }

    .Vacode {
      margin-left: 20px;
    }

    .isQR {
      margin-right: 240px;
    }

    .codeQR {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 20px 0;
    }

    .processing-status {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 10px;
      padding: 20px;
      color: #666;
      font-size: 14px;
    }
  }

  .table_bottom {
    display: flex;
    justify-content: flex-end;
    margin-top: 30px;
  }
}

:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  background: #e1f5ff;
  //   width: 240px;
}

:deep(.my-label) {
  background: #e1f5ff !important;
}

:deep(.my-content) {
  background: var(--el-color-danger-light-9);
}
.upload-demo {
  display: flex;
  align-items: center;
}
.fileOther {
  // margin-top: 26px;
  margin-left: 20px;
  display: flex;
  align-items: center;
  line-height: 22px;
  width: 420px;
  // margin-bottom: 56px;
  .title {
    color: #0e1832;
    font-family: Source Han Sans CN;
    font-size: 16px;
    font-weight: 400;
    height: 24px;
    white-space: nowrap;
  }
  .link {
    width: 20px;
    height: 20px;
    margin: 0 10px;
    cursor: pointer;
  }
  .fileName {
    color: #3876fd;
    font-family: Source Han Sans CN;
    font-size: 16px;
    font-weight: 400;
    height: 24px;
    cursor: pointer;
    // width: 350px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .linkDetele {
    width: 20px;
    height: 20px;
    margin-left: 10px;
    cursor: pointer;
  }
}
.el-link {
  line-height: 1.2;
  margin-bottom: 2px;
}
</style>
