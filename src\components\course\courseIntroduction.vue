<script setup>
import { ref, onMounted, computed, watch } from "vue";
import { requestTo } from "@/utils/http/tool";
import {
  courseIntroductionFindAll,
  courseKnowledgePointFind,
  equipmentDescriptionFind,
  precautionsFindAll,
  findUserAgreement,
  educationBureauFindAgreement,
  educationBureauPrecautionsFindAll,
  educationBureauEquipmentDescriptionFind,
  educationBureauCourseKnowledgePointFind,
  educationBureauCourseIntroductionFindAll
} from "@/api/course";
import { to } from "@iceywu/utils";
import { useUserStoreHook } from "@/store/modules/user";
const props = defineProps({
  periodId: {
    type: Number,
    default: 0
  },
  tableTitle: {
    type: String,
    default: "课期介绍"
  }
});
const refName = ref("课期介绍");
const contentData = ref("");
// 课期介绍查询
const findCourseIntroduction = async type => {
  const params = {
    coursePeriodId: props.periodId
  };
  // let [err, res] = await requestTo(courseIntroductionFindAll(params));
  let api =
    useUserStoreHook().roleTarget === "局端管理员"
      ? educationBureauCourseIntroductionFindAll
      : courseIntroductionFindAll;
  let [err, res] = await to(api(params));
  if (res.code === 200) {
    contentData.value = res?.data?.content || "";
    // console.log("🌳-----res--33---", res);
    // tripList.value = res;
  } else {
    console.log("🌵-----err-----", err);
  }
};
// 课期知识点查询
// const findKnowledgePoint = async type => {
//   const params = {
//     coursePeriodId: props.periodId
//   };
//   let api =
//     useUserStoreHook().roleTarget === "局端管理员"
//       ? educationBureauCourseKnowledgePointFind
//       : courseKnowledgePointFind;
//   let [err, res] = await to(api(params));
//   if (res.code === 200) {
//     contentData.value = res?.data?.content || "";
//     // console.log("🌳-----res-----", res);
//     // tripList.value = res;
//   } else {
//     console.log("🌵-----err-----", err);
//   }
// };
// 材料说明查询
const findEquipmentDescription = async type => {
  const params = {
    coursePeriodId: props.periodId
  };
  let api =
    useUserStoreHook().roleTarget === "局端管理员"
      ? educationBureauEquipmentDescriptionFind
      : equipmentDescriptionFind;
  let [err, res] = await to(api(params));
  if (res.code === 200) {
    contentData.value = res?.data?.content || "";
    // console.log("🌳-----res-33333----", res);
    // tripList.value = res;
  } else {
    console.log("🌵-----err-----", err);
  }
};
// 注意事项查询
const findPrecautions = async type => {
  const params = {
    coursePeriodId: props.periodId
  };
  let api =
    useUserStoreHook().roleTarget === "局端管理员"
      ? educationBureauPrecautionsFindAll
      : precautionsFindAll;
  let [err, res] = await to(api(params));
  if (res.code === 200) {
    contentData.value = res?.data?.content || "";
    // console.log("🌳-----res-----", res);
    // tripList.value = res;
  } else {
    console.log("🌵-----err-----", err);
  }
};
// 用户协议
const findUserAgreementAll = async type => {
  const params = {
    coursePeriodId: props.periodId
  };
  let api =
    useUserStoreHook().roleTarget === "局端管理员"
      ? educationBureauFindAgreement
      : findUserAgreement;
  let [err, res] = await to(api(params));
  if (res.code === 200) {
    // console.log("🌳-----res-----", res);
    contentData.value = res?.data?.content || "";
    // tripList.value = res;
  } else {
    console.log("🌵-----err-----", err);
  }
};
watch(
  () => props.tableTitle,
  v => {
    if (props.tableTitle === "课期介绍") {
      findCourseIntroduction();
    }
    // else if (props.tableTitle === "课期知识点") {
    //   findKnowledgePoint();
    // }
    else if (props.tableTitle === "材料说明") {
      findEquipmentDescription();
    } else if (props.tableTitle === "注意事项") {
      findPrecautions();
    } else if (props.tableTitle === "用户协议") {
      findUserAgreementAll();
    }
  },
  { deep: true }
);

onMounted(() => {
  if (props.tableTitle === "课期介绍") {
    findCourseIntroduction();
  }
  // else if (props.tableTitle === "课期知识点") {
  //   findKnowledgePoint();
  // }
  else if (props.tableTitle === "材料说明") {
    findEquipmentDescription();
  } else if (props.tableTitle === "注意事项") {
    findPrecautions();
  } else if (props.tableTitle === "用户协议") {
    findUserAgreementAll();
  }

  console.log("🌈periodId------------------------------>", props.periodId);
});
</script>

<template>
  <div class="course-introduction">
    <div v-if="contentData" class="content">
      <div v-html="contentData" />
    </div>
    <el-empty v-else description="暂无数据" />
  </div>
</template>

<style lang="scss" scoped>
.course-introduction {
  height: 100%;
  position: relative;
  .content {
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
    height: calc(100% - 7px);
    background: #f5f7fa;
    overflow-y: auto;
    -ms-overflow-style: none;
    scrollbar-width: none;

    :deep(img) {
      vertical-align: middle;
      display: block;
    }
  }
  .content::-webkit-scrollbar {
    display: none;
  }
}
</style>
