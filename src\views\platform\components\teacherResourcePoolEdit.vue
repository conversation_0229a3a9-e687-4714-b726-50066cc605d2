<script setup>
import {
  ref,
  onMounted,
  computed,
  reactive,
  watch,
  nextTick,
  onBeforeUnmount
} from "vue";
import { useRouter, useRoute } from "vue-router";
import RichEditor from "@/components/Base/RichEditor.vue";
import { ElMessage } from "element-plus";
import uploadImg from "@/assets/login/upload1.png";
import {
  uploadFile,
  validateFileType,
  isOverSizeLimit
} from "@/utils/upload/upload";
import { decrypt, encryption } from "@/utils/SM4.js";
import {
  Hide,
  View,
  Plus,
  Close,
  Upload,
  Document,
  Download,
  Delete
} from "@element-plus/icons-vue";
import { formatTime, downloadFileBySaveAs } from "@/utils/index";
import FileItem from "@/components/PreviewV2/FileItem.vue";
import { compareObjects, debounce } from "@iceywu/utils";
import {
  addTeacherDatabase,
  getTeacherDetail,
  updateTeacherDatabase,
  verifyPhone,
  getDictOptions
} from "@/api/teacherResourcePool"; // 导入verifyPhone接口

// 包装document.querySelector以防止空引用错误
const safeQuerySelector = selector => {
  try {
    return document.querySelector(selector);
  } catch (e) {
    console.warn(`安全查询失败: ${selector}`, e);
    return null;
  }
};

// 全局错误处理器
const handleGlobalError = event => {
  const errorMsg = event?.error?.message || event?.message;
  if (
    errorMsg &&
    errorMsg.includes(
      "Cannot read properties of null (reading 'querySelector')"
    )
  ) {
    console.warn("已捕获DOM查询错误，这不会影响功能");
    event.preventDefault(); // 阻止错误冒泡
  }
};

// 安装全局错误处理器
const setupErrorHandling = () => {
  window.addEventListener("error", handleGlobalError);
};

// 移除全局错误处理器
const cleanupErrorHandling = () => {
  window.removeEventListener("error", handleGlobalError);
};

const router = useRouter();
const route = useRoute();
const formRef = ref(null);
const getListLoading = ref(false);
const uploadKey = ref(Date.now());

// 表单数据
const form = ref({
  id: "",
  name: "", // 姓名
  teacherNumber: "", // 教师编号
  age: "", // 年龄
  gender: "", // 性别，修改为空字符串，不默认选择
  phone: "", // 联系方式
  politicalStatus: "", // 政治面貌
  idType: "", // 证件类型
  idNumber: "", // 身份证号
  healthCertificate: "", // 健康证明
  noCrimeCertificate: "", // 无犯罪证明
  mentalHealthCertificate: "", // 心理健康证明
  educationLevel: "", // 学历
  major: "", // 所学专业
  employeeStatus: "", // 在职
  teacherYear: "", // 从教时长
  professionalTitle: "", // 职称等级
  teacherCertificateNumber: "", // 教师资格证号码
  graduationCertificate: "", // 毕业证
  teachableCourse: "", // 可任教课程
  avatar: "", // 头像
  goodAt: "", // 擅长课程
  hobbies: "", // 兴趣爱好
  personBio: "", // 个人风采
  files: [] // 文件集合
});

// 文件集合
const formFile = ref({
  avatar: [], // 头像
  healthCertificate: [], // 健康证明
  noCrimeCertificate: [], // 无犯罪证明
  mentalHealthCertificate: [], // 心理健康证明
  graduationCertificate: [], // 毕业证
  teacherCertificate: [], // 教师资格证
  personBio: [] // 个人风采
});

// 新增选项数据
const educationOptions = ref([]);
const majorOptions = ref([]);
const politicalStatusOptions = ref([]);
const idTypeOptions = ref([]);

// 基本信息表单项配置
const basicInfoFields = ref([
  {
    label: "教师姓名",
    type: "input",
    prop: "name",
    check: true,
    maxlength: 50,
    placeholder: "请输入教师姓名"
  },
  {
    label: "教师编号",
    type: "input",
    check: false,
    prop: "teacherNumber",
    maxlength: 20,
    placeholder: "请输入教师编号"
  },
  {
    label: "年龄",
    type: "input",
    check: false,
    prop: "age",
    maxlength: 3,
    placeholder: "请输入年龄"
  },
  {
    label: "性别",
    type: "select",
    check: true,
    prop: "gender",
    placeholder: "请选择性别",
    opt: [
      { label: "男", value: 1 },
      { label: "女", value: 2 }
    ]
  },
  {
    label: "联系方式",
    type: "input",
    check: true,
    maxlength: 11,
    prop: "phone",
    placeholder: "请输入联系方式"
  },
  {
    label: "政治面貌",
    type: "cascader",
    check: false,
    prop: "politicalStatus",
    placeholder: "请选择政治面貌",
    options: politicalStatusOptions
  },
  {
    label: "证件类型",
    type: "cascader",
    check: false,
    prop: "idType",
    placeholder: "请选择证件类型",
    options: idTypeOptions
  },
  {
    label: "证件号",
    type: "input",
    check: false,
    maxlength: 18,
    prop: "idNumber",
    placeholder: "请输入证件号"
  },
  {
    label: "健康证明",
    type: "upload",
    check: false,
    prop: "healthCertificate",
    placeholder: "请上传健康证明",
    limit: 1,
    fileType: "MEDICAL_CERT",
    text: "支持上传png、jpg、jpeg图片格式，最多上传1张，单张图片大小不超过10MB"
  },
  {
    label: "无犯罪证明",
    type: "upload",
    check: false,
    prop: "noCrimeCertificate",
    placeholder: "请上传无犯罪证明",
    limit: 1,
    fileType: "NO_CRIME_CERT",
    text: "支持上传png、jpg、jpeg图片格式，最多上传1张，单张图片大小不超过10MB"
  },
  {
    label: "心理健康筛查证明",
    type: "upload",
    check: false,
    prop: "mentalHealthCertificate",
    placeholder: "请上传心理健康筛查证明",
    limit: 1,
    fileType: "MENTAL_HEALTH_CERT",
    text: "支持上传png、jpg、jpeg图片格式，最多上传1张，单张图片大小不超过10MB"
  }
]);

// 教学背景表单项配置
const teachingBackgroundFields = ref([
  {
    label: "学历",
    type: "cascader",
    check: true,
    prop: "educationLevel",
    placeholder: "请选择学历",
    options: educationOptions
  },
  {
    label: "所学专业",
    type: "cascader",
    check: true,
    prop: "major",
    placeholder: "请选择所学专业",
    options: majorOptions
  },
  {
    label: "在职",
    type: "select",
    check: false,
    prop: "employeeStatus",
    placeholder: "请选择状态",
    opt: [
      { label: "全职", value: "全职" },
      { label: "兼职", value: "兼职" }
    ]
  },
  {
    label: "从教时长",
    type: "input",
    check: false,
    maxlength: 3,
    prop: "teacherYear",
    placeholder: "请输入从教时长(年)"
  },
  {
    label: "职称级别",
    type: "select",
    check: false,
    prop: "professionalTitle",
    placeholder: "请选择职称级别",
    opt: [
      { label: "未定级", value: "UNRATED" },
      { label: "初级", value: "JUNIOR" },
      { label: "中级", value: "INTERMEDIATE" },
      { label: "副高级", value: "ASSOCIATE_SENIOR" },
      { label: "高级", value: "SENIOR" }
    ]
  },
  {
    label: "教师资格证号码",
    type: "input",
    check: false,
    maxlength: 50,
    prop: "teacherCertificateNumber",
    placeholder: "请输入教师资格证号码"
  },
  {
    label: "教师资格证",
    type: "upload",
    check: false,
    prop: "teacherCertificate",
    placeholder: "请上传教师资格证",
    limit: 1,
    fileType: "TEACHER_CERT",
    text: "支持上传png、jpg、jpeg图片格式，最多上传1张，单张图片大小不超过10MB"
  },
  {
    label: "毕业证",
    type: "upload",
    check: false,
    prop: "graduationCertificate",
    placeholder: "请上传毕业证",
    limit: 1,
    fileType: "GRADUATION_CERT",
    text: "支持上传png、jpg、jpeg图片格式，最多上传1张，单张图片大小不超过10MB"
  },
  {
    label: "可任教课程",
    type: "input",
    check: false,
    prop: "teachableCourse",
    placeholder: "请输入可任教课程"
  }
]);

// 个人风采表单项配置
const personalInfoFields = ref([
  {
    label: "个人头像",
    type: "upload",
    check: false,
    prop: "avatar",
    limit: 1,
    fileType: "COVER",
    text: "支持上传png、jpg、jpeg图片格式，最多上传1张，最佳尺寸:200*200px，单张图片大小不超过10MB"
  },
  {
    label: "擅长课程",
    type: "textarea",
    check: false,
    prop: "goodAt",
    placeholder: "请介绍擅长的课程",
    maxlength: 200
  },
  {
    label: "兴趣爱好",
    type: "textarea",
    check: false,
    prop: "hobbies",
    placeholder: "请介绍兴趣爱好",
    maxlength: 200
  },
  {
    label: "个人风采",
    type: "upload",
    check: false,
    prop: "personBio",
    placeholder: "请上传个人风采照片",
    limit: 5,
    fileType: "PERSON_BIO",
    text: "支持上传png、jpg、jpeg图片格式，最多上传5张，最佳尺寸:750*750px，单张图片大小不超过10MB"
  }
]);

// 页面分区数据 - 优化后的结构
const sectionsList = computed(() => [
  {
    title: "基本信息",
    column: 2,
    fields: basicInfoFields.value
  },
  {
    title: "教学背景",
    column: 2,
    fields: teachingBackgroundFields.value
  },
  {
    title: "个人风采",
    column: 1,
    fields: personalInfoFields.value
  }
]);

// 存储原始数据，用于比较是否变更
const originalData = ref({
  phone: "",
  phoneCt: "",
  avatar: "" // 新增：存储原始头像文件标识符
});

// 手机号是否已变更
const phoneChanged = ref(false);

// 年龄校验方法
const validateAge = (rule, value, callback) => {
  if (value) {
    if (!/^\d+$/.test(value)) {
      return callback(new Error("年龄必须为数字"));
    }
    const ageNum = parseInt(value, 10);
    if (ageNum < 18 || ageNum > 120) {
      return callback(new Error("年龄范围应在18-120岁之间"));
    }
  }
  callback();
};

// 手机号校验方法
const validatePhoneNumber = async (rule, value, callback) => {
  if (!value) {
    return callback(new Error("手机号不能为空"));
  }

  const phoneRegex = /^1[3-9]\d{9}$/;
  if (!phoneRegex.test(value)) {
    return callback(new Error("请输入正确的手机号码"));
  }

  // 编辑模式下且手机号变更时校验
  if (
    route.query.id &&
    originalData.value.phone &&
    value !== originalData.value.phone
  ) {
    phoneChanged.value = true;
    const params = {
      phone: encryption(value)
    };

    try {
      const response = await verifyPhone(params);
      if (response.code === 10016) {
        return callback(new Error("手机号已存在"));
      } else {
        return callback();
      }
    } catch (error) {
      console.error("手机号校验错误:", error);
      return callback(new Error("手机号校验失败"));
    }
  } else if (route.query.id && value === originalData.value.phone) {
    // 手机号未变更
    phoneChanged.value = false;
    return callback();
  } else {
    // 新增模式下校验
    const params = {
      phone: encryption(value)
    };

    try {
      const response = await verifyPhone(params);
      if (response.code === 10016) {
        return callback(new Error("手机号已存在"));
      } else {
        return callback();
      }
    } catch (error) {
      console.error("手机号校验错误:", error);
      return callback(new Error("手机号校验失败"));
    }
  }
};

// 更新校验规则，使用新的手机号校验方法
const rules = ref({
  name: [{ required: true, message: "教师姓名不能为空", trigger: "blur" }],
  gender: [{ required: true, message: "性别不能为空", trigger: "change" }],
  phone: [
    { required: true, message: "联系方式不能为空", trigger: "blur" },
    { validator: validatePhoneNumber, trigger: "blur" }
  ],
  age: [{ validator: validateAge, trigger: "blur" }],
  educationLevel: [
    { required: true, message: "学历不能为空", trigger: "change" }
  ],
  major: [{ required: true, message: "所学专业不能为空", trigger: "change" }]
});

// 根据id查询，使用getTeacherDetail接口获取教师详情
const getData = async () => {
  const id = route.query.id;
  if (id) {
    try {
      getListLoading.value = true;
      const { code, data } = await getTeacherDetail({ id });
      if (code === 200 && data) {
        // 填充表单数据
        form.value = {
          ...form.value,
          ...data
        };
        // 保存原始头像数据，用于比较
        originalData.value.avatar = data.avatar?.uploadFile?.fileIdentifier;

        // 处理手机号和身份证号数据 - 使用加密数据解密后显示
        if (data.phoneCt) {
          const decryptedPhone = decrypt(data.phoneCt);
          form.value.phone = decryptedPhone;
          // 保存原始手机号数据，用于比较
          originalData.value.phone = decryptedPhone;
          originalData.value.phoneCt = data.phoneCt;
        }

        if (data.idNumberCt) {
          form.value.idNumber = decrypt(data.idNumberCt);
        }

        // 处理文件数据
        processFileData(data);
      } else {
        ElMessage.error("获取教师详情失败");
      }
    } catch (error) {
      console.error("获取数据失败:", error);
      ElMessage.error(error.message || "获取数据失败");
    } finally {
      getListLoading.value = false;
    }
  }
};

// 递归处理数据，移除leaf属性
const processData = nodes => {
  return nodes.map(node => {
    const { leaf, ...newNode } = node;
    if (newNode.children && newNode.children.length > 0) {
      newNode.children = processData(newNode.children);
    }
    return newNode;
  });
};

// 通用获取选项数据方法
const getOptionsData = async (parentId, optionsRef) => {
  const { code, data } = await getDictOptions({ parentId });
  if (code === 200 && data) {
    optionsRef.value = processData(data);
  }
};

// 处理文件数据
const processFileData = data => {
  // 清空所有文件数组
  Object.keys(formFile.value).forEach(key => {
    formFile.value[key] = [];
  });

  // 处理头像
  if (data.avatar && data.avatar.uploadFile) {
    formFile.value.avatar = [
      {
        url: data.avatar.uploadFile.url,
        name: data.avatar.uploadFile.fileName || "头像",
        fileName: data.avatar.uploadFile.fileName || "头像",
        fileIdentifier: data.avatar.uploadFile.fileIdentifier,
        status: "success"
      }
    ];
  }

  // 处理其他文件
  if (data.files && Array.isArray(data.files)) {
    data.files.forEach(item => {
      // 获取uploadFile中的文件信息
      const fileInfo = item.uploadFile || {};

      // 确保文件对象有必要的属性
      const fileObj = {
        ...fileInfo,
        url: fileInfo.url || "",
        name: fileInfo.fileName || "未命名文件",
        fileName: fileInfo.fileName || "未命名文件",
        fileIdentifier: fileInfo.fileIdentifier || "",
        uid: fileInfo.uploadId || Date.now() + Math.random(),
        status: "success"
      };

      switch (item.fileType) {
        case "MEDICAL_CERT":
          formFile.value.healthCertificate.push(fileObj);
          break;
        case "NO_CRIME_CERT":
          formFile.value.noCrimeCertificate.push(fileObj);
          break;
        case "MENTAL_HEALTH_CERT":
          formFile.value.mentalHealthCertificate.push(fileObj);
          break;
        case "GRADUATION_CERT":
          formFile.value.graduationCertificate.push(fileObj);
          break;
        case "TEACHER_CERT":
          formFile.value.teacherCertificate.push(fileObj);
          break;
        case "PERSON_BIO":
          formFile.value.personBio.push(fileObj);
          break;
      }
    });
  }

  // 刷新上传组件
  uploadKey.value = Date.now();
};

// 准备文件数据用于提交
const prepareFileData = () => {
  form.value.files = [];

  // 处理各类文件
  const addFiles = (fileArray, fileType) => {
    if (fileArray && fileArray.length > 0) {
      fileArray.forEach((item, index) => {
        form.value.files.push({
          fileType,
          fileIdentifier: item.fileIdentifier,
          sortOrder: index + 1
        });
      });
    }
  };

  addFiles(formFile.value.healthCertificate, "MEDICAL_CERT");
  addFiles(formFile.value.noCrimeCertificate, "NO_CRIME_CERT");
  addFiles(formFile.value.mentalHealthCertificate, "MENTAL_HEALTH_CERT");
  addFiles(formFile.value.graduationCertificate, "GRADUATION_CERT");
  addFiles(formFile.value.teacherCertificate, "TEACHER_CERT");
  addFiles(formFile.value.personBio, "PERSON_BIO");

  // 处理头像
  if (formFile.value.avatar.length > 0) {
    form.value.avatar = formFile.value.avatar[0].fileIdentifier;
  }
};

// 文件上传前的验证
const beforeUpload = async (file, prop) => {
  // 判断是否重复
  if (prop !== "avatar") {
    const isDuplicate = formFile.value[prop].some(
      f =>
        (f.name === file.name || f.fileName === file.name) &&
        (f.size === file.size || f.totalSize === file.size)
    );

    if (isDuplicate) {
      ElMessage.error("不能上传重复的文件");
      return false;
    }
  }

  // 文件类型检验
  const typeImage = validateFileType(file, ["image"]);
  const isSize = isOverSizeLimit(file, typeImage.valid === true ? 10 : 30);

  if (!typeImage.valid) {
    ElMessage.error(typeImage.message || "请上传正确格式的图片");
    return false;
  }

  if (!isSize.valid) {
    ElMessage.error(isSize.message || "文件大小超出限制");
    return false;
  }

  try {
    const { code, data } = await uploadFile(file, () => {}, ["image"]);
    if (code === 200) {
      const newFile = {
        ...data,
        name: data.name || data.fileName || file.name,
        url: data.url || data.fileUrl || "",
        uid: data.uploadId || Date.now() + Math.random(),
        status: "success"
      };

      if (prop === "avatar") {
        formFile.value[prop] = [newFile];
      } else {
        formFile.value[prop].push(newFile);
      }
    }
  } catch (error) {
    ElMessage.error(error.message || "上传失败");
  }
};

// 删除文件
const handleRemove = (file, prop) => {
  const idx = formFile.value[prop].findIndex(
    i => i.url === file.url && i.uid === file.uid
  );
  if (idx !== -1) {
    formFile.value[prop].splice(idx, 1);
  } else {
    formFile.value[prop].splice(0, 1);
  }
};

//删除文件
const getDeleted = (item, index) => {
  formFile.value[item].splice(index, 1);
  uploadKey.value = Date.now(); // 关键：强制刷新
};

// 预览文件
const dialogVisible = ref(false);
const dialogImageUrl = ref("");
const handlePreview = file => {
  dialogImageUrl.value = file.url;
  dialogVisible.value = true;
};

// 超出上传数量限制
const handleExceed = (files, prop) => {
  const limit =
    basicInfoFields.value.find(item => item.prop === prop)?.limit ||
    teachingBackgroundFields.value.find(item => item.prop === prop)?.limit ||
    personalInfoFields.value.find(item => item.prop === prop)?.limit ||
    1;

  ElMessage.error(`最多只能上传${limit}个文件`);
};

// 返回上一页
const cancel = () => {
  router.go(-1);
};

// 提交表单
const submitForm = debounce(
  () => {
    formRef.value.validate(valid => {
      if (valid) {
        onSubmit();
      } else {
        console.log("表单校验失败");
      }
    });
  },
  1000,
  { immediate: true }
);

// 提交数据
const onSubmit = async () => {
  if (getListLoading.value) return;
  getListLoading.value = true;

  try {
    // 处理文件数据
    prepareFileData();

    // 准备参数
    const params = { ...form.value };
    const operateLog = {
      operateLogType: "TEACHER_DATABASE",
      operateType: route.query.id ? "修改了" : "新增了",
      operatorTarget: `${params.name}的师资信息`
    };

    // 确保更新操作时ID正确传入
    if (route.query.id) {
      params.id = route.query.id;

      // 如果手机号未变更，使用原始的加密手机号或不传
      if (
        !phoneChanged.value &&
        form.value.phone === originalData.value.phone
      ) {
        // 移除手机号参数，使用原有的手机号
        delete params.phone;
      } else if (params.phone) {
        // 手机号变更，进行加密
        params.phone = encryption(params.phone);
      }
    } else if (params.phone) {
      // 新增时加密手机号
      params.phone = encryption(params.phone);
    }

    // 对身份证号进行加密处理
    if (params.idNumber) {
      params.idNumber = encryption(params.idNumber);
    }

    // 过滤掉空字符串的参数，确保未填写的选项不传给后端
    Object.keys(params).forEach(key => {
      if (
        params[key] === "" ||
        params[key] === undefined ||
        params[key] === null
      ) {
        delete params[key];
      }
      // 处理空数组
      if (Array.isArray(params[key]) && params[key].length === 0) {
        delete params[key];
      }
    });

    let result;
    // 根据是否有ID判断是新增还是更新
    if (route.query.id) {
      // 更新操作
      result = await updateTeacherDatabase(params, operateLog);
    } else {
      // 新增操作
      result = await addTeacherDatabase(params, operateLog);
    }

    const { code, data, msg } = result;

    if (code === 200) {
      ElMessage.success("保存成功");
      // 返回列表页
      router.push("/platform/teacherResourcePool");
    } else {
      ElMessage.error(msg || "保存失败");
    }
  } catch (error) {
    console.error("提交失败:", error);
    ElMessage.error(error.message || "提交失败");
  } finally {
    getListLoading.value = false;
  }
};

// 处理可能的DOM操作相关错误
const safelyRunDomOperations = async () => {
  try {
    // 延迟执行以确保DOM完全加载
    await new Promise(resolve => setTimeout(resolve, 100));
    await nextTick();
    // DOM已准备好，可以安全执行DOM操作
  } catch (error) {
    console.error("DOM操作错误:", error);
  }
};

// 组件生命周期钩子
onMounted(async () => {
  // 安装错误处理
  setupErrorHandling();

  // 获取数据
  getData();

  // 获取级联选择器数据
  getOptionsData("453", educationOptions);
  getOptionsData("548", majorOptions);
  getOptionsData("1346", politicalStatusOptions);
  getOptionsData("359", idTypeOptions);

  // 安全初始化DOM操作
  await safelyRunDomOperations();

  // 修复可能有问题的组件初始化
  setTimeout(() => {
    // 再次尝试初始化，延迟500ms
    safelyRunDomOperations();
  }, 500);
});

// 组件卸载前清理
onBeforeUnmount(() => {
  // 移除错误处理器
  cleanupErrorHandling();

  // 清理可能存在的事件监听器或计时器
  // 这可以防止组件卸载后仍有代码尝试访问DOM元素
});
</script>

<template>
  <div class="main">
    <el-scrollbar class="scrollbar">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="">
        <!-- 信息部分循环 -->
        <template
          v-for="(section, sectionIndex) in sectionsList"
          :key="sectionIndex"
        >
          <div class="title-text">{{ section.title }}</div>
          <el-descriptions
            title=""
            :column="section.column || 2"
            border
            :label-width="'200px'"
          >
            <el-descriptions-item
              v-for="(item, index) in section.fields"
              :key="index"
              label-align="right"
              :label-class-name="
                item.type === 'upload' ? 'my-label upload-label' : 'my-label'
              "
              :span="item.span || 1"
            >
              <template #label>
                <span v-if="item.check" class="star">*</span>{{ item.label }}
              </template>
              <el-form-item
                :prop="item.prop"
                :inline-message="item.check"
                style="margin-bottom: 0"
                :show-message="true"
                error-placement="right"
              >
                <!-- 根据类型渲染不同的表单控件 -->
                <template v-if="item.type === 'text'">
                  <div style="color: #a8a8a8">
                    {{ form[item.prop] || "--" }}
                  </div>
                </template>

                <template v-else-if="item.type === 'input'">
                  <el-input
                    v-model.trim="form[item.prop]"
                    :placeholder="item.placeholder"
                    :style="{ width: item.width }"
                    :maxlength="item.maxlength || 50"
                    @input="
                      item.prop === 'teacherYear' || item.prop === 'age'
                        ? (form[item.prop] = (form[item.prop] || '').replace(
                            /[^\d]/g,
                            ''
                          ))
                        : null
                    "
                  />
                </template>

                <template v-else-if="item.type === 'cascader'">
                  <el-cascader
                    v-model="form[item.prop]"
                    :options="item.options"
                    :props="{
                      expandTrigger: 'hover',
                      value: 'name',
                      label: 'name',
                      children: 'children',
                      emitPath: false
                    }"
                    style="width: 100%"
                    :placeholder="item.placeholder"
                    clearable
                    :show-all-levels="false"
                  />
                </template>

                <template
                  v-else-if="item.type === 'select' || item.type === 'select2'"
                >
                  <el-select
                    v-model="form[item.prop]"
                    :placeholder="item.placeholder"
                    style="width: 100%"
                    :multiple="item.type === 'select2'"
                  >
                    <el-option
                      v-for="option in item.opt"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </template>

                <template v-else-if="item.type === 'textarea'">
                  <el-input
                    v-model="form[item.prop]"
                    :rows="5"
                    type="textarea"
                    :maxlength="item.maxlength || 1000"
                    show-word-limit
                    :placeholder="item.placeholder"
                  />
                </template>

                <template v-else-if="item.type === 'upload'">
                  <!-- 头像上传 -->
                  <template v-if="item.prop === 'avatar'">
                    <el-upload
                      class="avatar-uploader"
                      action="#"
                      :show-file-list="false"
                      :http-request="() => {}"
                      :before-upload="
                        uploadFile => beforeUpload(uploadFile, item.prop)
                      "
                    >
                      <el-image
                        v-if="formFile.avatar.length > 0"
                        :src="formFile.avatar[0].url"
                        fit="cover"
                        class="avatar"
                      />
                      <el-icon v-else class="avatar-uploader-icon">
                        <Plus />
                      </el-icon>
                    </el-upload>
                  </template>
                  <!-- 个人风采 -->
                  <template v-else-if="item.prop === 'personBio'">
                    <el-upload
                      :key="uploadKey + item.prop"
                      action="#"
                      :show-file-list="true"
                      :file-list="formFile[item.prop]"
                      :http-request="() => {}"
                      :limit="item.limit"
                      :on-exceed="() => handleExceed(null, item.prop)"
                      :accept="'image/*'"
                      list-type="picture-card"
                      :before-upload="file => beforeUpload(file, item.prop)"
                      :before-remove="file => handleRemove(file, item.prop)"
                      :on-preview="handlePreview"
                      :class="{
                        hideUploadBtn: formFile[item.prop].length >= item.limit
                      }"
                    >
                      <el-icon><Plus /></el-icon>
                    </el-upload>
                  </template>

                  <!-- 普通文件上传 -->
                  <template v-else>
                    <el-upload
                      :key="uploadKey + item.prop"
                      action="#"
                      :show-file-list="false"
                      class="upload-demo"
                      :limit="item.limit"
                      :on-exceed="() => handleExceed(null, item.prop)"
                      :http-request="() => {}"
                      :accept="'image/*'"
                      :before-upload="file => beforeUpload(file, item.prop)"
                    >
                      <template v-if="formFile[item.prop].length === 0">
                        <img :src="uploadImg" alt="">
                      </template>
                    </el-upload>
                    <template
                      v-for="(file, fileIndex) in formFile[item.prop]"
                      :key="fileIndex"
                    >
                      <FileItem
                        is-need-delte
                        :data="file"
                        :index="fileIndex"
                        style="width: 100%; min-width: 130px; height: 32px"
                        @delete="getDeleted(item.prop, fileIndex)"
                      />
                    </template>
                  </template>
                  <div class="upload_text">{{ item.text }}</div>
                </template>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item
              v-if="section.column === 2 && section.fields.length % 2 !== 0"
              class-name="placeholder-cell"
              label-class-name="placeholder-cell"
            />
          </el-descriptions>
        </template>
      </el-form>
    </el-scrollbar>

    <div class="footer">
      <div class="footer_right">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" :loading="getListLoading" @click="submitForm">
          保存
        </el-button>
      </div>
    </div>

    <!-- 图片预览弹窗 -->
    <el-dialog v-model="dialogVisible">
      <img class="w-full" :src="dialogImageUrl" alt="Preview Image">
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.scrollbar {
  height: calc(100vh - 190px);
  background-color: #fff;
}

.main {
  padding: 20px;
  background: #fff;
}

.title-text {
  color: #0e1832;
  font-family: Source Han Sans CN;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  margin: 20px 0 20px 10px;

  &::after {
    content: "";
    flex: 1;
    height: 1px;
    background: #e4e7ed;
    margin-left: 16px;
  }
}

.footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  font-size: 14px;

  .footer_right {
    display: flex;
    gap: 10px; /* 按钮之间的间距 */
  }
}

:deep(.my-label) {
  background: #fff !important;
  min-width: 100px !important;
}

:deep(.upload-label) {
  position: relative;
  bottom: 15px;
}

:deep(.placeholder-cell) {
  background: #fff !important;
}

:deep(
  .el-descriptions__body
    .el-descriptions__table.is-bordered
    .el-descriptions__cell
) {
  border: 0px !important;
}

.star {
  margin-right: 3px;
  color: red;
}

.upload_text {
  display: inline-block;
  font-size: 12px;
  color: #8c939d;
  line-height: 1.5;
}

:deep(.el-form-item__content) {
  display: inline-block;
  width: 300px;
}

:deep(.hideUploadBtn .el-upload--picture-card) {
  display: none;
}

:deep(.el-form-item__error) {
  position: absolute;
  left: 0;
  top: 100%;
  white-space: nowrap;
  z-index: 10;
}

:deep(.report-btn) {
  height: 30px !important;
}
:deep(.report-input) {
  height: 32px !important;
}
:deep(.avatar-uploader .el-upload) {
  background-color: rgb(250, 250, 250);
  border: 1px dashed var(--el-border-color);
  border-radius: 50%;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
  width: 100px;
  height: 100px;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  text-align: center;
  line-height: 100px;
}
.avatar {
  width: 100px;
  height: 100px;
  display: block;
  border-radius: 50%;
}
:deep(.upload-demo) {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
</style>
