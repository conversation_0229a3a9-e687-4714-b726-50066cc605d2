<script setup>
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";

defineOptions({
  name: "CouponManagementCreate"
});

const router = useRouter();

// 表单数据
const formData = ref({
  couponName: "",
  couponTypes: [],
  discountType: "满减",
  discountAmount: "",
  discountThreshold: "",
  issueCount: "",
  issueStartTime: "",
  issueEndTime: "",
  useStartTime: "",
  useEndTime: "",
  notes: "",
  status: "enabled"
});

// 表单验证规则
const formRules = reactive({
  couponName: [
    { required: true, message: "请输入优惠券名称", trigger: "blur" }
  ],
  couponTypes: [
    { required: true, message: "请选择优惠券使用类型", trigger: "change" }
  ],
  discountType: [
    { required: true, message: "请选择优惠券类型", trigger: "change" }
  ],
  discountAmount: [
    { required: true, message: "请输入优惠金额", trigger: "blur" }
  ],
  discountThreshold: [
    { required: true, message: "请输入优惠门槛", trigger: "blur" }
  ],
  issueCount: [
    { required: true, message: "请输入发放数量", trigger: "blur" }
  ],
  issueStartTime: [
    { required: true, message: "请选择发放开始时间", trigger: "change" }
  ],
  issueEndTime: [
    { required: true, message: "请选择发放结束时间", trigger: "change" }
  ],
  useStartTime: [
    { required: true, message: "请选择使用开始时间", trigger: "change" }
  ],
  useEndTime: [
    { required: true, message: "请选择使用结束时间", trigger: "change" }
  ]
});

// 优惠券使用类型选项
const couponTypeOptions = [
  { label: "通用", value: "universal" },
  { label: "适用", value: "applicable" },
  { label: "材料", value: "material" }
];

// 优惠券类型选项
const discountTypeOptions = [
  { label: "满减券", value: "满减券" }
];

// 状态选项
const statusOptions = [
  { label: "启用", value: "enabled" },
  { label: "停用", value: "disabled" }
];

const formRef = ref();
const loading = ref(false);

// 提交表单
const handleSubmit = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      loading.value = true;

      // 模拟API调用
      setTimeout(() => {
        console.log("提交的表单数据:", formData.value);
        ElMessage.success("优惠券创建成功");
        loading.value = false;
        router.push("/coupon/management/index");
      }, 1000);
    } else {
      ElMessage.error("请完善表单信息");
    }
  });
};

// 取消操作
const handleCancel = () => {
  ElMessageBox.confirm(
    "确定要取消新建优惠券吗？未保存的数据将丢失。",
    "确认取消",
    {
      confirmButtonText: "确定",
      cancelButtonText: "继续编辑",
      type: "warning"
    }
  ).then(() => {
    router.push("/coupon/management/index");
  }).catch(() => {
    // 用户选择继续编辑
  });
};

// 重置表单
const handleReset = () => {
  formRef.value.resetFields();
};

onMounted(() => {
  // 页面初始化逻辑
});
</script>

<template>
  <div class="coupon-create">
    <div class="common">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        class="coupon-form"
      >
        <div class="form-section">
          <h3 class="section-title">优惠券基础信息</h3>

          <el-form-item label="优惠券名称" prop="couponName">
            <el-input
              v-model="formData.couponName"
              placeholder="请输入优惠券名称"
              style="width: 400px"
            />
          </el-form-item>

          <el-form-item label="优惠券使用类型" prop="couponTypes">
            <el-checkbox-group v-model="formData.couponTypes">
              <el-checkbox
                v-for="option in couponTypeOptions"
                :key="option.value"
                :label="option.value"
              >
                {{ option.label }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <el-form-item label="优惠券类型" prop="discountType">
            <el-radio-group v-model="formData.discountType">
              <el-radio
                v-for="option in discountTypeOptions"
                :key="option.value"
                :label="option.value"
              >
                {{ option.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="满" prop="discountThreshold">
            <div class="discount-input-group">
              <el-input
                v-model="formData.discountThreshold"
                placeholder="请输入金额"
                style="width: 150px"
              />
              <span class="form-text">减</span>
              <el-input
                v-model="formData.discountAmount"
                placeholder="请输入金额"
                style="width: 150px"
              />
            </div>
          </el-form-item>

          <el-form-item label="发放数量" prop="issueCount">
            <el-input
              v-model="formData.issueCount"
              placeholder="请输入优惠券发放数量"
              style="width: 400px"
            />
          </el-form-item>

          <el-form-item label="发放时间" prop="issueStartTime">
            <div class="time-range">
              <span>开始</span>
              <el-date-picker
                v-model="formData.issueStartTime"
                type="date"
                placeholder="请选择开始时间"
                style="width: 200px; margin: 0 10px"
              />
              <span>结束</span>
              <el-date-picker
                v-model="formData.issueEndTime"
                type="date"
                placeholder="请选择结束时间"
                style="width: 200px; margin-left: 10px"
              />
            </div>
          </el-form-item>

          <el-form-item label="使用时间" prop="useStartTime">
            <div class="time-range">
              <span>开始</span>
              <el-date-picker
                v-model="formData.useStartTime"
                type="date"
                placeholder="请选择开始时间"
                style="width: 200px; margin: 0 10px"
              />
              <span>结束</span>
              <el-date-picker
                v-model="formData.useEndTime"
                type="date"
                placeholder="请选择结束时间"
                style="width: 200px; margin-left: 10px"
              />
            </div>
          </el-form-item>

          <el-form-item label="备注">
            <el-input
              v-model="formData.notes"
              type="textarea"
              :rows="4"
              placeholder="请输入备注信息"
              style="width: 400px"
            />
          </el-form-item>

          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="formData.status">
              <el-radio
                v-for="option in statusOptions"
                :key="option.value"
                :label="option.value"
              >
                {{ option.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>

        <div class="form-actions">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" :loading="loading" @click="handleSubmit">
            确认
          </el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.coupon-create {
  .common {
    height: 88vh;
    padding: 20px;
    background-color: #fff;
    border-radius: 4px;
    position: relative;
  }

  .page-header {
    margin-bottom: 24px;

    h2 {
      font-size: 20px;
      font-weight: bold;
      color: #606266;
      margin: 0;
    }
  }

  .coupon-form {
    max-width: 100%;
    padding-bottom: 80px;
  }

  .form-section {
    margin-bottom: 32px;
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 20px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e4e7ed;
  }

  .time-range {
    display: flex;
    align-items: center;

    span {
      color: #606266;
      font-size: 14px;
    }
  }

  .discount-input-group {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .form-text {
    margin: 0 10px;
    color: #606266;
    font-size: 14px;
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    position: absolute;
    bottom: 20px;
    right: 20px;
    left: 20px;
    background-color: #fff;
    padding-top: 20px;
    border-top: 1px solid #e4e7ed;
  }
}
</style>
