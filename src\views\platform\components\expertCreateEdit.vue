<script setup>
import { ref, reactive, onMounted, nextTick } from "vue";
import { useRouter, useRoute } from "vue-router";
import {
  platformInfoFind,
  platformInfoSaveOrUpdate,
  platformInfoUpdate,
  platformInfoSave
} from "@/api/apiPlatform.js";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox } from "element-plus";

const router = useRouter();
const route = useRoute();

const ruleFormRef = ref();
const buttonLoading = ref(false); // 按钮加载状态
const from = ref({
  id: "",
  customerServiceHotline: "",
  email: "",
  copyright: "",
  icpRecordNumber: "",
  securityNumber: "",
  securityNumberLink: "",
  icpRecordNumberLink: ""
});

//  基本信息
const formData = ref([
  {
    label: "教师名字",
    type: "input",
    prop: "createdAt",
    check: true,
    placeholder: "请输入教师名字"
  },
  {
    label: "教师编号",
    type: "input",
    prop: "createdAt",
    check: true,
    placeholder: "请输入教师编号"
  },
  {
    label: "年龄",
    type: "input",
    prop: "createdAt",
    check: true,
    placeholder: "请输入年龄"
  },
  {
    label: "性别",
    type: "select",
    prop: "name",
    check: true,
    placeholder: "请选择性别",
    options: [
      {
        label: "男",
        value: "男"
      },
      {
        label: "女",
        value: "女"
      }
    ]
  },
  {
    label: "联系方式",
    type: "input",
    prop: "gender",
    check: true,
    placeholder: "请输入性别"
  },
  {
    label: "政治面貌",
    type: "select",
    prop: "workUnit",
    check: true,
    placeholder: "请选择政治面貌",
    options: [
      {
        label: "党员",
        value: "党员"
      },
      {
        label: "团员",
        value: "团员"
      },
      {
        label: "群众",
        value: "群众"
      }
    ]
  },
  {
    label: "身份证",
    type: "input",
    prop: "goodAt",
    check: true,
    placeholder: "请输入身份证号或上传身份证"
  },
  {
    label: "机构名称",
    type: "input",
    prop: "registerYear",
    check: true,
    placeholder: "请输入机构名称"
  },
  {
    label: "无犯罪证明",
    type: "input",
    prop: "registerYear",
    check: true,
    placeholder: "请上传无犯罪证明"
  },
  {
    label: "健康证明",
    type: "input",
    prop: "registerYear",
    check: true,
    placeholder: "请上传体检表"
  },
  {
    label: "心理健康筛查证明",
    type: "input",
    prop: "registerYear",
    check: true,
    placeholder: "请上传心理健康筛查证明"
  },
  {
    label: "注册地址",
    type: "input",
    prop: "registerYear",
    check: false,
    placeholder: "请输入注册地址"
  },
  {
    label: "机构类别",
    type: "select",
    prop: "registerYear",
    check: false,
    placeholder: "请输入机构类别",
    options: [
      {
        label: "一级",
        value: "一级"
      },
      {
        label: "二级",
        value: "二级"
      },
      {
        label: "三级",
        value: "三级"
      }
    ]
  },
  {
    label: "培训类别",
    type: "select",
    prop: "startTeachYear",
    check: false,
    placeholder: "请输入培训类别",
    options: [
      {
        label: "一级",
        value: "一级"
      },
      {
        label: "二级",
        value: "二级"
      },
      {
        label: "三级",
        value: "三级"
      }
    ]
  }
]);
//  个人风采
const formDataV1 = ref([
  {
    label: "学历",
    type: "select",
    prop: "createdAt",
    check: false,
    placeholder: "请选择学历",
    options: [
      {
        label: "博士",
        value: "博士"
      },
      {
        label: "硕士",
        value: "硕士"
      },
      {
        label: "本科",
        value: "本科"
      }
    ]
  },
  {
    label: "所学专业",
    type: "input",
    prop: "createdAt",
    check: false,
    placeholder: "请输入所学专业"
  },
  {
    label: "在职",
    type: "input",
    prop: "createdAt",
    check: true,
    placeholder: "请输入任教时间"
  },
  {
    label: "从教时长",
    type: "input",
    prop: "createdAt",
    check: false,
    placeholder: "请输入从教时长"
  },
  {
    label: "毕业证",
    type: "input",
    prop: "createdAt",
    check: false,
    placeholder: "请上传毕业证"
  },
  {
    label: "可任教课程",
    type: "select",
    prop: "createdAt",
    check: false,
    placeholder: "请选择课程",
    options: [
      {
        label: "一级",
        value: "一级"
      },
      {
        label: "二级",
        value: "二级"
      },
      {
        label: "三级",
        value: "三级"
      }
    ]
  },
  {
    label: "教师资格证",
    type: "input",
    prop: "createdAt",
    check: false,
    placeholder: "请上传教师资格证"
  },
  {
    label: "教师资格证号码",
    type: "input",
    prop: "createdAt",
    check: false,
    placeholder: "请输入教师资格证号码"
  },
  {
    label: "职称级别",
    type: "select",
    prop: "createdAt",
    check: false,
    placeholder: "请选择职称级别",
    options: [
      {
        label: "一级",
        value: "一级"
      },
      {
        label: "二级",
        value: "二级"
      },
      {
        label: "三级",
        value: "三级"
      }
    ]
  },
  {
    label: "教案",
    type: "input",
    prop: "createdAt",
    check: false,
    placeholder: "请上传教案"
  }
]);
//  教学背景
const formDataV2 = ref([
  {
    label: "个人头像",
    type: "input",
    prop: "createdAt",
    check: true,
    placeholder: "请输入教学经历"
  },
  {
    label: "擅长课程",
    type: "input",
    prop: "createdAt",
    check: false,
    placeholder: "请介绍一下擅长的课程"
  },
  {
    label: "兴趣爱好",
    type: "input",
    prop: "createdAt",
    check: false,
    placeholder: "请介绍一下兴趣爱好"
  },
  {
    label: "个人采风",
    type: "input",
    prop: "createdAt",
    check: true,
    placeholder: "请输入教学经历"
  }
]);

const rules = reactive({
  name: [{ required: true, message: "专家名 不能为空！", trigger: "blur" }],
  email: [{ required: false, message: "平台邮箱 不能为空！", trigger: "blur" }],
  workUnit: [
    { required: true, message: "工作单位 不能为空！", trigger: "blur" }
  ],
  goodAt: [{ required: true, message: "擅长领域 不能为空！", trigger: "blur" }],
  registerYear: [
    { required: true, message: "平台注册年限 不能为空！", trigger: "blur" }
  ],
  startTeachYear: [
    { required: true, message: "从教开始时间 不能为空！", trigger: "blur" }
  ]
});

const removeEmptyValues = obj => {
  return Object.fromEntries(
    Object.entries(obj).filter(
      ([key, value]) =>
        value !== "" && !(Array.isArray(value) && value.length === 0)
    )
  );
};
function findDifferences(objA, objB) {
  const differences = {};
  // 遍历 objA 的属性
  for (const key in objA) {
    if (objA.hasOwnProperty(key)) {
      if (objB.hasOwnProperty(key)) {
        if (objA[key] !== objB[key]) {
          differences[key] = { oldValue: objA[key], newValue: objB[key] };
        }
      } else {
        differences[key] = { oldValue: objA[key], newValue: undefined };
      }
    }
  }
  // 检查 objB 中是否有 objA 没有的属性
  for (const key in objB) {
    if (objB.hasOwnProperty(key) && !objA.hasOwnProperty(key)) {
      differences[key] = { oldValue: undefined, newValue: objB[key] };
    }
  }
  return differences;
}

function find(val) {
  if (val === "name") {
    return "专家名";
  } else if (val === "gender") {
    return "性别";
  } else if (val === "workUnit") {
    return "工作单位";
  } else if (val === "goodAt") {
    return "擅长领域";
  } else if (val === "registerYear") {
    return "平台注册年限";
  } else if (val === "startTeachYear") {
    return "从教开始时间";
  }
}

// 取消
const cancel = () => {
  startAdd();
};

async function save(text, val) {
  let freezeText = "确定要保存吗？";
  ElMessageBox.confirm(`${freezeText}`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(() => {
      // save1();
      if (!ruleFormRef.value) return;
      ruleFormRef.value.validate((valid, fields) => {
        if (valid) {
          buttonLoading.value = true;
          save1();
        }
      });
    })
    .catch(() => {
      // if (ruleFormRef.value) ruleFormRef.value.resetFields();
      // startAdd();
    });
}

// 保存
const save1 = async () => {
  const result = findDifferences(deepCopy.value, from.value);
  let isEmpty = Object.keys(result).length === 0;
  if (isEmpty) return (buttonLoading.value = false);
  const firstKey = Object.keys(result)[0];
  const text = find(firstKey);
  const operateLog = {
    operateLogType: "PLATFORM_SETTINGS",
    operateType: "修改了" + "平台信息" + text
    // operatorTarget: form.value.name,
  };
  const paramsArg = {
    id: from.value.id,
    createdAt: from.value.createdAt,
    name: from.value.name,
    gender: from.value.gender,
    workUnit: from.value.workUnit,
    goodAt: from.value.goodAt,
    registerYear: from.value.registerYear,
    startTeachYear: from.value.startTeachYear
  };
  console.log("🐳-----paramsArg-----", paramsArg);
  const api = removeEmptyValues(paramsArg);
  const res = await platformInfoSaveOrUpdate(api, operateLog);
  if (res.code === 200) {
    startAdd();
    ElMessage({
      type: "success",
      message: "保存成功"
    });
    buttonLoading.value = false;
  } else {
    ElMessage({
      type: "error",
      message: "保存失败"
    });
    buttonLoading.value = false;
  }
};

//  新增
const newlyAdd = async () => {
  console.log(from.value.customerServiceHotline);

  if (!ruleFormRef.value) return;
  ruleFormRef.value.validate(async (valid, fields) => {
    if (valid) {
      const paramsArg = {
        name: from.value.name,
        gender: from.value.gender,
        workUnit: from.value.workUnit,
        goodAt: from.value.goodAt,
        registerYear: from.value.registerYear,
        startTeachYear: from.value.startTeachYear
      };
      const res = await platformInfoSaveOrUpdate(paramsArg);
      if (res.code === 200) {
        ElMessage({
          type: "success",
          message: "新增成功"
        });
      } else {
        ElMessage({
          type: "error",
          message: "新增失败"
        });
      }
    }
  });
};

const dev = ref(false);
const deepCopy = ref();
const startAdd = async val => {
  //   const res = await platformInfoFind();
  //   if (res.code === 200) {
  //     if (res.data === null) {
  //       dev.value = true;
  //     } else {
  //       from.value = res.data;
  //       console.log("🌳-----res.data-----", res.data);
  //       deepCopy.value = JSON.parse(JSON.stringify(res.data));
  //     }
  //   }
};

const title = ref();
onMounted(() => {
  nextTick();
  title.value = route.query.title;
  const id = route.query.id;
  console.log("🌵-----id-----", id);
  if (title.value !== "edit") {
    formData.value.shift();
  }
});
</script>

<template>
  <div class="commonapp">
    <el-scrollbar class="scrollbar">
      <el-form ref="ruleFormRef" :model="from" :rules="rules">
        <div class="title-text">基本信息</div>
        <el-descriptions :column="2" border :label-width="'200px'">
          <el-descriptions-item
            v-for="(item, index) in formData"
            :key="index"
            label-align="center"
          >
            <template #label>
              <span v-if="item.check" class="star">*</span>{{ item.label }}
            </template>
            <el-form-item
              :prop="item.prop"
              :inline-message="item.check"
              style="margin-bottom: 0"
              :show-message="true"
            >
              <template v-if="item.type === 'input'">
                <el-input
                  v-model.trim="item.label"
                  :placeholder="item.placeholder"
                  :style="{ width: item.width }"
                />
              </template>
              <template v-else-if="item.type === 'select'">
                <el-select v-model="item.prop" :placeholder="item.placeholder">
                  <el-option
                    v-for="it in item.options"
                    :key="it.value"
                    :label="it.label"
                    :value="it.value"
                  />
                </el-select>
              </template>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
        <div class="title-text">个人风采</div>
        <el-descriptions :column="2" border :label-width="'200px'">
          <el-descriptions-item
            v-for="(item, index) in formDataV1"
            :key="index"
            label-align="center"
          >
            <template #label>
              <span v-if="item.check" class="star">*</span>{{ item.label }}
            </template>
            <el-form-item
              :prop="item.prop"
              :inline-message="item.check"
              style="margin-bottom: 0"
              :show-message="true"
            >
              <template v-if="item.type === 'input'">
                <el-input
                  v-model.trim="item.label"
                  :placeholder="item.placeholder"
                  :style="{ width: item.width }"
                />
              </template>
              <template v-else-if="item.type === 'select'">
                <el-select v-model="item.prop" :placeholder="item.placeholder">
                  <el-option
                    v-for="it in item.options"
                    :key="it.value"
                    :label="it.label"
                    :value="it.value"
                  />
                </el-select>
              </template>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
        <div class="title-text">教学背景</div>
        <el-descriptions :column="1" border :label-width="'200px'">
          <el-descriptions-item
            v-for="(item, index) in formDataV2"
            :key="index"
            label-align="center"
          >
            <template #label>
              <span v-if="item.check" class="star">*</span>{{ item.label }}
            </template>
            <el-form-item
              :prop="item.prop"
              :inline-message="item.check"
              style="margin-bottom: 0"
              :show-message="true"
            >
              <template v-if="item.type === 'input'">
                <el-input
                  v-model.trim="item.label"
                  :placeholder="item.placeholder"
                  :style="{ width: item.width }"
                />
              </template>
              <template v-else-if="item.type === 'select'">
                <el-select v-model="item.prop" :placeholder="item.placeholder">
                  <el-option
                    v-for="it in item.options"
                    :key="it.value"
                    :label="it.label"
                    :value="it.value"
                  />
                </el-select>
              </template>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
      </el-form>
    </el-scrollbar>
    <div class="footer">
      <el-button @click="router.go(-1)">取消</el-button>
      <el-button
        v-if="title !== 'edit'"
        :loading="buttonLoading"
        type="primary"
        @click="newlyAdd"
      >
        确认创建
      </el-button>
      <el-button v-else :loading="buttonLoading" type="primary" @click="save">
        保存
      </el-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.commonapp {
  background-color: #fff;
  padding: 20px;

  .scrollbar {
    height: calc(100vh - 190px);
    background-color: #fff;
  }

  .formbox {
    margin-bottom: 20px;
  }

  .footer {
    display: flex;
    justify-content: end;
  }
}

.title-text {
  color: #0e1832;
  font-family: Source Han Sans CN;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  margin: 20px 0 20px 10px;

  &::after {
    content: "";
    flex: 1;
    height: 1px;
    background: #e4e7ed;
    margin-left: 16px;
  }
}

.star {
  margin-right: 3px;
  color: red;
}
</style>
