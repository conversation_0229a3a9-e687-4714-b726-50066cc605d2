import { getAsyncTask as getAsyncTask<PERSON><PERSON> } from "@/api/common";
import { getAsyncTask as getAsyncTaskFunc } from "@iceywu/utils";

export const getAsyncTask = async id => {
  const rules = [
    {
      keys: "code",
      val: 200
    },
    {
      keys: ["data", "complete"],
      val: true
    }
  ];
  const params = { id };
  const { task } = getAsyncTaskFunc(getAsyncTaskApi, { rules, params });
  // console.warn("🐳-----task-----", task);
  return task;
};
