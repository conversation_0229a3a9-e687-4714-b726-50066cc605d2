import { http } from "@/utils/http";

/*  订单管理  */
// 分页查询
export const ordersFindAll = params => {
  return http.request(
    "get",
    "/platform/orders/findAll",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 查询子订单详情
export const getOrderDetails = params => {
  return http.request(
    "get",
    "/platform/orders/getOrderDetails",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
//主订单退款
export const refund = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/orders/refund",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ORDER_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "退单了主订单"
      }
    }
  );
};
//子订单退款
export const confirmRefund = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/orders/refundSub",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ORDER_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "退单了子订单"
      }
    }
  );
};
// 根据订单id查询退款记录
export const findByOrdersId = params => {
  return http.request(
    "get",
    "/platform/refundApply/findByOrdersId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 根据子订单id查询退款记录
export const findBySubOrdersId = params => {
  return http.request(
    "get",
    "/platform/refundRecord/findBySubOrdersId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 平台端确认主订单退款（新增）
export const confirmMainOrderRefund = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/orders/confirmRefund",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ORDER_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "确认了主订单退款"
      }
    }
  );
};
// 平台端确认子订单退款（新增）
export const confirmSubOrderRefund = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/orders/confirmRefundSub",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ORDER_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "确认了子订单退款"
      }
    }
  );
};
// 平台端驳回主订单退款（新增）
export const rejectMainOrderRefund = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/orders/refundRejected",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ORDER_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "驳回了主订单退款"
      }
    }
  );
};
// 平台端驳回子订单退款（新增）
export const rejectSubOrderRefund = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/orders/refundRejectedSub",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ORDER_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "驳回了子订单退款"
      }
    }
  );
};
