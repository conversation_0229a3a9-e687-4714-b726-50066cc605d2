<script setup>
import { ref, reactive, onMounted, watch, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import {
  platformAdd,
  platformfindById,
  platformUpdate,
  platformRole,
  verifyUsername,
  verifyPhone
} from "@/api/platform.js";
import { getPhonecode } from "@/api/leaderLecturer.js";
import { decrypt, encryption } from "@/utils/SM4.js";
import { ElMessage } from "element-plus";
import { requestTo } from "@/utils/http/tool";
import { formatTime, generatePassword } from "@/utils/index.js";
import { to, compareObjects, removeEmptyValues, debounce } from "@iceywu/utils";
import { Hide, View, Loading } from "@element-plus/icons-vue";
import { getBindCodePlatform, getUnbindPlatform } from "@/api/user.js";
import WxQrCode from "@/components/WxQrCode/index.vue";

const router = useRouter();
const route = useRoute();
const ruleFormRef = ref();
const isCaptchaDisabled = ref(false);
const ruleForm = reactive({
  name: "",
  account: "",
  phone: "",
  email: "",
  idNumber: "",
  roleIds: [],
  code: "",
  isBindWx: null
});

// 微信相关变量
const wxQrCodeRef = ref(null);
const showUnbindDialog = ref(false);
const isWxCallbackProcessed = ref(false);
const unbindWxLoading = ref(false);

// 名字校验
const nameTest = (rule, value, callback) => {
  const namePattern = /^[\u4E00-\u9FA5]+$/;
  if (!value) {
    callback(new Error("姓名不能为空"));
  } else if (!namePattern.test(value)) {
    callback(new Error("姓名必须为汉字"));
  } else {
    callback();
  }
};
// 自定义电话号码校验方法
const validatePhoneNumber = async (rule, value, callback) => {
  if (
    oldParams.value.phone &&
    (value === otherNumberObj.value.phone ||
      value === decrypt(oldParams.value.phone))
  ) {
    if (formData.value[3].label === "验证码") {
      // formData.value[2].hasButton = false;
      formData.value.splice(3, 1);
    }
    callback();
    return;
  }
  const phoneRegex = /^1[3-9]\d{9}$/; // 匹配中国大陆手机号码
  // return;
  if (!value) {
    isCaptchaDisabled.value = true;
    return callback(new Error("手机号不能为空"));
  } else if (!phoneRegex.test(value) && route.query.type === "create") {
    isCaptchaDisabled.value = true;
    return callback(new Error("请输入有效的手机号码"));
  } else if (!phoneRegex.test(value) && route.query.type === "edite") {
    isCaptchaDisabled.value = true;
    return callback(new Error("请输入有效的手机号码"));
  } else {
    isCaptchaDisabled.value = false;
    // const oldPhone = oldData.value?.phone;
    // const decryptedPhone = oldPhone ? decrypt(oldPhone) : null;
    // if (value == decryptedPhone) {
    //   return callback();
    // }
    if (formData.value[3].label !== "验证码") {
      // formData.value[2].hasButton = false;
      // 往数组指定位置添加验证码字段
      formData.value.splice(3, 0, {
        label: "验证码",
        type: "input",
        prop: "code",
        span: 1,
        placeholder: "请输入验证码",
        width: "220px",
        check: true
      });

      // 如果form中没有code字段，初始化它
      if (!ruleForm.code) {
        ruleForm.code = "";
      }
    }
    if (value == decrypt(oldParams.value.phoneCt)) {
      callback();
    } else {
      const params = {
        phone: encryption(value)
      };
      try {
        const response = await verifyPhone(params);
        // console.log("🌈-----response-----", response);
        if (response.code === 70008) {
          isCaptchaDisabled.value = true;
          callback(new Error("手机号已存在"));
        } else {
          callback();
        }
      } catch (error) {
        console.log("🌈-----error-----", error);
      }
    }

    // callback();
  }
};
// 自定义账号校验方法
const validateAccount = async (rule, value, callback) => {
  if (value === oldParams.value.account) {
    callback();
    return;
  }
  const accountPattern = /^(adm[in]{1,2}|root|sysadm|[\w-]*(admin)[\w-]*)$/;
  if (!value) {
    callback(new Error("账号不能为空"));
  } else {
    try {
      const response = await verifyUsername({ username: value });
      // console.log("🌈-----response-----", response);
      if (response.code === 10016) {
        callback(new Error("账号已存在"));
      } else {
        // callback();
        if (accountPattern.test(value)) {
          callback(new Error("该用户名不可用，请重新输入"));
        } else {
          callback();
        }
      }
    } catch (error) {
      console.log("🌈-----error-----", error);
    }
    // callback();
  }
};
const ID_NUMBER_REGEX = /(^\d{15}$)|(^\d{17}([0-9X])$)/i;
const CHECK_CODES = ["1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2"];
const FACTORS = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
const validateIdNumber = (rule, value, callback) => {
  if (
    oldParams.value.idNumber &&
    (value === otherNumberObj.value.idNumber ||
      value === decrypt(oldParams.value.idNumber))
  ) {
    callback();
    return;
  }
  if (value && !ID_NUMBER_REGEX.test(value)) {
    return callback(new Error("身份证号格式不正确"));
  }
  if (value && value.length === 18 && !isValidChineseIDChecksum(value)) {
    return callback(new Error("身份证号不正确"));
  }
  callback();
};
function isValidChineseIDChecksum(idNumber) {
  let sum = 0;
  for (let i = 0; i < 17; i++) {
    const digit = parseInt(idNumber[i], 10);
    if (isNaN(digit)) {
      return false; // 遇到非数字字符返回 false
    }
    sum += digit * FACTORS[i];
  }

  const index = sum % 11;
  const expectedCheckCode = CHECK_CODES[index];
  return expectedCheckCode.toUpperCase() === idNumber[17].toUpperCase();
}
const rules = reactive({
  name: [
    { validator: nameTest, trigger: "blur" }
    // { min: 3, max: 5, message: "Length should be 3 to 5", trigger: "blur" }
  ],
  account: [{ required: true, validator: validateAccount, trigger: "blur" }],
  phone: [
    // { required: true, message: "手机号不能为空", trigger: "blur" },
    // { pattern: /^1[3-9]\d{9}$/, message: "手机号格式不正确", trigger: "blur" },
    { required: true, validator: validatePhoneNumber, trigger: "blur" }
  ],
  code: [{ required: true, message: "验证码不能为空", trigger: "change" }],
  idNumber: [{ validator: validateIdNumber, trigger: "blur" }],
  email: [
    {
      validator: (rule, value, callBack) => {
        if (!value) {
          callBack();
          return;
        }
        // 邮箱正则表达式
        const pattern = /^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$/i;

        // 测试邮箱是否匹配模式
        if (!pattern.test(value)) {
          callBack("邮箱格式错误");
          return;
        }
        callBack();
      },
      trigger: "blur"
    }
  ]
});
const fixData = ref([
  {
    id: "1",
    label: "账号ID",
    value: "--",
    width: "400px"
  },
  {
    id: "2",
    label: "创建时间",
    value: "--",
    width: "400px"
  }
]);
const formData = ref([
  {
    label: "姓名",
    type: "input",
    prop: "name",
    check: true,
    placeholder: "请输入姓名",
    width: "400px",
    eye: false,
    maxLength: 10
  },
  {
    label: "账号",
    type: "input",
    prop: "account",
    check: true,
    placeholder: "请输入账号",
    width: "400px",
    eye: false,
    maxLength: 20
  },
  {
    label: "手机号",
    type: "input",
    prop: "phone",
    check: true,
    placeholder: "请输入手机号",
    width: "400px",
    eye: true,
    isView: false,
    buttonText: "获取验证码",
    maxLength: 11
  },
  {
    label: "邮箱",
    type: "input",
    prop: "email",
    placeholder: "请输入邮箱",
    width: "400px",
    eye: false,
    maxLength: 30
  },
  {
    label: "身份证号",
    type: "input",
    prop: "idNumber",
    placeholder: "请输入身份证号",
    width: "400px",
    eye: true,
    isView: false,
    maxLength: 18
  },
  {
    label: "角色",
    type: "checkbox",
    prop: "roleIds",
    placeholder: "请选择角色",
    width: "220px",
    eye: false,
    list: []
  },
  {
    label: "微信绑定",
    type: "img",
    prop: "isBindWx",
    url: "",
    width: "400px",
    height: "120px",
    eye: false
  }
]);
const oldParams = ref({});
const paramsArg = ref({});
const otherNumberObj = ref({});
const idNumberObj = ref({});
const phoneObj = ref({});
// 实时更新初始密码
const newPassword = ref("");
watch(
  () => [ruleForm.name, ruleForm.phone],
  ([name, phone]) => {
    // 前置校验
    if (name && name.length >= 2 && phone && /^1[3-9]\d{9}$/.test(phone)) {
      newPassword.value = generatePassword(name, phone);
    } else {
      newPassword.value = "";
    }
  }
);
// 查询
const getPlatformfindById = async () => {
  const params = {
    id: route.query.id
  };
  const [err, res] = await requestTo(platformfindById(params));
  // console.log("🐬res--------22---------------------->", res);
  if (res) {
    fixData.value[0].value = res.id || 0;
    fixData.value[1].value = res.createdAt ? formatTime(res?.createdAt) : "--";
    ruleForm.name = res.name;
    ruleForm.account = res.account;
    ruleForm.phone = res.phone;
    // ruleForm.phone = decrypt(res?.phoneCt) || "暂无数据";
    ruleForm.email = res.email;
    ruleForm.idNumber = res.idNumber;
    ruleForm.roleIds = res.roles.map(item => item.id) || [];
    ruleForm.isBindWx = res.isBindWx;
    oldParams.value = {
      name: res.name,
      account: res.account,
      phone: res.phoneCt,
      email: res.email,
      idNumber: res.idNumberCt,
      roleIds: res.roles.map(item => item.id) || []
    };
    otherNumberObj.value = {
      phone: res.phone,
      idNumber: res.idNumber
    };
    idNumberObj.value = {
      name: res.name,
      account: res.account,
      email: res.email,
      // idNumber: res?.idNumberCt || "--",
      roleIds: res.roles.map(item => item.id) || []
    };
    // console.log("🦄roles------------------------------>", oldParams.value);
    // console.log('🌳 tableData.value------------------------------>',tableData.value);
  }
  if (err) {
    console.log("err------------------------------>", err);
  }
};
// 查询角色
const getPlatformRole = async () => {
  const [err, res] = await requestTo(platformRole());
  // console.log("🐬res--------22-----333----------------->", res);
  if (res) {
    formData.value[5].list = res.map(item => {
      return {
        name: item.name,
        id: item.id
      };
    });
  }
  if (err) {
    console.log("err------------------------------>", err);
  }
};
const submitLoading = ref(false);
const submitForm = async formEl => {
  if (!formEl) return;
  if (submitLoading.value) return;
  submitLoading.value = true;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      const params = {
        name: ruleForm.name,
        account: ruleForm.account,
        phone: encryption(ruleForm.phone),
        email: ruleForm.email,
        idNumber: encryption(ruleForm.idNumber),
        roleIds: ruleForm.roleIds.map(item => item),
        code: ruleForm.code
      };
      // 创建参数对象，准备应用removeEmptyValues
      let params1 = {
        name: ruleForm.name,
        account: ruleForm.account,
        phone: encryption(ruleForm.phone),
        code: ruleForm.code
      };

      // 单独处理email和idNumber，有值就添加
      if (
        ruleForm.email !== undefined &&
        ruleForm.email !== null &&
        ruleForm.email !== ""
      ) {
        params1.email = ruleForm.email;
      }
      if (
        ruleForm.idNumber !== undefined &&
        ruleForm.idNumber !== null &&
        ruleForm.idNumber !== ""
      ) {
        params1.idNumber = encryption(ruleForm.idNumber);
      }
      // 添加roleIds
      if (ruleForm.roleIds?.length > 0) {
        params1.roleIds = ruleForm.roleIds.map(item => item);
      }

      // 移除空值
      params1 = removeEmptyValues(params1);
      if (route.query.type === "edite") {
        if (
          (ruleForm.phone === otherNumberObj.value.phone ||
            ruleForm.phone === decrypt(oldParams.value.phone)) &&
            (ruleForm.idNumber === otherNumberObj.value.idNumber ||
              ruleForm.idNumber === decrypt(oldParams.value.idNumber))
        ) {
          let editeParams = {
            name: ruleForm.name,
            account: ruleForm.account,
            roleIds: ruleForm.roleIds.map(item => item)
          };
          // 如果email有值，添加到参数中
          if (
            ruleForm.email !== undefined &&
            ruleForm.email !== null &&
            ruleForm.email !== ""
          ) {
            editeParams.email = ruleForm.email;
          }
          // 如果idNumber有值，添加到参数中
          if (
            ruleForm.idNumber !== undefined &&
            ruleForm.idNumber !== null &&
            ruleForm.idNumber !== ""
          ) {
            editeParams.idNumber = encryption(ruleForm.idNumber);
          }
          const resParams = compareObjects(oldParams.value, editeParams);
          paramsArg.value = resParams;
        } else if (
          (ruleForm.phone === otherNumberObj.value.phone ||
            ruleForm.phone === decrypt(oldParams.value.phone)) &&
            (ruleForm.idNumber !== otherNumberObj.value.idNumber ||
              ruleForm.idNumber !== decrypt(oldParams.value.idNumber))
        ) {
          let editeParams = {
            name: ruleForm.name,
            account: ruleForm.account,
            roleIds: ruleForm.roleIds.map(item => item)
          };
          // 如果email有值，添加到参数中
          if (
            ruleForm.email !== undefined &&
            ruleForm.email !== null &&
            ruleForm.email !== ""
          ) {
            editeParams.email = ruleForm.email;
          }
          // 如果idNumber有值，添加到参数中
          if (
            ruleForm.idNumber !== undefined &&
            ruleForm.idNumber !== null &&
            ruleForm.idNumber !== ""
          ) {
            editeParams.idNumber = encryption(ruleForm.idNumber);
          }
          const resParams = compareObjects(idNumberObj.value, editeParams);
          paramsArg.value = resParams;
        } else if (
          (ruleForm.phone !== otherNumberObj.value.phone ||
            ruleForm.phone !== decrypt(oldParams.value.phone)) &&
            (ruleForm.idNumber === otherNumberObj.value.idNumber ||
              ruleForm.idNumber === decrypt(oldParams.value.idNumber))
        ) {
          let editeParams = {
            name: ruleForm.name,
            account: ruleForm.account,
            phone: encryption(ruleForm.phone),
            roleIds: ruleForm.roleIds.map(item => item),
            code: ruleForm.code
          };
          // 如果email有值，添加到参数中
          if (
            ruleForm.email !== undefined &&
            ruleForm.email !== null &&
            ruleForm.email !== ""
          ) {
            editeParams.email = ruleForm.email;
          }
          // 如果idNumber有值，添加到参数中
          if (
            ruleForm.idNumber !== undefined &&
            ruleForm.idNumber !== null &&
            ruleForm.idNumber !== ""
          ) {
            editeParams.idNumber = encryption(ruleForm.idNumber);
          }
          const resParams = compareObjects(idNumberObj.value, editeParams);
          paramsArg.value = resParams;
        } else {
          let editeParams = {
            name: ruleForm.name,
            account: ruleForm.account,
            phone: encryption(ruleForm.phone),
            roleIds: ruleForm.roleIds.map(item => item),
            code: ruleForm.code
          };
          // 如果email有值，添加到参数中
          if (
            ruleForm.email !== undefined &&
            ruleForm.email !== null &&
            ruleForm.email !== ""
          ) {
            editeParams.email = ruleForm.email;
          }
          // 如果idNumber有值，添加到参数中
          if (
            ruleForm.idNumber !== undefined &&
            ruleForm.idNumber !== null &&
            ruleForm.idNumber !== ""
          ) {
            editeParams.idNumber = encryption(ruleForm.idNumber);
          }
          const resParams = compareObjects(oldParams.value, editeParams);
          paramsArg.value = resParams;
        }
        paramsArg.value.id = Number(route.query.id);
        if (!paramsArg.value.roleIds || paramsArg.value.roleIds.Length < 0) {
          paramsArg.value.roleIds = ruleForm.roleIds.map(item => item);
        }

        // 确保email和idNumber总是被包含在参数中（如果有值）
        if (
          ruleForm.email !== undefined &&
          ruleForm.email !== null &&
          ruleForm.email !== ""
        ) {
          paramsArg.value.email = ruleForm.email;
        }
        if (
          ruleForm.idNumber !== undefined &&
          ruleForm.idNumber !== null &&
          ruleForm.idNumber !== ""
        ) {
          if (ruleForm.idNumber === otherNumberObj.value.idNumber) {
            paramsArg.value.idNumber = oldParams.value.idNumber;
          } else if (ruleForm.idNumber === decrypt(oldParams.value.idNumber)) {
            paramsArg.value.idNumber = oldParams.value.idNumber;
          } else {
            paramsArg.value.idNumber = encryption(ruleForm.idNumber);
          }
        }
      }
      const operateLog = {
        operateLogType: "ACCOUNT_MANAGEMENT",
        operateType:
          route.query.type === "create"
            ? `创建了${ruleForm.name}的平台账号`
            : `编辑了${ruleForm.name}的平台账号`
        // operatorTarget: form.value.name,
      };
      console.log("🌵params----------22-------------------->", params);
      console.log(
        "🍭paramsArg.value------------------------------>",
        paramsArg.value
      );
      let resApi =
        route.query.type === "create"
          ? platformAdd(params1, operateLog)
          : platformUpdate(paramsArg.value, operateLog);
      const [err, result] = await to(resApi);

      if (result.code === 200) {
        route.query.type === "create"
          ? ElMessage.success("创建成功")
          : ElMessage.success("修改成功");

        if (route.query.id) {
          router.push({
            path: "/account/platform/account/detail",
            query: {
              id: route.query.id
            }
          });
        }
        router.push("/account/platform/account");
      } else {
        if (result.code === 70008) {
          ElMessage.error("该手机号已被使用");
          return;
        } else if (result.code === 10016) {
          ElMessage.error("该账号已被使用");
          return;
        } else if (result.code === 10027) {
          ElMessage.error("该手机号已存在");
          return;
        } else if (result.code === 10028) {
          ElMessage.error("该身份证号已存在");
          return;
        }
        route.query.type === "create"
          ? ElMessage.error("创建失败")
          : ElMessage.error("修改失败");
        submitLoading.value = false;
      }
      // console.log("🐳res------------------------------>", res);
      //  if()
    } else {
      console.log("error submit!", fields);
    }
    submitLoading.value = false;
  });
};
// 获取验证码
const getCaptcha = async phoneCode => {
  const phoneRegex = /^1[3-9]\d{9}$/; // 匹配中国大陆手机号码
  if (!phoneCode) {
    isCaptchaDisabled.value = true;
    ElMessage({ message: "手机号不能为空" });
    return;
  }
  if (!phoneRegex.test(phoneCode)) {
    isCaptchaDisabled.value = true;
    ElMessage({ message: "电话号码格式不正确", type: "warning" });
    return;
  }
  const params = {
    phone: encryption(phoneCode),
    codeType: "VERIFICATION_CODE"
  };
  isCaptchaDisabled.value = false;
  // return;
  const { code, msg } = await getPhonecode(params);
  if (code == 200) {
    ElMessage({
      message: "验证码已发送",
      type: "success"
    });
  }

  console.log("🐳-----params-----", params);
};
// 解密后的手机号或解密后的身份证号
const numberFn = (num, type) => {
  const str = num.toString();
  if (type === "phone") {
    return str.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2");
  } else {
    return str.replace(/(\d{3})\d{11}(\d{4})/, "$1***********$2");
  }
};
const isViewFn = val => {
  val.isView = !val.isView;
  if (val.label === "手机号") {
    if (val.isView === false) {
      ruleForm.phone = otherNumberObj.value.phone;
    } else {
      ruleForm.phone = decrypt(oldParams.value.phone);
    }
  } else {
    if (val.isView === false) {
      ruleForm.idNumber = otherNumberObj.value.idNumber;
    } else {
      ruleForm.idNumber = decrypt(oldParams.value.idNumber);
    }
  }
};
// 输入内容中间去空和数字不能输文字
const inputChange = (value, prop) => {
  const numberWhite = ["phone", "idNumber"];
  if (numberWhite.includes(prop)) {
    const filteredValue = value.replace(/[^\dX]/gi, "");
    ruleForm[prop] = filteredValue.toUpperCase();
  } else {
    ruleForm[prop] = value.replace(/\s/g, "");
  }
};
// 微信回调处理
const handleWxCallback = (code, state) => {
  // 检查是否已经处理过此次绑定请求
  const processedKey = `wx_bind_processed_${code}_${state}`;
  if (sessionStorage.getItem(processedKey)) {
    console.log("已处理过此绑定请求，不再重复处理");
    isWxCallbackProcessed.value = true;
    return;
  }
  const storedState = sessionStorage.getItem("wx_login_state");

  // 状态码 是否一致
  if (state !== storedState) {
    ElMessage({
      message: "微信状态不一致，请重新扫码",
      type: "error"
    });
    isWxCallbackProcessed.value = true; // 标记为已处理
    return;
  }

  console.log(ruleForm.isBindWx, "isbind");

  // 状态码一致，处理微信绑定
  if (ruleForm.isBindWx) {
    // 如果当前已绑定微信，这里可以处理解绑逻辑（如果需要的话）
    ElMessage.info("当前账号已绑定微信");
    isWxCallbackProcessed.value = true; // 标记为已处理
  } else {
    // 如果当前未绑定微信，处理绑定逻辑
    bindCode(code, processedKey);
  }
};

// 绑定账号
const bindCode = async (code, processedKey) => {
  console.log(code, "code");
  const params = {
    code: code,
    userId: route.query.id,
    userType: "PLATFORM_ADMIN"
  };

  try {
    const res = await getBindCodePlatform(params, {
      operateLogType: "ACCOUNT_MANAGEMENT",
      operateType: `对账号“${ruleForm.account}”完成微信绑定操作`
    });
    if (res.code === 200) {
      ElMessage.success("绑定成功");

      // 记录已处理状态到会话存储
      sessionStorage.setItem(processedKey, "true");
      // 重新获取用户信息
      await getPlatformfindById();

      // 重置二维码组件状态
      if (wxQrCodeRef.value) {
        wxQrCodeRef.value.resetInit();
      }

      // 标记为已处理
      isWxCallbackProcessed.value = true;
    } else {
      ElMessage.warning(res.msg);
      isWxCallbackProcessed.value = true; // 标记为已处理
    }
  } catch (err) {
    isWxCallbackProcessed.value = true; // 标记为已处理
    throw new Error(err);
  }
};

// 解绑微信
const handleChangeWx = debounce(
  async () => {
    if (unbindWxLoading.value) return;
    unbindWxLoading.value = true;
    try {
      const params = {
        userId: route.query.id,
        userType: "PLATFORM_ADMIN"
      };

      const res = await getUnbindPlatform(params, {
        operateLogType: "ACCOUNT_MANAGEMENT",
        operateType: `对账号“${ruleForm.account}”完成微信解绑操作`
      });

      if (res.code === 200) {
        ElMessage.success("微信解绑成功");

        // 关闭对话框
        showUnbindDialog.value = false;

        await getPlatformfindById();
      }
      console.log("微信解绑完成");
    } catch (error) {
      ElMessage.error("微信解绑失败，请重试");
    } finally {
      unbindWxLoading.value = false;
    }
  },
  1000,
  { immediate: true }
);

// 构建包含当前 query 参数的重定向路径
const redirectPathWithQuery = computed(() => {
  const currentPath = route.path;
  const queryParams = new URLSearchParams();

  // 将当前的 query 参数添加到 URLSearchParams 中，排除微信回调参数
  Object.keys(route.query).forEach(key => {
    // 排除微信回调相关的参数
    if (key !== "code" && key !== "state") {
      if (route.query[key] !== null && route.query[key] !== undefined) {
        queryParams.append(key, route.query[key]);
      }
    }
  });

  // 如果有 query 参数，则构建完整路径
  const queryString = queryParams.toString();
  return queryString ? `${currentPath}?${queryString}` : currentPath;
});

// 检测是否有微信回调参数且未处理
const hasWxCallbackParams = computed(() => {
  return !!(
    route.query.code &&
    route.query.state &&
    !isWxCallbackProcessed.value
  );
});

// 监听路由参数变化
watch(
  () => route.query,
  newQuery => {
    const { code, state } = newQuery;
    // console.log(code, "code");
    // console.log(state, "state");

    if (code && state && !isWxCallbackProcessed.value) {
      handleWxCallback(code, state);
    }
  },
  { immediate: true }
);
onMounted(() => {
  if (route.query.id) {
    getPlatformfindById();
  }
  getPlatformRole();
});
</script>

<template>
  <div class="localendAccount-add">
    <!-- <div class="title">
      <span class="title-text" @click="router.go(-1)">局端账号</span>
      <span class="line">\</span>
      <span class="create-title">创建账号</span>
    </div> -->
    <div class="localendAccount-container">
      <el-scrollbar style="height: calc(100vh - 234px); margin-bottom: 30px">
        <el-descriptions
          v-if="route.query.id"
          class="descrip-info"
          title=""
          :column="2"
          border
          :label-width="'200px'"
        >
          <template v-for="(item, index) in fixData" :key="index">
            <el-descriptions-item label-align="center">
              <template #label>
                <div class="cell-item">{{ item.label }}</div>
              </template>
              <div>{{ item.value }}</div>
            </el-descriptions-item>
          </template>
        </el-descriptions>

        <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules">
          <el-descriptions title="" :column="2" border :label-width="'200px'">
            <el-descriptions-item
              v-for="(item, index) in formData"
              :key="index"
              label-align="center"
              :span="
                item.prop === 'phone'
                  ? formData.some(i => i.prop === 'code')
                    ? 1
                    : 2
                  : item.prop === 'code'
                    ? 1
                    : 2
              "
            >
              <template #label>
                <span v-if="item.check" class="star">*</span>{{ item.label }}
              </template>
              <el-form-item
                :prop="item.prop"
                :inline-message="true"
                style="margin-bottom: 0"
                :show-message="true"
                error-placement="right"
              >
                <!-- input输入 -->
                <template v-if="item.type === 'input'">
                  <el-input
                    v-if="
                      item.eye === true &&
                      route.query.type === 'edite' &&
                      ruleForm[item.prop]
                    "
                    v-model.trim="ruleForm[item.prop]"
                    :placeholder="item.placeholder"
                    :style="{ width: item.width }"
                    :disabled="item.isView === true ? false : true"
                    :maxlength="item.maxLength"
                    :show-word-limit="item.maxLength"
                    @input="inputChange($event, item.prop)"
                  >
                    <template #suffix>
                      <el-icon
                        v-if="item.isView === true"
                        style="cursor: pointer"
                        @click="isViewFn(item)"
                      >
                        <View />
                      </el-icon>
                      <el-icon
                        v-else
                        style="cursor: pointer"
                        @click="isViewFn(item)"
                      >
                        <Hide />
                      </el-icon>
                    </template>
                  </el-input>
                  <el-input
                    v-else
                    v-model.trim="ruleForm[item.prop]"
                    :placeholder="item.placeholder"
                    :style="{ width: item.width }"
                    :maxlength="item.maxLength"
                    :show-word-limit="item.maxLength"
                    @input="inputChange($event, item.prop)"
                  />
                  <div v-if="item.prop === 'code'" class="Vacode">
                    <el-button
                      v-countdown="{
                        value: 60,
                        callback: () => getCaptcha(ruleForm.phone),
                        countdownText: 's后重新获取',
                        loadingText: '发送中...'
                      }"
                    >
                      获取验证码
                    </el-button>
                  </div>
                </template>
                <!-- 角色选择 -->
                <template v-if="item.type === 'checkbox'">
                  <el-checkbox-group v-model="ruleForm.roleIds">
                    <el-checkbox
                      v-for="(option, index) in item.list"
                      :key="index"
                      :label="option.name"
                      :value="option.id"
                    />
                  </el-checkbox-group>
                </template>

                <!-- 二维码展示 -->
                <template
                  v-else-if="
                    item.type === 'img' && route.query.type === 'edite'
                  "
                >
                  <span v-if="!ruleForm.isBindWx" class="isQR">
                    当前未绑定
                  </span>
                  <div v-else>
                    <span>已绑定,
                      <el-link
                        type="primary"
                        underline="hover"
                        @click="showUnbindDialog = true"
                      >
                        解绑
                      </el-link>
                    </span>
                  </div>
                  <div v-if="!ruleForm.isBindWx" class="codeQR">
                    <!-- 只有在没有微信回调参数时才显示二维码 -->
                    <WxQrCode
                      v-if="!hasWxCallbackParams"
                      ref="wxQrCodeRef"
                      :redirectPath="redirectPathWithQuery"
                    />
                    <!-- 有微信回调参数时显示处理中状态 -->
                    <div v-else class="processing-status">
                      <el-icon class="is-loading">
                        <Loading />
                      </el-icon>
                      <span>正在处理微信绑定...</span>
                    </div>
                  </div>
                </template>
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>
          <el-form-item>
            <!-- 初始密码为 {{ "1111" }} -->
            <div v-if="route.query.type === 'create'" class="password-info">
              密码生成规则：姓名首字母+手机号后6位+@
              <span v-if="newPassword" class="password">初始密码为： {{ newPassword }}</span>
            </div>
          </el-form-item>
        </el-form>
      </el-scrollbar>

      <div class="buttons">
        <el-button @click="router.go(-1)">取消</el-button>
        <el-button
          type="primary"
          class="create"
          :loading="submitLoading"
          @click="submitForm(ruleFormRef)"
        >
          {{ route.query.type === "create" ? "确认新建" : "保存" }}
        </el-button>
      </div>
    </div>

    <!-- 解绑微信确认对话框 -->
    <el-dialog
      v-model="showUnbindDialog"
      title="解绑微信"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="dialog-content">
        <span>确定要解绑当前微信账号吗？</span>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showUnbindDialog = false">取消</el-button>
          <el-button
            type="primary"
            :loading="unbindWxLoading"
            @click="handleChangeWx"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.localendAccount-add {
  box-sizing: border-box;
  // padding: 22px 20px;
  font-size: 14px;
  //   font-family: PingFangSC-regular;
  color: #101010;
  .title {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .title-text {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 76px;
      height: 28px;
      margin-right: 10px;
      cursor: pointer;
      background-color: #f6e5d4;
      border: 1px solid #ff8c19;
    }

    .line {
      margin-right: 20px;
    }
  }
  .localendAccount-container {
    box-sizing: border-box;
    // width: 100%;
    // height: calc(100vh - 114px);
    padding: 20px 20px;
    background-color: #fff;
    position: relative;
    .descrip-info {
      margin-bottom: 20px;
    }
    .password-info {
      margin-top: 20px;
      .password {
        margin-left: 20px;
      }
    }
    .buttons {
      display: flex;
      // justify-content: space-between;
      justify-content: flex-end;
      width: 100%;
      // margin: 0 auto;
      // margin-top: 28vh;
      // position: absolute;
      // bottom: 30px;

      .create {
        // margin-right: 80px;
      }
    }
  }

  .star {
    margin-right: 3px;
    color: red;
  }
  .Vacode {
    margin-left: 20px;
  }

  .isQR {
    margin-right: 240px;
  }

  .codeQR {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 0;
  }

  .processing-status {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding: 20px;
    color: #666;
    font-size: 14px;
  }
}
:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  width: 240px;
  background: #e1f5ff;
}
.el-link {
  line-height: 1.2;
  margin-bottom: 2px;
}
</style>
