import { reactive, ref, computed, onMounted, onActivated } from "vue";
import { useTable } from "plus-pro-components";
import { findall } from "@/api/facultyReview";
import { getDictOptions } from "@/api/teacherResourcePool";
import { requestTo } from "@/utils/http/tool";
import { removeEmptyValues } from "@iceywu/utils";
import { useRouter } from "vue-router";
import { decrypt } from "@/utils/SM4";
import { View, Hide } from "@element-plus/icons-vue";

export function useFacultyReview() {
  const router = useRouter();
  const educationOptions = ref([]);
  const majorOptions = ref([]);

  const processData = nodes => {
    return nodes.map(node => {
      const { leaf, ...newNode } = node;
      if (newNode.children && newNode.children.length > 0) {
        newNode.children = processData(newNode.children);
      }
      return newNode;
    });
  };

  const getOptions = async (parentId, optionsRef) => {
    const { code, data } = await getDictOptions({ parentId });
    if (code === 200 && data) {
      optionsRef.value = processData(data);
    }
  };

  getOptions("453", educationOptions);
  getOptions("548", majorOptions);
  const state = reactive({
    query: {},
    tableData: [],
    pagination: {
      page: 1,
      size: 20,
      total: 0
    }
  });

  const { buttons } = useTable();
  const columns = [
    {
      label: "教师姓名",
      minWidth: 100,
      prop: "name"
    },
    {
      label: "性别",
      minWidth: 70,
      prop: "gender",
      hideInSearch: true,
      valueType: "select",
      options: [
        { value: 0, label: "女" },
        { value: 1, label: "男" }
      ]
    },
    {
      label: "联系方式",
      minWidth: 150,
      prop: "phone",
      hideInSearch: true,
      render: (text, { row }) => {
        const eye_phone = row => {
          row.type_phone = !row.type_phone;
        };
        return (
          <div style="display: flex; align-items: center;">
            <span>
              {row.phone
                ? row.type_phone
                  ? decrypt(row.phoneCt)
                  : row.phone
                : "--"}
            </span>
            {row.phone && (
              <el-icon
                style="margin-left: 10px; cursor: pointer;"
                onClick={() => eye_phone(row)}
              >
                {row.type_phone ? <View /> : <Hide />}
              </el-icon>
            )}
          </div>
        );
      }
    },
    {
      label: "学历",
      minWidth: 100,
      prop: "educationLevel",
      valueType: "cascader",
      options: educationOptions,
      fieldProps: {
        props: {
          expandTrigger: "hover",
          value: "name",
          label: "name",
          children: "children",
          emitPath: false
        },
        "show-all-levels": false
      }
    },
    {
      label: "专业",
      minWidth: 150,
      prop: "major",
      valueType: "cascader",
      hideInSearch: true,
      options: majorOptions,
      fieldProps: {
        props: {
          expandTrigger: "hover",
          value: "name",
          label: "name",
          children: "children",
          emitPath: false
        },
        "show-all-levels": false
      }
    },
    {
      label: "审核状态",
      minWidth: 120,
      prop: "auditState",
      valueType: "select",
      options: [
        { value: "PENDING_REVIEW", label: "待审核" },
        { value: "APPROVED", label: "已通过" },
        { value: "REJECTED", label: "不通过" }
      ],
      render(value) {
        const options = [
          { value: "PENDING_REVIEW", label: "待审核" },
          { value: "APPROVED", label: "已通过" },
          { value: "REJECTED", label: "不通过" },
          { value: "CANCEL", label: "无" }
        ];
        const option = options.find(opt => opt.value === value);

        const color =
          value === "REJECTED"
            ? "rgb(245, 108, 108)"
            : value === "APPROVED" || value === "PENDING_REVIEW"
              ? "rgb(64, 158, 255)"
              : "";

        return (
          <p style={{ padding: 10, color }}>{option ? option.label : value}</p>
        );
      }
    },
    {
      label: "审核类型",
      minWidth: 150,
      prop: "applyType",
      valueType: "select",
      options: [
        { value: "TEACHER_DATABASE_ADD", label: "新增" },
        { value: "TEACHER_DATABASE_UPDATE", label: "修改" }
      ]
    },
    {
      label: "创建时间",
      minWidth: 200,
      prop: "createdAt",
      valueType: "date-picker",
      hideInSearch: true
    }
  ];

  // 按钮
  buttons.value = [
    {
      text: "详情",
      code: "view",
      props: { type: "primary" },
      show: computed(() => true),
      onClick({ row }) {
        const routePath =
          row.applyType === "TEACHER_DATABASE_ADD" ? "addDetail" : "editDetail";
        router.push({
          path: `/platform/facultyReview/${routePath}`,
          query: { reviewId: row.id }
        });
      }
    }
  ];

  const plusPageInstance = ref(null);

  const beforeSearchSubmit = params => {
    const result = { ...params };

    if (params.page !== undefined) {
      result.page = params.page - 1;
    }

    if (params.createdAt) {
      result.startTime = new Date(params.createdAt[0]).getTime();
      result.endTime = new Date(params.createdAt[1]).getTime();
      delete result.createdAt;
    }

    return removeEmptyValues(result);
  };

  const getList = async (params = {}) => {
    const adjustedParams = { ...params };
    if (adjustedParams.page !== undefined) {
      adjustedParams.page = Number(adjustedParams.page) - 1;
    }

    const [err, res] = await requestTo(findall(adjustedParams));

    if (res) {
      res.content.forEach(item => {
        item.type_phone = false;
      });
      return {
        data: res.content,
        total: res.totalElements
      };
    }
    if (err) {
      console.error(err);
      return {
        data: [],
        total: 0
      };
    }
  };

  return {
    state,
    columns,
    buttons,
    plusPageInstance,
    beforeSearchSubmit,
    getList
  };
}
