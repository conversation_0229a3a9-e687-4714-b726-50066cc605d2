<script setup>
import { id } from "element-plus/es/locale/index.mjs";
import { onMounted, ref, defineEmits } from "vue";
import { requestTo } from "@/utils/http/tool";
import { pinyin } from "pinyin-pro";
import { decrypt, encryption } from "@/utils/SM4.js";
import { ElMessage } from "element-plus";
import { useUserStoreHook } from "@/store/modules/user";

const props = defineProps({
  title: {
    type: String
  },
  api: {
    type: String,
    default: ""
  },
  id: {
    type: Number
  },
  name: {
    type: String,
    default: ""
  },
  phone: {
    type: String,
    default: ""
  },
  account: {
    type: String,
    default: ""
  }
});
const emit = defineEmits(["giveup", "close", "cancel"]);
onMounted(() => {
  console.log(props.title);
  generatePassword();
});
const info = localStorage.getItem("user-info");
const infoData = localStorage.getItem("userInfoData");
const newPassword = ref("");
const getListLoading = ref(false);
const btnOKClick = async data => {
  if (getListLoading.value) {
    return;
  }
  getListLoading.value = true;
  const operateLog = {
    operateLogType: "ACCOUNT_MANAGEMENT",
    operateType: "重置" + props.name + "密码了"
    // operatorTarget: form.value.name,
  };
  let paramsData = {
    id: props.id,
    password: encryption(newPassword.value)
  };
  try {
    const { code, data, msg } = await props.api(paramsData, operateLog);
    if (code == 200) {
      ElMessage({
        message: "重置密码成功",
        type: "success"
      });
      /** 退出登录 */
      if (JSON.parse(info)?.roleTarget === "局端管理员") {
        if (JSON.parse(infoData)?.account == props.account) {
          useUserStoreHook().logOut();
        }
      }
    }
  } catch (error) {
    console.error("生成密码失败：", error);
  }
  getListLoading.value = false;
  emit("cancel");
};
// 生成管理员账号的方法
const generatePassword = () => {
  try {
    // 1. 获取姓名首字母
    const initials = pinyin(props.name, {
      pattern: "first", // 只保留首字母
      toneType: "none", // 不显示声调
      type: "array" // 返回数组格式
    })
      .join("")
      .toLowerCase();

    // 2. 获取手机号后6位
    const phonePart = props.phone.slice(-6);

    // 3. 组合生成密码
    newPassword.value = `${initials}${phonePart}@`;
    console.log("🌈-----newPassword.value-----", newPassword.value);
  } catch (error) {
    console.error("生成密码失败：", error);
    // password.value = '生成失败，请检查输入';
  }
};
</script>

<template>
  <div class="popup">
    <div class="title">
      <span class="title_text">{{ title }} </span>
    </div>
    <div class="content">
      <div class="info">
        <p>重置密码的账号 {{ props.account }}</p>
        <p>密码将被重置为 {{ newPassword }}</p>
        <p>密码生成规则：姓名首字母+手机号后6位+@</p>
      </div>
      <div class="btns">
        <div class="btn_cancel" @click="$emit('cancel')">取消</div>
        <div class="btn_cancel" @click="btnOKClick">确认重置</div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.popup {
  width: 600px;
  height: 400px;
  background: #fff;

  .title {
    font-size: 14px;
    line-height: 40px;
    color: #fff;
    text-align: center;
    background: #333;
  }

  .content {
    .info {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 300px;

      :nth-child(2) {
        margin: 20px 0 20px 0px;
      }
    }

    .btns {
      display: flex;
      justify-content: space-between;
      padding: 0 20px;
      font-size: 14px;
      color: #fff;

      .btn_cancel {
        padding: 8px 13px;
        cursor: pointer;
        background: #e6983a;
        border-radius: 8px;
      }

      :nth-child(2) {
        background: #409eff;
      }
    }
  }
}
</style>
