import { Edit, Hide, View } from "@element-plus/icons-vue";
import dayjs from "dayjs";
import { onMounted, reactive, ref, onActivated } from "vue";
import { parentIsFreeze, parentFindAll } from "@/api/parentManage.js";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
import { decrypt, encryption } from "@/utils/SM4.js";

export function useRole() {
  const columns = [
    {
      label: "家长ID",
      prop: "id",
      minWidth: 90,
      formatter: ({ id }) => {
        return id || "--";
      }
    },
    {
      label: "家长姓名",
      prop: "name",
      width: 200,
      cellRenderer: ({ row }) => (
        <el-text line-clamp="1">{row.name || "--"}</el-text>
      )
    },
    {
      label: "家长手机号",
      prop: "phone",
      minWidth: 90,
      cellRenderer: ({ row }) =>
        row.phone
? (
          <div style="display: flex; justify-content: left;">
            {row.isName === true
? (
              <div style="width: 90px">{decrypt(row.phoneCt)}</div>
            )
: (
              <div style="width: 90px">{row.phone}</div>
            )}

            {row.isName === true
? (
              <el-icon
                style=" margin-left: 24px; margin-top: 4px;"
                onClick={() => imgAdd(row)}
              >
                <View />
              </el-icon>
            )
: (
              <el-icon
                style=" margin-left: 24px; margin-top: 4px;"
                onClick={() => imgAdd(row)}
              >
                <Hide />
              </el-icon>
            )}
          </div>
        )
: (
          <div>--</div>
        )
    },
    {
      label: "创建时间",
      minWidth: 90,
      prop: "createdAt",
      formatter: ({ createdAt }) => {
        return createdAt
          ? dayjs(createdAt).format("YYYY-MM-DD HH:mm:ss")
          : "--";
      }
    },
    {
      label: "账号状态",
      minWidth: 90,
      prop: "freeze",
      cellRenderer: ({ row }) => (
        <div style={{ color: row.freeze ? "#f56c6c" : "" }}>
          {row.freeze ? "冻结" : "正常"}
        </div>
      )
    },
    {
      label: "操作",
      fixed: "right",
      width: 180,
      slot: "operation"
    }
  ];

  const courseTypeoptions = ref([
    {
      value: "",
      label: "全部"
    },
    {
      value: false,
      label: "正常"
    },
    {
      value: true,
      label: "冻结"
    }
  ]);

  const router = useRouter();
  const dataList = ref([]);
  const loadingTable = ref(false);
  const pagination = {
    total: 0,
    pageSize: 15,
    currentPage: 1,
    background: true
  };

  const from = reactive({
    name: "",
    phone: "",
    freeze: "",
    time: []
  });
  const startAdd = async () => {
    const paramsArg = {
      page: 0,
      size: 15,
      sort: "createdAt,desc"
    };
    const [err, res] = await requestTo(parentFindAll(paramsArg));
    if (res) {
      dataList.value = res.content;
      pagination.total = res.totalElements;
      // pagination.pageSize = res.size;
      // pagination.currentPage = 1;
    }
    if (err) {
    }
  };
  const removeEmptyValues = obj => {
    return Object.fromEntries(
      Object.entries(obj).filter(
        ([key, value]) =>
          value !== null &&
          value !== "" &&
          !(Array.isArray(value) && value.length === 0)
      )
    );
  };
  const startAdd1 = async val => {
    // console.log("🍧-----val-----", val);
    const paramsArg = {
      name: val.name || "",
      phone: val.phone || "",
      freeze: val.freeze,
      startTime: val.startTime || "",
      endTime: val.endTime || "",
      page: val.page || 0,
      size: val.size || "",
      sort: "createdAt,desc"
    };
    // console.log("🌵-----paramsArg-----", paramsArg);
    let aee = removeEmptyValues(paramsArg);
    const [err, res] = await requestTo(parentFindAll(aee));
    if (res) {
      dataList.value = res.content;
      pagination.total = res.totalElements;
    }
    if (err) {
    }
  };
  // 搜索
  const onSearch = () => {
    let time1 = from.time !== null ? new Date(from.time[0]).getTime() : "";
    let time2 = from.time !== null ? new Date(from.time[1]).getTime() : "";
    const text = {
      name: from.name || "",
      phone: from.phone || "",
      freeze: from.freeze,
      startTime: time1 || "",
      endTime: time2 || "",
      page: pagination.currentPage - 1 || "",
      size: pagination.pageSize || ""
    };
    startAdd1(text);
  };

  // 每页多少条
  async function handleSizeChange(val) {
    pagination.pageSize = val;
    let time1 = from.time !== null ? new Date(from.time[0]).getTime() : "";
    let time2 = from.time !== null ? new Date(from.time[1]).getTime() : "";
    const paramsArg = {
      name: from.name || "",
      phone: from.phone || "",
      freeze: from.freeze,
      startTime: time1 || "",
      endTime: time2 || "",
      page: 0,
      size: val
    };
    startAdd1(paramsArg);
  }
  // 前往页数
  async function handleCurrentChange(val) {
    pagination.currentPage = val;
    let time1 = from.time !== null ? new Date(from.time[0]).getTime() : "";
    let time2 = from.time !== null ? new Date(from.time[1]).getTime() : "";
    const paramsArg = {
      name: from.name || "",
      phone: from.phone || "",
      freeze: from.freeze,
      startTime: time1 || "",
      endTime: time2 || "",
      page: val - 1,
      size: pagination.pageSize
    };
    startAdd1(paramsArg);
  }

  // 订单管理
  const toAdd = (text, val) => {
    router.push({
      path: "/parentManagement/componts/parentOrderManage",
      query: { id: val?.id }
    });
  };
  // 详情
  const openDialog = (text, val) => {
    router.push({
      path: "/parentManagement/componts/parentDetails",
      query: { id: val?.id }
    });
  };
  const isFreezeAdd = async (text, val) => {
    const operateLog = {
      operateLogType: "PARENT_MANAGEMENT",
      operateType: text + "了" + val.name
      // operatorTarget: form.value.name,
    };
    const paramsArg = {
      id: val.id,
      freeze: text === "解冻" ? false : true
    };
    const { code } = await parentIsFreeze(paramsArg, operateLog);
    if (code === 200) {
      onSearch();
      ElMessage({
        type: "success",
        message: text === "解冻" ? "解冻成功" : "冻结成功"
      });
    } else {
      ElMessage({
        type: "error",
        message: text === "解冻" ? "解冻失败" : "冻结失败"
      });
    }
  };
  //  冻结 解冻
  const isFreezeApi = (text, val) => {
    let freezeText = `确定要${text}该账号吗？`;
    ElMessageBox.confirm(`${freezeText}`, `确定${text}`, {
      confirmButtonText: "确定",
      cancelButtonText: "取消"
    })
      .then(() => {
        isFreezeAdd(text, val);
      })
      .catch(() => {
        // ElMessage({
        //   type: 'info',
        //   message: 'Delete canceled',
        // })
      });
  };
  function imgAdd(val) {
    dataList.value.map(it => {
      if (it.id === val.id) {
        it.isName = !val.isName;
      }
    });
  }
  // 重置
  const setData = () => {
    from.name = "";
    from.phone = "";
    from.freeze = "";
    from.time = [];
    startAdd();
  };

  const handleInput = value => {
    const regex = /^[0-9+\-*/]*$/;
    let regExp = /^\d+$/;
    const phoneRegex = /^1[3-9]\d{9}$/; // 匹配中国大陆手机号码
    if (!regex.test(value)) {
      from.phone = "";
      ElMessage({
        type: "error",
        message: "请输入正确的手机号"
      });
    }
  };

  onActivated(() => {
    let time1 = from.time !== null ? new Date(from.time[0]).getTime() : "";
    let time2 = from.time !== null ? new Date(from.time[1]).getTime() : "";
    const text = {
      name: from.name || "",
      phone: from.phone || "",
      freeze: from.freeze || "",
      startTime: time1 || "",
      endTime: time2 || "",
      page: pagination.currentPage - 1 || "",
      size: pagination.pageSize || ""
    };
    startAdd1(text);
  });

  onMounted(() => {
    startAdd();
  });

  return {
    from,
    pagination,
    dataList,
    columns,
    onSearch,
    setData,
    handleInput,
    toAdd,
    openDialog,
    isFreezeApi,
    handleCurrentChange,
    handleSizeChange,
    loadingTable,
    courseTypeoptions
  };
}
