<script setup lang="ts">
import { computed, ref, onMounted } from "vue";
import { requestTo } from "@/utils/http/tool";
import {
  findSnapshotByApplyId,
  findUpdateDetailByApplyId,
  findByExpertApplyId,
  reviewApplication
} from "@/api/facultyReview";
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { removeEmptyValues } from "@iceywu/utils";

const route = useRoute();

// 审核弹窗相关
const rejectDialogVisible = ref(false);
const approveDialogVisible = ref(false);
const rejectReason = ref("");

// 打开驳回弹窗
const openRejectDialog = () => {
  rejectDialogVisible.value = true;
  rejectReason.value = "";
};

// 打开审核通过弹窗
const openApproveDialog = () => {
  approveDialogVisible.value = true;
};

// 提交审核结果
const submitAudit = async isApproved => {
  try {
    const params = removeEmptyValues({
      applyId: route.query.reviewId,
      auditState: isApproved ? "APPROVED" : "REJECTED",
      auditComment: isApproved ? "" : rejectReason.value
    });
    const operateLog = {
      operateLogType: "TEACHER_DATABASE",
      operateType: isApproved ? "审核通过了" : "审核驳回了",
      operatorTarget: `${expertApplyInfo.value.name}的师资申请`
    };

    const { code, data } = await reviewApplication(params, operateLog);

    if (code === 200) {
      ElMessage.success(isApproved ? "审核通过成功" : "驳回成功");
      // 重新加载数据
      fetchData();
    } else {
      ElMessage.error(isApproved ? "审核通过失败" : "驳回失败");
    }
  } catch (error) {
    console.error("审核操作失败:", error);
    ElMessage.error("审核操作失败");
  } finally {
    // 关闭弹窗
    rejectDialogVisible.value = false;
    approveDialogVisible.value = false;
  }
};

const auditStateMap = {
  PENDING_REVIEW: "待审核",
  APPROVED: "已通过",
  REJECTED: "不通过",
  CANCEL: "用户已取消申请"
};

const applyTypeMap = {
  TEACHER_DATABASE_ADD: "新增",
  TEACHER_DATABASE_UPDATE: "修改"
};

const pageTitle = computed(() => {
  return (
    auditStateMap[route.query.auditState as keyof typeof auditStateMap] ||
    "教师信息变更对比"
  );
});

const statusClass = computed(() => {
  const state = expertApplyInfo.value.auditState;
  if (state === "REJECTED") {
    return "rejected";
  }
  if (state === "CANCEL") {
    return "cancel";
  }
  return "";
});

// 性别映射
const genderMap = {
  1: "男",
  2: "女",
  0: "未知"
};

// 学历映射
const educationLevelMap = {
  DOCTOR: "博士",
  MASTER: "硕士",
  BACHELOR: "本科",
  COLLEGE: "专科",
  HIGH_SCHOOL: "高中",
  JUNIOR_HIGH_SCHOOL: "初中",
  PRIMARY_SCHOOL: "小学"
};

// 职称级别映射
const professionalTitleMap = {
  UNRATED: "未定级",
  JUNIOR: "初级",
  INTERMEDIATE: "中级",
  ASSOCIATE_SENIOR: "副高级",
  SENIOR: "高级"
};

const loading = ref(true);
const originalData = ref({});
const changedData = ref({});
const expertApplyInfo = ref({
  id: "",
  createdAt: "",
  updatedAt: "",
  name: "",
  phone: "",
  phoneCt: "",
  educationLevel: "",
  major: "",
  auditState: "",
  applyType: "",
  auditTime: "",
  opinion: ""
});
const hasLoadError = ref(false);

const formatTimestamp = timestamp => {
  if (!timestamp) return "--";
  const date = new Date(timestamp);
  const Y = date.getFullYear();
  const M = String(date.getMonth() + 1).padStart(2, "0");
  const D = String(date.getDate()).padStart(2, "0");
  const h = String(date.getHours()).padStart(2, "0");
  const m = String(date.getMinutes()).padStart(2, "0");
  const s = String(date.getSeconds()).padStart(2, "0");
  return `${Y}-${M}-${D} ${h}:${m}:${s}`;
};

const fetchData = async () => {
  loading.value = true;
  hasLoadError.value = false;

  try {
    const reviewId = route.query.reviewId;

    // 并行调用两个接口
    const [snapshotResult, updateResult, expertApplyResult] = await Promise.all(
      [
        requestTo(findSnapshotByApplyId({ id: reviewId })),
        requestTo(findUpdateDetailByApplyId({ id: reviewId })),
        requestTo(findByExpertApplyId({ expertApplyId: reviewId }))
      ]
    );

    const [snapshotErr, snapshotRes] = snapshotResult;
    const [updateErr, updateRes] = updateResult;
    const [expertApplyErr, expertApplyRes] = expertApplyResult;

    // 处理原始数据
    if (!snapshotErr && snapshotRes) {
      originalData.value = snapshotRes;
    } else {
      console.error("获取原始数据失败:", snapshotErr);
      hasLoadError.value = true;
    }

    // 处理审核申请数据
    if (!expertApplyErr && expertApplyRes) {
      expertApplyInfo.value = expertApplyRes;
    } else {
      console.error("获取审核申请数据失败:", expertApplyErr);
      hasLoadError.value = true;
    }

    // 处理修改数据
    if (!updateErr && updateRes) {
      let parsedData = {};
      if (updateRes.baseData && typeof updateRes.baseData === "string") {
        try {
          // 解析baseData中的JSON字符串
          parsedData = JSON.parse(updateRes.baseData);
        } catch (e) {
          console.error("解析修改数据(baseData)失败:", e);
          hasLoadError.value = true;
        }
      }
      // TODO: file部分后端未完成，暂时不作处理
      changedData.value = parsedData;
    } else {
      console.error("获取修改数据失败:", updateErr);
      hasLoadError.value = true;
    }
  } catch (error) {
    console.error("数据加载异常:", error);
    hasLoadError.value = true;
  } finally {
    loading.value = false;
  }
};

// 页面加载时调用
fetchData();

// 计算需要展示的字段列表（只包含修改后的字段）
const changedFields = computed(() => {
  if (!changedData.value) return [];
  return Object.keys(changedData.value);
});

// 格式化显示字段
const formatValue = (field, value) => {
  if (field === "gender") {
    return genderMap[value] || "--";
  } else if (field === "educationLevel") {
    return educationLevelMap[value] || "--";
  } else if (field === "professionalTitle") {
    return professionalTitleMap[value] || "--";
  } else if (field === "teacherYear" && value) {
    return `${value}年`;
  }
  return value || "--";
};

// 获取字段中文名称
const getFieldLabel = field => {
  const labelMap = {
    name: "教师姓名",
    teacherNumber: "教师编号",
    age: "年龄",
    gender: "性别",
    phone: "联系方式",
    politicalStatus: "政治面貌",
    idNumber: "身份证号",
    healthCertificate: "健康证明",
    noCrimeCertificate: "无犯罪证明",
    mentalHealthCertificate: "心理健康筛查证明",
    educationLevel: "学历",
    major: "所学专业",
    employeeStatus: "在职",
    teacherYear: "从教时长",
    professionalTitle: "职称级别",
    teacherCertificateNumber: "教师资格证号码",
    teacherCertificate: "教师资格证",
    graduationCertificate: "毕业证",
    teachableCourse: "可任教课程",
    avatar: "个人头像",
    goodAt: "擅长课程",
    hobbies: "兴趣爱好",
    personBio: "个人风采"
  };

  return labelMap[field] || field;
};
</script>

<template>
  <el-skeleton v-if="loading" :loading="loading" animated :count="3">
    <template #template>
      <div style="padding: 20px">
        <el-skeleton-item variant="p" style="width: 100%; height: 40px" />
        <el-skeleton-item
          variant="p"
          style="width: 100%; height: 40px; margin-top: 16px"
        />
        <el-skeleton-item
          variant="p"
          style="width: 100%; height: 40px; margin-top: 16px"
        />
      </div>
    </template>
  </el-skeleton>

  <el-empty v-else-if="hasLoadError" description="数据加载失败，请刷新重试">
    <el-button type="primary" @click="fetchData">重新加载</el-button>
  </el-empty>

  <div v-else class="page-layout">
    <div class="audit-info">
      <div class="audit-info-row">
        <div v-if="expertApplyInfo.auditState" class="info-item">
          审核状态：<span class="status-text" :class="statusClass">{{
            auditStateMap[expertApplyInfo.auditState] || "--"
          }}</span>
        </div>
        <div v-if="expertApplyInfo.applyType" class="info-item">
          审核类型：{{ applyTypeMap[expertApplyInfo.applyType] || "--" }}
        </div>
        <div v-if="expertApplyInfo.auditTime" class="info-item">
          审核时间：{{ formatTimestamp(expertApplyInfo.auditTime) }}
        </div>
      </div>
      <div
        v-if="expertApplyInfo.auditState === 'REJECTED'"
        class="info-item audit-opinion"
      >
        审核意见：{{ expertApplyInfo.opinion || "--" }}
      </div>
    </div>
    <div class="comparison-panel">
      <div class="comparison-header">
        <div class="item-header">项目</div>
        <div class="before-header">修改前</div>
        <div class="after-header">修改后</div>
      </div>
      <el-scrollbar class="content-area">
        <div class="comparison-container">
          <el-descriptions border :column="1" class="comparison-content">
            <el-descriptions-item
              v-for="field in changedFields"
              :key="field"
              :label="getFieldLabel(field)"
            >
              <div class="comparison-row">
                <div class="before-value">
                  {{ formatValue(field, originalData[field]) }}
                </div>
                <div class="after-value">
                  {{ formatValue(field, changedData[field]) }}
                </div>
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-scrollbar>
      <!-- <div
        v-if="expertApplyInfo.auditState === 'PENDING_REVIEW'"
        class="footer"
      > -->
      <div class="footer">
        <div class="footer_right">
          <el-button type="danger" @click="openRejectDialog">驳回</el-button>
          <el-button type="primary" @click="openApproveDialog">
            审核通过
          </el-button>
        </div>
      </div>
    </div>

    <!-- 驳回弹窗 -->
    <el-dialog
      v-model="rejectDialogVisible"
      title="驳回"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-input
        v-model="rejectReason"
        type="textarea"
        :rows="2"
        maxlength="100"
        placeholder="请输入理由"
      />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="rejectDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitAudit(false)">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 审核通过弹窗 -->
    <el-dialog
      v-model="approveDialogVisible"
      title="审核通过"
      width="300px"
      :close-on-click-modal="false"
    >
      <span>确认审核通过吗？</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="approveDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitAudit(true)">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.page-layout {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 88vh;
}
.content-area {
  flex: 1;
  overflow: auto;
}
.audit-info {
  background: #fff;
  padding: 20px 23px;
  flex-shrink: 0;
}

.audit-info .info-item {
  font-size: 14px;
  line-height: 2;
  color: #606266;
}

.audit-info .status-text {
  color: #409eff;
}

.audit-info .status-text.rejected {
  color: rgb(245, 108, 108);
}

.audit-info .status-text.cancel {
  color: inherit;
}

.comparison-panel {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
  padding: 20px;
  background: #fff;
  overflow: hidden;
}
.comparison-container {
  border-top: none;
  border-left: 1px solid #dcdfe6;
  border-right: 1px solid #dcdfe6;
  border-bottom: 1px solid #dcdfe6;
}

.comparison-title {
  color: rgb(64, 158, 255);
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
}

.comparison-header {
  display: flex;
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  flex-shrink: 0;
}

.item-header {
  width: 180px;
  padding: 12px 10px;
  font-weight: bold;
  text-align: center;
  border-right: 1px solid #dcdfe6;
}

.before-header,
.after-header {
  flex: 1;
  padding: 12px 10px;
  font-weight: bold;
  text-align: center;
}

.before-header {
  border-right: 1px solid #dcdfe6;
}

.comparison-row {
  display: flex;
  width: 100%;
}

.before-value,
.after-value {
  flex: 1;
  padding: 8px;
}

.before-value {
  border-right: 1px solid #dcdfe6;
}

:deep(.el-descriptions__label) {
  width: 180px;
  text-align: center;
}

:deep(.el-descriptions__content) {
  padding: 0 !important;
}

:deep(.el-descriptions-item__content) {
  padding: 0 !important;
}

.footer {
  flex-shrink: 0;
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;
  font-size: 14px;
}
.footer .footer_right {
  display: flex;
  gap: 10px;
}
.audit-info-row {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
.audit-opinion {
  margin-top: 10px;
}

@media (max-width: 768px) {
  .audit-info-row {
    flex-direction: column;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
