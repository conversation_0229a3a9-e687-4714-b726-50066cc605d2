<script setup>
import { onMounted, ref } from "vue";
import { formatTime } from "@/utils/index";
// import { courseFindAll } from "@/api/brUser";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import dayjs from "dayjs";
import {
  priceSettingFindAll,
  findFree,
  bureauPriceSettingFindAll,
  bureauFindfree
} from "@/api/course";
import { to } from "@iceywu/utils";
import { useUserStoreHook } from "@/store/modules/user";
const router = useRouter();
const route = useRoute();
const freeValue = ref("");
const refundValue = ref("");
onMounted(() => {
  getTableList();
  getFreeData();
  // tableData.value = Array.from({ length: 20 }).fill({
  //   completed_at: "0",
  //   pending_review: 1,
  //   createdAt: 172627952334,
  //   name: "2024-09-14习题任务",
  //   termNumber: 1,
  //   courseTypeName: "手工",
  //   state: 1,
  //   organizationName: "2024-09-14习题任务",
  //   freeze: 1
  // });
});
// 表格数据
const tableData = ref([]);
const tableTitle = ref([]);
// 新的价格设置数据
const priceSettings = ref({
  coursePrice: "", // 课程费用 (CLASS_HOUR)
  materialPrice: "", // 材料费用 (MATERIAL)
  servicePrice: "", // 服务费用 (SERVICE)
  insurancePrice: "", // 保险费用 (INSURANCE)
  materialMandatory: true, // 材料费用是否必选
  serviceMandatory: true, // 服务费用是否必选
  insuranceMandatory: true, // 保险费用是否必选
  // 促销价相关字段
  promotionEnabled: false, // 是否开启促销价
  promotionPrice: "", // 促销价
  ruleFirstNEnabled: false, // 规则1：前 N 名
  ruleFirstNCount: "", // N 值
  ruleTimeEnabled: false, // 规则2：时间范围
  ruleTimeRange: [] // [开始时间, 结束时间]
});
const params = ref({
  page: 1,
  size: 10,
  sort: "createdAt,desc",
  totalElements: 0
});
// 获取规格列表信息
const getListLoading = ref(false);
const getTableList = async data => {
  if (getListLoading.value) {
    return;
  }
  getListLoading.value = true;
  let paramsData = {
    coursePeriodId: route.query.periodId
  };
  // console.log("🍧-----paramsData-----", paramsData);
  const api =
    useUserStoreHook().roleTarget === "局端管理员"
      ? bureauPriceSettingFindAll
      : priceSettingFindAll;
  const [err, result] = await requestTo(api(paramsData));
  // console.log("🎁-----result--22---", result);
  if (result) {
    // 处理新的价格设置数据结构
    result.forEach(item => {
      if (item.feeType === "CLASS_HOUR") {
        priceSettings.value.coursePrice =
          item.amount !== undefined && item.amount !== null ? item.amount : "";

        // 处理促销价数据回显
        if (item.promotion) {
          // 有促销价数据，设置是否开启促销价为是
          priceSettings.value.promotionEnabled = true;

          // 回显促销价
          priceSettings.value.promotionPrice =
            item.promotion.promotionPrice !== undefined &&
            item.promotion.promotionPrice !== null
              ? item.promotion.promotionPrice
              : "";

          // 回显促销规则：前N名用户
          if (item.promotion.totalQuota) {
            priceSettings.value.ruleFirstNEnabled = true;
            priceSettings.value.ruleFirstNCount = item.promotion.totalQuota;
          }

          // 回显促销规则：时间范围
          if (item.promotion.startTime && item.promotion.endTime) {
            priceSettings.value.ruleTimeEnabled = true;
            // 将时间戳转换为日期时间格式
            const startTime = dayjs(item.promotion.startTime).format(
              "YYYY-MM-DD HH:mm:ss"
            );
            const endTime = dayjs(item.promotion.endTime).format(
              "YYYY-MM-DD HH:mm:ss"
            );
            priceSettings.value.ruleTimeRange = [startTime, endTime];
          }
        } else {
          // 没有促销价数据，重置促销价相关字段
          priceSettings.value.promotionEnabled = false;
          priceSettings.value.promotionPrice = "";
          priceSettings.value.ruleFirstNEnabled = false;
          priceSettings.value.ruleFirstNCount = "";
          priceSettings.value.ruleTimeEnabled = false;
          priceSettings.value.ruleTimeRange = [];
        }
      } else if (item.feeType === "MATERIAL") {
        priceSettings.value.materialPrice =
          item.amount !== undefined && item.amount !== null ? item.amount : "";
        priceSettings.value.materialMandatory = item.mandatory;
      } else if (item.feeType === "SERVICE") {
        priceSettings.value.servicePrice =
          item.amount !== undefined && item.amount !== null ? item.amount : "";
        priceSettings.value.serviceMandatory = item.mandatory;
      } else if (item.feeType === "INSURANCE") {
        priceSettings.value.insurancePrice =
          item.amount !== undefined && item.amount !== null ? item.amount : "";
        priceSettings.value.insuranceMandatory = item.mandatory;
      }
    });
  } else {
    console.log("没有数据");
  }
  getListLoading.value = false;
};
// 获取费用说明及退款信息
const getLoading = ref(false);
const getFreeData = async data => {
  if (getLoading.value) {
    return;
  }
  getLoading.value = true;
  let paramsData = {
    coursePeriodId: route.query.periodId
  };
  const api =
    useUserStoreHook().roleTarget === "局端管理员" ? bureauFindfree : findFree;
  const [err, result] = await to(api(paramsData));
  if (result?.code === 200) {
    freeValue.value = result?.data?.feeDescription;
    refundValue.value = result?.data?.refundPolicy;
  } else {
    // ElMessage.error('获取失败');
    console.log("无数据");
  }
  getLoading.value = false;
};

// 暴露给父组件的方法
const refreshData = () => {
  getTableList();
  getFreeData();
};

// 暴露方法给父组件
defineExpose({
  refreshData
});
</script>

<template>
  <div class="containers">
    <!-- 旧的规格表格显示已注释 -->
    <!-- <div class="con_table">
      <div v-if="tableData?.length" class="table-content">
        <pure-table
          ref="tableRef"
          row-key="id"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          table-layout="auto"
          :loading="getListLoading"
          :size="size"
          :data="tableData"
          :columns="tableTitle"
          :style="{ height: '100%' }"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @drag-sort-end="handleSortEnd"
        />
      </div>
      <el-skeleton v-else-if="getListLoading" :rows="5" animated />
      <el-empty
        v-else
        description="暂无数据"
        class="w100%"
        :style="{ flex: 1 }"
      />
    </div> -->

    <!-- 新的价格设置展示 -->
    <div class="price-setting-section">
      <div class="price-description-list">
        <!-- 课程费用 -->
        <div class="description-item">
          <div class="label">课时费用：</div>
          <div class="value">
            {{
              priceSettings.coursePrice !== "" &&
              priceSettings.coursePrice !== null &&
              priceSettings.coursePrice !== undefined
                ? `${priceSettings.coursePrice} 元`
                : "暂无数据"
            }}
          </div>
        </div>

        <!-- 促销价相关展示（仅在开启促销价时显示） -->
        <template v-if="priceSettings.promotionEnabled">
          <!-- 促销价 -->
          <div class="description-item">
            <div class="label">促销价：</div>
            <div class="value">
              {{
                priceSettings.promotionPrice !== "" &&
                priceSettings.promotionPrice !== null &&
                priceSettings.promotionPrice !== undefined
                  ? `${priceSettings.promotionPrice} 元`
                  : "暂无数据"
              }}
            </div>
          </div>

          <!-- 促销规则 -->
          <div class="description-item promotion-rules">
            <div class="label">促销规则：</div>
            <div class="value">
              <div v-if="priceSettings.ruleFirstNEnabled" class="rule-text">
                前{{ priceSettings.ruleFirstNCount }}名用户可以享受促销价
              </div>
              <div v-if="priceSettings.ruleTimeEnabled" class="rule-text">
                {{ priceSettings.ruleTimeRange[0] }} 至
                {{ priceSettings.ruleTimeRange[1] }} 前报名用户可以享受促销价
              </div>
              <div
                v-if="
                  !priceSettings.ruleFirstNEnabled &&
                  !priceSettings.ruleTimeEnabled
                "
                class="rule-text"
              >
                暂无促销规则
              </div>
            </div>
          </div>
        </template>

        <!-- 材料费用 -->
        <div class="description-item">
          <div class="label">材料费用：</div>
          <div class="value">
            {{
              priceSettings.materialPrice !== "" &&
              priceSettings.materialPrice !== null &&
              priceSettings.materialPrice !== undefined
                ? `${priceSettings.materialPrice} 元${priceSettings.materialMandatory ? "（家长必选）" : "（家长可选）"}`
                : "暂无数据"
            }}
          </div>
        </div>

        <!-- 服务费用 -->
        <div class="description-item">
          <div class="label">服务费用：</div>
          <div class="value">
            {{
              priceSettings.servicePrice !== "" &&
              priceSettings.servicePrice !== null &&
              priceSettings.servicePrice !== undefined
                ? `${priceSettings.servicePrice} 元`
                : "暂无数据"
            }}
          </div>
        </div>

        <!-- 保险费用 -->
        <div class="description-item">
          <div class="label">保险费用：</div>
          <div class="value">
            {{
              priceSettings.insurancePrice !== "" &&
              priceSettings.insurancePrice !== null &&
              priceSettings.insurancePrice !== undefined
                ? `${priceSettings.insurancePrice} 元`
                : "暂无数据"
            }}
          </div>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="free">
        <div class="title">费用说明（包含、不包含）</div>
        <!-- <div class="text">
          <el-input
            v-model="freeValue"
            :rows="6"
            type="textarea"
            resize="none"
            disabled
            :placeholder="freeValue ? freeValue : '暂无数据'"
          />
        </div> -->
        <div class="text-box">
          {{ freeValue || "暂无数据" }}
        </div>
      </div>
      <div class="refund">
        <div class="title">退款政策</div>
        <!-- <div class="text">
          <el-input
            v-model="refundValue"
            :rows="6"
            type="textarea"
            resize="none"
            disabled
            :placeholder="refundValue ? refundValue : '暂无数据'"
          />
        </div> -->
        <div class="text-box">
          {{ refundValue || "暂无数据" }}
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.containers {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  .text-box {
    border: 1px solid #e5e5e5;
    border-radius: 3px;
    width: 100%;
    height: 200px;
    padding: 5px 10px;
    font-size: 14px;
    overflow-y: auto;
  }
  .con_table {
    // 旧的表格样式已注释
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0;
    .table-content {
      width: 100%;
      display: flex;
      flex-direction: column;
      flex: 1;
      min-height: 0;
      :deep(.el-table) {
        flex: 1;
        height: 100%;
        width: 100%;
      }
      :deep(.pure-table) {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100%;
      }
    }
  }

  // 新的价格设置样式
  .price-setting-section {
    margin-bottom: 20px;
    background: #fff;
    border-radius: 8px;

    .title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 20px;
    }

    .price-description-list {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px 40px;

      .description-item {
        display: flex;
        align-items: center;
        padding: 12px 0;

        .label {
          font-size: 14px;
          color: #606266;
          font-weight: 500;
          min-width: 80px;
          flex-shrink: 0;
        }

        .value {
          font-size: 14px;
          color: #303133;
          flex: 1;
        }

        &.promotion-rules {
          align-items: flex-start;

          .value {
            display: flex;
            flex-direction: column;

            .rule-text {
              margin-bottom: 16px;
              font-size: 14px;
              color: #303133;

              &:last-child {
                margin-bottom: 0;
              }
            }
          }
        }
      }
    }
  }

  .content {
    box-sizing: border-box;
    display: flex;
    padding: 10px 0 10px 0;

    .free {
      width: 50%;
      margin-right: 40px;
    }

    .title {
      margin-bottom: 18px;
      font-size: 14px;
      color: rgb(16 16 16 / 100%);
    }

    .refund {
      width: 50%;
    }

    .text {
      width: 100%;
    }
  }
}
</style>
