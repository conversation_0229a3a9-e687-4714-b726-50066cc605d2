<script setup>
import { ref, reactive, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import RichEditor from "@/components/Base/RichEditor.vue";
import { uploadFile, isOverSizeLimit } from "@/utils/upload/upload.js";
import { Plus } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox, genFileId, ElUpload } from "element-plus";
import { discoverFindAll, discoverDelete, discoverAdd } from "@/api/findapi.js";
const router = useRouter();
const route = useRoute();

const buttonLoading = ref(false); // 按钮加载状态
const ruleFormRef = ref();
const form = ref({
  fileVOS: [],
  fileList: [
    // {
    //   name: "food.jpeg",
    //   url: "https://fuss10.elemecdn.com/3/63/4e7f3a15429bfda99bce42a18cdd1jpeg.jpeg?imageMogr2/thumbnail/360x360/format/webp/quality/100"
    // }
  ],
  title: "",
  content: ""
});

const formData = ref([
  {
    label: "图片",
    type: "elUpload",
    prop: "fileList",
    show: true,
    check: true,
    placeholder: "请输入姓名",
    width: "400px"
  },
  {
    label: "标题",
    type: "input",
    prop: "title",
    show: true,
    check: true,
    placeholder: "请输入标题",
    width: "450px"
  },
  {
    label: "内容",
    type: "RichEditor",
    prop: "content",
    show: true,
    check: true,
    isView: true,
    placeholder: "请输入内容",
    width: "400px"
  }
]);

// 自定义富文本校验方法
const validateIntroduction = (rule, value, callback) => {
  // console.log("🍭-----rule, value, callback-----", value);
  // 移除HTML标签并检查内容是否为空
  const cleanValue = value.replace(/<[^>]*>/g, "").trim();
  if (cleanValue === "") {
    callback(new Error("内容不能为空"));
  } else {
    callback();
  }
};
const rules = reactive({
  fileList: [{ required: true, message: "请上传图片", trigger: "blur" }],
  title: [{ required: true, message: "请输入标题", trigger: "blur" }],
  content: [
    { required: true, validator: validateIntroduction, trigger: "blur" }
  ]
});

const removeEmptyValues = obj => {
  return Object.fromEntries(
    Object.entries(obj).filter(
      ([key, value]) =>
        value !== "" && !(Array.isArray(value) && value.length === 0)
    )
  );
};
function setFilesFn(val) {
  let arr = [];
  for (let index = 0; index < val.length; index++) {
    const element = val[index].fileIdentifier;
    arr.push({
      fileType: "COVER",
      fileIdentifier: element,
      sortOrder: index + 1
    });
  }
  return arr;
}

// NONE("无"),
// COVER("封面"),
// BUSINESS_LICENSE("营业执照"),
// PHOTO("照片"),
// QUALIFICATION_DOCUMENT("资质文件"),
// ILLUSTRATION("配图");

// 图片上传
const fileUpload = async (file, row) => {
  if (form.value.fileList.length > 8) {
    return ElMessage({ type: "warning", message: "上传图片为最多9张" });
  }
  // let isSize = isOverSizeLimit(file, 10);
  // if (isSize.valid) {
  try {
    // 检查文件大小，10MB = 10 * 1024 * 1024 字节
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      ElMessage.error("上传图片大小不能超过10MB！");
      return false; // 阻止上传
    }
    const { code, data } = await uploadFile(file, () => {}, ["image"]);
    if (code === 200) {
      form.value.fileList.push({
        url: data.url,
        name: data.fileName,
        fileIdentifier: data?.fileIdentifier
      });

      if (ruleFormRef.value) {
        ruleFormRef.value.validateField("fileList", valid => {
          if (!valid) {
            console.log("验证失败");
          } else {
            console.log("验证成功");
          }
        });
      }
      return false;
    }
  } catch (error) {
    form.value.fileList?.pop();
    ElMessage({
      type: "error",
      message: error.message
    });
  }
  // } else {
  //   form.value.fileList?.pop();
  //   console.log('🎁-----form.value.fileList--新增---', form.value.fileList);
  //   ElMessage.error(isSize.message);
  // }
};

// 提交表单
const newlyAdd = () => {
  ruleFormRef.value.validate(async valid => {
    if (valid) {
      console.log("表单数据:", form.value);
      buttonLoading.value = true;
      const operateLog = {
        operateLogType: "PLATFORM_SETTINGS",
        operateType: "新增了标题为“" + form.value.title + "”"
      };
      let fileList = setFilesFn(form.value.fileList);
      const paramsArg = {
        title: form.value.title || "",
        content: form.value.content || "",
        files: fileList || ""
      };
      let api = removeEmptyValues(paramsArg);
      const res = await discoverAdd(api, operateLog);
      console.log("🍭-----api, operateLog-----", api, operateLog);
      if (res.code === 200) {
        ElMessage({
          type: "success",
          message: "新增成功"
        });
        router.go(-1);
        buttonLoading.value = false;
      } else {
        ElMessage({
          type: "error",
          message: "新增失败"
        });
        buttonLoading.value = false;
      }
    } else {
      console.log("表单校验失败", valid);
    }
  });
};
</script>

<template>
  <div class="commonapp">
    <!-- <div class="formbox">新增</div> -->
    <el-scrollbar class="scrollbar">
      <div class="table_content">
        <el-form ref="ruleFormRef" :rules="rules" :model="form">
          <el-descriptions title="" :column="1" border :label-width="'15%'">
            <el-descriptions-item
              v-for="(item, index) in formData"
              :key="index"
              label-align="center"
              label-class-name="my-label"
            >
              <template #label>
                <span v-if="item.check" class="star">*</span> {{ item.label }}
              </template>
              <el-form-item
                :prop="item.prop"
                :inline-message="item.check"
                style="margin-bottom: 0"
                :show-message="true"
                error-placement="right"
              >
                <!-- input输入 -->
                <template v-if="item.type === 'input'">
                  <el-input
                    v-model.trim="form[item.prop]"
                    :placeholder="item.placeholder"
                    :style="{ width: item.width }"
                    show-word-limit
                    maxlength="50"
                  />
                </template>
                <!-- 富文本 -->
                <template v-if="item.type === 'elUpload'">
                  <div>
                    <div>
                      <el-upload
                        v-model:file-list="form[item.prop]"
                        :limit="9"
                        accept="image/*"
                        list-type="picture-card"
                        :http-request="() => {}"
                        :before-upload="
                          uploadFile => fileUpload(uploadFile, form)
                        "
                      >
                        <el-icon>
                          <Plus />
                        </el-icon>
                      </el-upload>
                    </div>
                    <div><span class="star">*</span>图片上传上限为9张</div>
                    <div class="upload_text">
                      支持上传jpg、jpeg、png、gif、bmp、webp格式图片，图片最佳尺寸：400*400px，单张图片大小不超过10MB
                    </div>
                  </div>
                </template>
                <!-- 富文本 -->
                <template v-if="item.type === 'RichEditor'">
                  <RichEditor
                    v-model="form[item.prop]"
                    height="400px"
                    :readOnly="false"
                    :isShield="true"
                    :excludeKeys="['headerSelect', 'fontSize', 'lineHeight']"
                    :lineHeight="2"
                  />
                </template>
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>
        </el-form>
      </div>
    </el-scrollbar>

    <div class="footer">
      <el-button @click="router.go(-1)">取消</el-button>
      <el-button :loading="buttonLoading" type="primary" @click="newlyAdd">
        保存
      </el-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.commonapp {
  // margin: 20px;
  //   display: flex;
  //   align-items: flex-start;
  //   height: 72vh;
  //   max-width: 990px;
  background-color: #fff;
  padding: 20px;

  .scrollbar {
    // padding-top: 20px;
    margin-bottom: 20px;
    height: calc(100vh - 206px);
    background-color: #fff;
  }

  .formbox {
    margin-bottom: 20px;
  }
  .footer {
    display: flex;
    justify-content: end;
  }
  // .table_content {
  // margin-bottom: 20px;
  // }
}

.star {
  margin-right: 3px;
  color: red;
}
:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  background: #e1f5ff;
}
:deep(.el-form-item__content) {
  .upload_text {
    font-size: 12px;
    position: relative;
    top: 5px;
    color: #8c939d;
  }
}
</style>
