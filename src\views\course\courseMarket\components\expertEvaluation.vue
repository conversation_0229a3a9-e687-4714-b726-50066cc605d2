<script lang="js" setup>
import { useTable, PlusTable } from "plus-pro-components";
import { computed } from "vue";

const TestServe = {
  getList: async () => {
    const data = Array.from({ length: 5 }).map((item, index) => {
      return {
        id: index,
        name: index + "name",
        status: String(index % 3),
        tag:
          index === 1
            ? "success"
            : index === 2
              ? "warning"
              : index === 3
                ? "info"
                : "danger",
        time: new Date()
      };
    });
    return {
      data: data,
      total: data.length
    };
  }
};

const { tableData, total, pageInfo, buttons } = useTable();

pageInfo.value.pageSize = 5;

const tableConfig = [
  {
    label: "评价人",
    prop: "name"
  },
  {
    label: "评价时间",
    prop: "time",
    valueType: "date-picker"
  },
  {
    label: "打分",
    width: 200,
    prop: "rate",
    valueType: "rate",
    editable: true,
    fieldProps: {
      disabled: false,
      allowHalf: true
    }
  },
  {
    label: "评价内容",
    prop: "text",
    valueType: "text",
    width: 200,
    formatter: value => value || "暂无评价"
  }
];

buttons.value = [
  {
    // 修改
    text: "关联课程",
    // code: "edit",
    props: {
      type: "primary"
    },
    show: computed(() => true),
    onClick(params) {
      if (params?.formRefs) {
        // isEdit v0.1.8 新增
        const isEdit = params.formRefs[0].isEdit.value;
        isEdit
          ? params.formRefs[0]?.stopCellEdit()
          : params.formRefs[0]?.startCellEdit();
      }
    }
  }
];

const getList = async () => {
  try {
    const { data, total: dataTotal } = await TestServe.getList();
    tableData.value = data;
    total.value = dataTotal;
  } catch (error) {}
};
const handlePaginationChange = _pageInfo => {
  pageInfo.value = _pageInfo;

  getList();
};

getList();
</script>

<template>
  <div class="expert-evaluation">
    <PlusTable
      :columns="tableConfig"
      :table-data="tableData"
      :pagination="{
        total,
        modelValue: pageInfo,
        pageSizeList: [10, 20, 50]
      }"
      :action-bar="{ buttons, width: '100px' }"
      :title-bar="false"
      pagination-align="right"
      @pagination-change="handlePaginationChange"
    />
  </div>
</template>

<style scoped>
.expert-evaluation {
  height: calc(100vh - 190px);
  width: 100%;
}
/* 强制分页右对齐并添加顶部间距 */
:deep(.el-pagination) {
  display: flex;
  justify-content: flex-end;
  padding-top: 15px;
}
</style>
