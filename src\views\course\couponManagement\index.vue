<script setup>
import { useCouponManagement } from "./components/couponManagementHook.jsx";
import HandlingAudits from "./components/handlingAudits.vue";

defineOptions({
  name: "AppealManagementIndex"
});

// 使用申诉管理 hook
const {
  // 响应式数据
  activeTab,
  tabOptions,
  appealObj,
  searchForm,
  tableData,
  loading,
  pagination,
  selectedRows,
  isSelectionMode,
  searchColumns,
  columns,
  // actionBar,
  appealDetailVisible,
  currentAppealDetail,
  showAuditDialog,

  // 方法
  handleTabClick,
  loadTableData,
  handleSearch,
  handleReset,
  handleProcess,
  handleEnableBatchMode,
  handleCancelBatchMode,
  handleBatchProcess,
  handleSelectionChange,
  handlePageChange,
  handleSizeChange,
  handleViewAppealDetail,
  handleCloseAppealDetail,
  handleAuditSubmit
} = useAppealManagement();
</script>

<template>
  <div class="containers">
    <div class="main">
      <!-- 申诉管理tab栏 -->
      <div class="appeal-tabs">
        <el-tabs
          v-model="activeTab"
          class="demo-tabs"
          @tab-click="handleTabClick"
        >
          <el-tab-pane
            v-for="(item, index) in tabOptions"
            :key="index"
            :label="item.name"
            :name="index"
          />
        </el-tabs>
      </div>

      <!-- 搜索区域 -->
      <div class="search-section">
        <PlusSearch
          v-model="searchForm"
          :columns="searchColumns"
          :show-number="1"
          :has-unfold="false"
          @search="handleSearch"
          @reset="handleReset"
        />
      </div>

      <!-- 批量操作按钮 -->
      <div v-if="appealObj.showBatchAudit" class="batch-actions">
        <el-button
          type="primary"
          :disabled="isSelectionMode"
          @click="handleEnableBatchMode"
        >
          批量处理
        </el-button>
      </div>

      <!-- 申诉列表表格 -->
      <div class="appeal-content">
        <PlusTable
          :title-bar="false"
          :columns="columns"
          :table-data="tableData"
          :loadingStatus="loading"
          :action-bar="false"
          :is-selection="isSelectionMode"
          :pagination="pagination"
          :adaptive="true"
          row-key="id"
          :border="false"
          @selection-change="handleSelectionChange"
          @page-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>

      <!-- 多选模式下的操作按钮 -->
      <div v-if="isSelectionMode" class="selection-actions">
        <el-button
          type="primary"
          :disabled="selectedRows.length === 0"
          @click="handleBatchProcess"
        >
          处理
        </el-button>
        <el-button @click="handleCancelBatchMode"> 取消 </el-button>
      </div>
    </div>

    <!-- 申诉详情弹窗 -->
    <el-dialog
      v-model="appealDetailVisible"
      title="申诉详情"
      width="800px"
      @close="handleCloseAppealDetail"
    >
      <div v-if="currentAppealDetail" class="appeal-detail-content">
        <div style="margin-bottom: 18px">
          <h4>申诉描述：</h4>
          <div style="margin-bottom: 10px; color: #606266">
            {{ currentAppealDetail.content }}
          </div>
        </div>
        <div
          v-if="
            currentAppealDetail.images && currentAppealDetail.images.length > 0
          "
          style="margin-bottom: 18px"
        >
          <h4>相关图片：</h4>
          <div style="display: flex; gap: 16px">
            <el-image
              v-for="(img, idx) in currentAppealDetail.images"
              :key="idx"
              :src="img"
              style="
                width: 120px;
                height: 90px;
                border-radius: 8px;
                object-fit: cover;
              "
              fit="cover"
              :preview-src-list="currentAppealDetail.images"
              :initial-index="idx"
            />
          </div>
        </div>
        <div
          v-if="
            currentAppealDetail.videos && currentAppealDetail.videos.length > 0
          "
        >
          <h4>相关视频：</h4>
          <div style="display: flex; gap: 16px; flex-wrap: wrap">
            <video
              v-for="(video, idx) in currentAppealDetail.videos"
              :key="idx"
              :src="video"
              :poster="video.poster"
              controls
              style="
                width: 240px;
                height: 160px;
                border-radius: 8px;
                background: #000;
              "
            />
          </div>
        </div>
      </div>
    </el-dialog>
    <!-- 处理弹窗 -->
    <HandlingAudits v-model="showAuditDialog" @submit="handleAuditSubmit" />
  </div>
</template>

<style lang="scss" scoped>
.containers {
  display: flex;
  flex-direction: column;
  height: 88vh;
  overflow: hidden;
  box-sizing: border-box;

  .main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: #fff;
    padding: 20px;
    box-sizing: border-box;
  }
}

// 申诉管理tab样式
.appeal-tabs {
  margin-bottom: 20px;

  :deep(.el-tabs) {
    --el-tabs-header-height: 40px;
  }

  // 保持蓝色下划线效果
  :deep(.el-tabs__active-bar) {
    background-color: #409eff;
    height: 2px;
  }

  :deep(.el-tabs__nav-wrap::after) {
    background-color: #e4e7ed;
    height: 1px;
  }

  :deep(.el-tabs__item) {
    font-size: 14px;
    font-weight: 500;
    color: #606266;

    &.is-active {
      color: #409eff;
      font-weight: 600;
    }

    &:hover {
      color: #409eff;
    }
  }
}

// 搜索区域
.search-section {
  margin-bottom: 16px;

  :deep(.el-form-item__content .el-icon) {
    display: none;
    width: 0;
    height: 0;
  }
  :deep(.el-form-item__content) {
    .el-button {
      span {
        margin-left: 0px !important;
      }
    }
  }
}

// 批量操作按钮区域
.batch-actions {
  margin-bottom: 16px;

  .el-button {
    margin-right: 12px;
  }
}

// 申诉内容区域
.appeal-content {
  flex: 1;
  overflow: hidden;

  :deep(.plus-table) {
    height: 100%;

    .el-table {
      height: calc(100% - 60px) !important;
    }
  }
}

// 申诉详情弹窗样式
.appeal-detail-content {
  .appeal-text {
    margin-bottom: 20px;

    h4 {
      margin: 0 0 10px 0;
      color: #303133;
      font-size: 14px;
      font-weight: 600;
    }

    p {
      margin: 0;
      line-height: 1.6;
      color: #606266;
      word-break: break-word;
    }
  }

  .appeal-images {
    margin-bottom: 20px;

    h4 {
      margin: 0 0 10px 0;
      color: #303133;
      font-size: 14px;
      font-weight: 600;
    }

    .image-gallery {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;

      .appeal-image {
        width: 120px;
        height: 120px;
        border-radius: 6px;
        cursor: pointer;
        border: 1px solid #dcdfe6;

        &:hover {
          border-color: #409eff;
        }
      }
    }
  }

  .appeal-videos {
    h4 {
      margin: 0 0 10px 0;
      color: #303133;
      font-size: 14px;
      font-weight: 600;
    }

    .video-gallery {
      .video-item {
        margin-bottom: 15px;

        &:last-child {
          margin-bottom: 0;
        }

        h5 {
          margin: 0 0 8px 0;
          color: #606266;
          font-size: 13px;
          font-weight: 500;
        }

        .appeal-video {
          width: 100%;
          max-width: 500px;
          height: auto;
          border-radius: 6px;
          border: 1px solid #dcdfe6;
        }
      }
    }
  }
}

// 表格中申诉内容摘要样式
:deep(.appeal-content-summary) {
  .appeal-text-summary {
    margin-bottom: 6px;
    line-height: 1.4;
    color: #606266;
    word-break: break-word;
  }

  .appeal-media-tags {
    margin-bottom: 6px;

    .el-tag {
      margin-right: 4px;
      margin-bottom: 2px;
    }
  }
}

/* 表头颜色为白色 */
:deep(.el-table__header th) {
  background-color: #fff !important;
  color: rgb(85, 85, 85);
}
/*去掉tabs底部的下划线*/
::v-deep .el-tabs__nav-wrap::after {
  position: static !important;
}
</style>
