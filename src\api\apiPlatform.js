import { http } from "@/utils/http";

/** 机构分页查询 */
export const organizationFindAll = params => {
  return http.request(
    "get",
    "/platform/organization/findAll",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};

/** 机构不分页查询 */
export const organizationFindByNoPage = params => {
  return http.request(
    "get",
    "/platform/organization/findByNoPage",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};

/** 课程分页查询 */
export const courseFindAll = params => {
  return http.request(
    "get",
    "/platform/course/findAll",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};

// 根据父Id查询所有下级节点
export const findModelChildrenByParentId = params => {
  return http.request(
    "get",
    "/common/dict/findModelChildrenByParentId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};

/* 轮播图 api */
// 分页查询
export const bannerFindAll = params => {
  return http.request(
    "get",
    "/platform/banner/findAll",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 新增
export const bannerSave = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/banner/save",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "PLATFORM_SETTINGS",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
// 根据ID删除
export const bannerDelete = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/banner/delete",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "PLATFORM_SETTINGS",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
// 修改
export const bannerUpdate = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/banner/update",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "PLATFORM_SETTINGS",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
// 上移或下移
export const bannerMove = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/banner/move",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "PLATFORM_SETTINGS",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};

/* 推荐课程 api */
// 精品课程-查询
export const premiumCourseFindAll = params => {
  return http.request(
    "get",
    "/platform/premiumCourse/findAll",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 精品课程-修改
export const premiumCourseUpdate = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/premiumCourse/update",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "PLATFORM_SETTINGS",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
// 精品课程-新增
export const premiumCourseSave = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/premiumCourse/save",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "PLATFORM_SETTINGS",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
// 精品课程-上移或下移
export const premiumCourseMove = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/premiumCourse/move",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "PLATFORM_SETTINGS",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
// 精品课程-根据ID删除
export const premiumCourseseDelete = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/premiumCourse/delete",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "PLATFORM_SETTINGS",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};

// 热门课程-查询
export const popularCourseFindAll = params => {
  return http.request(
    "get",
    "/platform/popularCourse/findAll",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 热门课程-修改
export const popularCourseUpdate = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/popularCourse/update",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "PLATFORM_SETTINGS",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
// 热门课程-新增
export const popularCourseSave = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/popularCourse/save",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "PLATFORM_SETTINGS",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
// 热门课程-上移或下移
export const popularCourseMove = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/popularCourse/move",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "PLATFORM_SETTINGS",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
// 热门课程-根据ID删除
export const popularCourseDelete = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/popularCourse/delete",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "PLATFORM_SETTINGS",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};

/* 平台信息 api */
// 查询
export const platformInfoFind = params => {
  return http.request(
    "get",
    "/platform/platformInfo/find",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 新增或更新
export const platformInfoSaveOrUpdate = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/platformInfo/saveOrUpdate",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "PLATFORM_SETTINGS",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};

// 修改
export const platformInfoUpdate = data => {
  return http.request(
    "post",
    "/platform/platformInfo/update",
    { data },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 新增
export const platformInfoSave = data => {
  return http.request(
    "post",
    "/platform/platformInfo/save",
    { data },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
