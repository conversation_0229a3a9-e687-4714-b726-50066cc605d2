<script setup>
import { onMounted, ref, onBeforeMount, computed, nextTick } from "vue";
import { ArrowLeft, ArrowRight } from "@element-plus/icons-vue";
import { ImageThumbnail } from "@/utils/imageProxy";
const props = defineProps({
  width: {
    type: Number,
    default: 350
  },
  height: {
    type: Number,
    default: 250
  },

  itemCount: {
    type: Number,
    default: 50
  },
  imgList: {
    type: Array,
    default: () => []
  },
  srcList: {
    type: Array,
    default: () => []
  }
});

const monitorList = ref([]);

function getPos(index) {
  return {
    left: index * props.width - page.value * pageOneW.value + "px"
  };
}

const rect = ref(null);
const w = computed(() => {
  return props.width + "px";
});
const h = computed(() => {
  return props.height + "px";
});

const items = ref([]);
const page = ref(0);

function pageChange(dir) {
  if (dir < 0) {
    if (page.value === 0) return;
  } else {
    if (page.value + 1 >= maxPage.value) return;
  }
  page.value += dir;
  console.log("🦄page.value------------------------------>", page.value);
}
function getItems() {
  let list = [];
  for (let i = 0; i < props.itemCount; i++) {
    list[i] = {
      pos: i * props.width
    };
  }
  items.value = list;
  console.log("🦄items.value------------------------------>", items.value);
}

const itemAllW = computed(() => {
  return props.itemCount * props.width;
});
const maxPage = computed(() => {
  return Math.ceil(props.itemCount / pageCount.value);
});

const pageOneW = computed(() => {
  return Math.floor(rectW.value / props.width) * props.width;
});
const pageCount = computed(() => {
  return Math.floor(rectW.value / props.width);
});
const rectW = ref(0);
function onResize() {
  page.value = 0;
  nextTick(() => {
    rectW.value = rect.value.clientWidth;
  });
}
const srcId = ref(0);
const showPreview = ref(false);
// 图片预览
const handleClick = id => {
  showPreview.value = true;
  srcId.value = id;
};
onMounted(() => {
  getItems();

  nextTick(() => {
    rectW.value = rect.value.clientWidth;
  });
  window.removeEventListener("resize", onResize);
  window.addEventListener("resize", onResize);
});
</script>

<template>
  <div class="scrollBar">
    <div v-if="maxPage > 1" class="btn" @click="pageChange(-1)">
      <el-icon><ArrowLeft /></el-icon>
    </div>
    <div ref="rect" :class="maxPage > 1 ? 'container' : 'container1'">
      <div
        v-for="(item, index) in imgList"
        :key="index"
        class="item"
        :style="getPos(index)"
      >
        <img
          :src="ImageThumbnail(item.url, '200x')"
          class="h-full"
          @click="handleClick(index)"
        >
      </div>
      <el-image-viewer
        v-if="showPreview"
        :url-list="srcList"
        show-progress
        :initial-index="srcId"
        :hide-on-click-modal="true"
        @close="showPreview = false"
      />
    </div>
    <div v-if="maxPage > 1" class="btn" @click="pageChange(1)">
      <el-icon><ArrowRight /></el-icon>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.scrollBar {
  // box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  // padding: 18px;
  .btn {
    // border: 1px solid #b3d8ff;
    width: 40px;
    height: 40px;
    // line-height: 100px;
    // text-align: center;
    cursor: pointer;
    background-color: #e2ecf8;
    // icon
    font-size: 20px;
    color: #409eff;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 10px;
    // margin: 0 auto;

    &:hover {
      background-color: #409eff;
      color: white;
    }
  }
  .arrow {
    width: 30px;
    background-color: rgb(0, 0, 0);
    height: 30px;
  }
  .container {
    width: 100%;
    position: relative;
    height: v-bind(h);
    overflow: hidden;
    flex: 1;
    .item {
      box-sizing: border-box;
      // border: 1px solid red;
      width: v-bind(w);
      height: v-bind(h);
      // background-color: aqua;
      position: absolute;
      transition-property: left;
      transition-duration: 0.5s;
      padding: 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }

  .container1 {
    width: 100%;
    position: relative;
    height: v-bind(h);
    overflow: hidden;
    display: flex;
    justify-content: space-around;
    .item {
      box-sizing: border-box;
      // border: 1px solid red;
      width: v-bind(w);
      height: v-bind(h);
      // background-color: aqua;
      transition-property: left;
      transition-duration: 0.5s;
      padding: 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
  }
}
</style>
