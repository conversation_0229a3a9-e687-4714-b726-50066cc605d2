<script setup>
import { ref, reactive, onMounted, computed } from "vue";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import {
  commentFindAll,
  commentAdd,
  commentdeleteId,
  commentFindId,
  commentEdit
} from "@/api/commentLibrary.js";
import domain from "@/utils/http/base";

defineOptions({
  name: "ExpertDatabasename"
});

const router = useRouter();
const route = useRoute();

const dataList = ref([]);
const loadingTable = ref(false);
const tableRef = ref(null);
const pagination = ref({
  currentPage: 1,
  pageSize: 15,
  total: 0
});
const from = ref({
  name: "",
  time: null
});

const paramsArg = {
  page: 0, // 改为从0开始
  size: 15,
  sort: "createdAt,desc"
};

// 计算序号列表
const getIndexList = computed(() => {
  const currentPage = pagination.value.currentPage;
  const pageSize = pagination.value.pageSize;
  return dataList.value.map((item, index) => {
    return {
      ...item,
      displayIndex: (currentPage - 1) * pageSize + index + 1
    };
  });
});

const columns = [
  {
    label: "序号",
    prop: "displayIndex",
    width: 60,
    minWidth: 90
  },
  {
    label: "评语内容",
    prop: "content",
    minWidth: 200,
    formatter: ({ content }) => {
      return content || "--";
    }
  },
  {
    label: "操作",
    fixed: "right",
    width: 120,
    slot: "operation"
  }
];

// 列表 api
const onSearch = async (params = paramsArg) => {
  loadingTable.value = true;
  try {
    const [err, res] = await requestTo(commentFindAll(params));
    // console.log("🍧-----列表数据-----", err, res);

    if (!err && res && res.content) {
      dataList.value = res.content;
      pagination.value.total = res.totalElements || 0;
      // 更新当前页码（从后端返回的数据计算）
      if (res.number !== undefined) {
        pagination.value.currentPage = res.number + 1;
      }
    } else {
      dataList.value = [];
      pagination.value.total = 0;
      if (err) {
        console.error("获取列表失败:", err);
      }
    }
  } catch (error) {
    console.error("获取列表失败:", error);
    dataList.value = [];
    pagination.value.total = 0;
  } finally {
    loadingTable.value = false;
  }
};

// 每页多少条
async function handleSizeChange(val) {
  pagination.value.pageSize = val;
  pagination.value.currentPage = 1; // 重置到第一页

  const params = {
    ...paramsArg,
    page: 0, // 从0开始
    size: val
  };
  onSearch(params);
}

// 前往页数
async function handleCurrentChange(val) {
  pagination.value.currentPage = val;

  const params = {
    ...paramsArg,
    page: val - 1, // 转换为从0开始的页码
    size: pagination.value.pageSize
  };
  onSearch(params);
}

// 校验
const validateContent = (rule, value, callback) => {
  if (!value) {
    callback(new Error("评语内容不能为空"));
  } else if (value.length > 200) {
    callback(new Error("评语内容不能超过200字"));
  } else {
    callback();
  }
};

// 弹框
const showDialog = ref(false);
const content = ref("");
const titleDialog = ref("");
const currentRow = ref(null); // 添加当前编辑行数据
const form = ref({
  content: "",
  displayIndex: "" // 添加序号字段
});
const rules = ref({
  content: [{ required: true, validator: validateContent, trigger: "blur" }],
  displayIndex: [{ required: true, message: "序号不能为空", trigger: "blur" }]
});
const formRef = ref(null);

// 删除操作
const isDeleteApi = async (type, row) => {
  try {
    await ElMessageBox.confirm(`确定要删除这条评语吗？`, "删除确认", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    });

    const operateLog = {
      operateLogType: "PLATFORM_SETTINGS",
      operateType: `删除了一条名为“${row.content}”的评语`
    };

    // 执行删除
    const data = await commentdeleteId({ id: row.id }, operateLog);
    if (data.code === 200) {
      ElMessage.success("删除成功");
      // 如果当前页只有一条数据且不是第一页，则跳转到上一页
      if (dataList.value.length === 1 && pagination.value.currentPage > 1) {
        pagination.value.currentPage -= 1;
        const params = {
          ...paramsArg,
          page: pagination.value.currentPage - 1,
          size: pagination.value.pageSize
        };
        onSearch(params);
      } else {
        onSearch(); // 刷新当前页
      }
    } else {
      ElMessage.error(data.message || "删除失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除失败:", error);
      ElMessage.error("删除失败，请重试");
    }
  }
};

// 打开弹框
const openDialog = (val, row = null) => {
  showDialog.value = true;
  if (val === "add") {
    titleDialog.value = "新增评语";
    form.value.content = "";
    form.value.displayIndex = ""; // 新增时清空序号
    currentRow.value = null;
  } else if (val === "edit") {
    titleDialog.value = "编辑评语";
    currentRow.value = row;
    // 加载编辑数据
    if (row) {
      form.value.content = row.content || "";
      form.value.displayIndex = row.displayIndex || ""; // 编辑时显示序号
    }
  }
};

// 弹框确认提交
const submit = async formRef => {
  if (!formRef) return;

  try {
    await formRef.validate();
    const operateLog = {
      operateLogType: "PLATFORM_SETTINGS",
      operateType:
        titleDialog.value === "新增评语"
          ? `新增了一条名为“${form.value.content}”的评语`
          : `修改了一条名为“${form.value.content}”的评语`
    };
    if (titleDialog.value === "新增评语") {
      const data = await commentAdd(
        { content: form.value.content },
        operateLog
      );
      if (data.code === 200) {
        ElMessage.success("新增成功");
        showDialog.value = false;
        onSearch(); // 刷新列表
      } else {
        ElMessage.error(data.message || "新增失败");
      }
    } else if (titleDialog.value === "编辑评语") {
      const data = await commentEdit(
        {
          ...form.value,
          id: currentRow.value?.id
        },
        operateLog
      );
      if (data.code === 200) {
        ElMessage.success("编辑成功");
        showDialog.value = false;
        onSearch(); // 刷新列表
      } else {
        ElMessage.error(data.message || "编辑失败");
      }
    }
  } catch (error) {
    console.error("表单校验失败", error);
    ElMessage.error("操作失败，请重试");
  }
};

onMounted(async () => {
  onSearch();
});
</script>

<template>
  <div class="common">
    <div class="common-btn">
      <el-button type="primary" @click="openDialog('add')">
        新增评语
      </el-button>
    </div>
    <div class="puretable">
      <pure-table
        ref="tableRef"
        row-key="id"
        adaptive
        :adaptiveConfig="{ offsetBottom: 108 }"
        align-whole="left"
        table-layout="auto"
        :loading="loadingTable"
        :data="getIndexList"
        :columns="columns"
        :pagination="{ ...pagination }"
        :header-cell-style="{
          background: 'var(--el-fill-color-light)',
          color: 'var(--el-text-color-primary)'
        }"
        @page-size-change="handleSizeChange"
        @page-current-change="handleCurrentChange"
      >
        <template #operation="{ row }">
          <el-button
            class="reset-margin"
            link
            type="primary"
            @click="openDialog('edit', row)"
          >
            编辑
          </el-button>
          <el-button link type="danger" @click="isDeleteApi('delete', row)">
            删除
          </el-button>
        </template>
      </pure-table>
    </div>
    <!-- 弹框 -->
    <PlusDialog
      v-model="showDialog"
      :title="titleDialog"
      cancel-text="取消"
      confirm-text="确定"
      @confirm="submit(formRef)"
    >
      <el-form ref="formRef" :model="form" :rules="rules" label-width="">
        <!-- 编辑时显示序号 -->
        <el-form-item
          v-if="titleDialog === '编辑评语'"
          label="序号"
          prop="displayIndex"
          required
        >
          <el-input
            v-model="form.displayIndex"
            style="width: 100%"
            disabled
            placeholder="序号"
          />
        </el-form-item>
        <el-form-item label="评语内容" prop="content">
          <el-input
            v-model="form.content"
            style="width: 100%"
            placeholder="请输入评语"
          />
        </el-form-item>
      </el-form>
    </PlusDialog>
  </div>
</template>

<style lang="scss" scoped>
.common {
  padding: 20px 20px 2px 20px;
  background-color: #fff;
  margin-bottom: 20px;

  .con_top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: fit-content;
    margin-bottom: 24px;

    .titles {
      font-size: 20px;
      font-weight: bold;
      color: #606266;
    }
  }

  .search {
    display: flex;

    // justify-content: space-between;
    .button {
      display: flex;
      justify-content: right;
    }
  }
}

.buttom {
  display: flex;
  justify-content: end;
}

.upload-demo {
  margin-bottom: 10px;
}
.common-btn {
  width: 100%;
  margin: 0 0 20px 0;
  display: flex;
  justify-content: right;
}
:deep(.el-form-item__label) {
  width: 80px;
  text-align: right;
}
</style>
