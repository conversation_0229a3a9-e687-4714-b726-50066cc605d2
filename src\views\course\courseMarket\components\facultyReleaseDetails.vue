<script lang="js" setup>
import { onMounted, ref } from "vue";
import ExpertInformation from "./expertInformation.vue";
import ExpertEvaluation from "./expertEvaluation.vue";

// onMounted(() => {
//   console.log("师资发布详情页面已加载");
// });

const tabValue = ref([
  {
    label: "专家个人信息",
    id: 0
  },
  {
    label: "专家评价",
    id: 1
  }
]);
// 切换tab
const handleClick = tab => {
  console.log("切换到标签:", tab.props.label);
};
</script>

<template>
  <div class="faculty-release-details">
    <el-tabs @tab-click="handleClick">
      <el-tab-pane v-for="item in tabValue" :key="item.id" :label="item.label">
        <template v-if="item.id === 0">
          <ExpertInformation />
        </template>
        <template v-else-if="item.id === 1">
          <ExpertEvaluation />
        </template>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<style scoped>
.faculty-release-details {
  background-color: #fff;
  padding: 10px 30px;
}
:deep(.el-tabs__nav-wrap::after) {
  display: none !important;
}
</style>
