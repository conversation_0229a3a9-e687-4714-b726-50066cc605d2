<script setup>
import { ref, reactive, onMounted } from "vue";
import { ordersGetOrderDetails } from "@/api/parentManage.js";
import { studentFindById } from "@/api/studentManage.js";
import { requestTo } from "@/utils/http/tool";
import { useRouter, useRoute } from "vue-router";
import { decrypt, encryption } from "@/utils/SM4.js";
import dayjs from "dayjs";
import { Edit, Hide, View } from "@element-plus/icons-vue";
import { ElMessageBox, ElMessage } from "element-plus";
const router = useRouter();
const route = useRoute();

import pdfOne from "@/assets/a.pdf";
const data = [
  { id: 1, url: "", name: "附件1" },
  { id: 2, url: "", name: "附件2" }
];
const currentPdf = ref(null);
const filesAdd = val => {
  console.log("🦄-----pdfOne-----", pdfOne);
  currentPdf.value = pdfOne;
};

const indexList = ref({
  ordersId: "2", // 订单ID
  groupOrder: "是", // 是否团单
  createdAt: "2016-05-04 14:25:66", // 创建时间
  orderStatus: "UNPAID", // 订单状态
  payTime: "2016-05-04 14:25:01", // 付款时间
  finishTime: "2016-05-04 14:25:02", // 完成时间
  chargebackTime: "2016-05-04 14:25:03", // 退单时间
  cancelTime: "2016-05-04 14:25:04", // 取消时间
  courseName: "课程3", // 课程名
  termNumber: "41", // 课程期号
  organizationName: "机构3", // 机构
  organizationServicePhone: "15658632245", // 机构客服热线
  organizationManager: "机构-张三", // 机构管理员
  organizationManagerPhone: "", // 管理员电话

  buyer: "购买人-张三", // 购买人
  buyerPhone: "15658632245", // 购买人电话
  studentName: "学生-李四", // 学生
  specification: "规格1,规格2,规格3", // 购买规格
  price: "¥198.00 (原价220.00)", // 购买价格
  files: "学生-李四" // 订单附带文件
});

const indexListOne = ref();

const startAdd = async val => {
  const paramsArg = {
    id: val.id
  };
  return;
  const [err, res] = await requestTo(ordersGetOrderDetails(paramsArg));
  if (res) {
    console.log("🐬-----res---开始--", res);
    // indexList.value = res
  }
  if (err) {
  }
};
// 子女详情
const startAddOne = async val => {
  const paramsArg = {
    id: val.studentId
  };
  const [err, res] = await requestTo(studentFindById(paramsArg));
  if (res) {
    indexListOne.value = res;
  }
  if (err) {
  }
};
// 证件号
const iconidNum = ref(false);
function idNumAdd(val) {
  iconidNum.value = !iconidNum.value;
}
function idNumberAdd(val) {
  if (val) {
    return decrypt(val);
  } else {
    return "--";
  }
}
function idNumberAdd1(val) {
  console.log("🐠-----val-----", val);
  if (val) {
    let idNumber = decrypt(val);
    return hide_tm(idNumber, "idNumber");
  } else {
    return "--";
  }
}
// 机构客服热线
const iconserv = ref(false);
function servAdd(val) {
  iconserv.value = !iconserv.value;
}
// 管理员电话
const iconorgan = ref(false);
function organAdd(val) {
  iconorgan.value = !iconorgan.value;
}
// 购买人电话
const iconbuyer = ref(false);
function buyerAdd(val) {
  iconbuyer.value = !iconbuyer.value;
}

const titl = ref({});
onMounted(() => {
  let teId = route.query;
  titl.value = teId;
  startAdd(teId);
  startAddOne(teId);
});

const isTime = val => {
  if (val === "UNPAID" || val === "PAID") {
    // 待支付  已支付
    return "--";
  } else if (val === "REFUND") {
    // 已退款
    return indexList.value?.chargebackTime;
  } else if (val === "CANCELLED") {
    // 已取消
    return indexList.value?.courseName;
  } else {
    // 完成时间
    return indexList.value?.finishTime;
  }
};

const isComplete = val => {
  if (val === "REFUND") {
    // 已退款
    return "退单时间";
  } else if (val === "CANCELLED") {
    // 已取消
    return "取消时间";
  } else {
    return "完成时间";
  }
};

const stateAdd = val => {
  if (val === "UNPAID") {
    return "未支付";
  } else if (val === "PAY") {
    return "支付中";
  } else if (val === "PAID") {
    return "已支付";
  } else if (val === "REFUNDING") {
    return "退款中";
  } else if (val === "REFUND") {
    return "已退款";
  } else if (val === "PARTIALLY_REFUNDED") {
    return "部分退款";
  } else if (val === "CANCELLED") {
    return "已取消";
  } else if (val === "COMPLETED") {
    return "已完成";
  } else if (val === "CLOSED") {
    return "已关闭";
  } else if (val === "ERROR") {
    return "异常";
  } else {
    return "--";
  }
};

const hide_tm = (val, key) => {
  if (val == undefined) return;
  if (key == "idNumber") {
    return (
      val.substring(0, 1) +
      "*".repeat(3) +
      val.substring(4, 12) +
      "*".repeat(5) +
      val.substring(17)
    );
  } else if (key == "phone") {
    return val.replace(/^(\d{3})(\d{4})(\d{4})$/, "$1****$3");
  } else if (key == "name") {
    const surname = val.charAt(0); // 获取第一个字符作为姓氏
    const nameLength = val.length - 1; // 剩余的部分作为名字的长度
    const maskedName = surname + "*".repeat(nameLength); // 构造脱敏后的姓名
    return maskedName;
  }
};
</script>

<template>
  <div>
    <div class="common bottom">
      <div class="title">家长管理 \ 详情 \ 子女详情 \ {{ titl.title }}</div>
      <div class="puretable">
        <el-descriptions :column="3" border label-class-name="my-label">
          <el-descriptions-item label="学生ID" width="107px">
            {{ indexListOne?.id || "--" }}
          </el-descriptions-item>
          <el-descriptions-item label="学生姓名" width="107px">
            {{ indexListOne?.name || "--" }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间" width="107px">
            {{
              dayjs(indexListOne?.createdAt).format("YYYY-MM-DD HH:mm:ss") ||
              "--"
            }}
          </el-descriptions-item>
          <el-descriptions-item label="学校" width="107px">
            {{ indexListOne?.school || "--" }}
          </el-descriptions-item>
          <el-descriptions-item label="证件类型" width="107px">
            {{ indexListOne?.idType || "--" }}
          </el-descriptions-item>
          <el-descriptions-item label="证件号" width="107px">
            <div class="iconteyp1">
              <div v-if="iconidNum === true">
                {{ idNumberAdd(indexListOne?.idNumber) }}
              </div>
              <div v-else>
                {{ idNumberAdd1(indexListOne?.idNumber) }}
              </div>
              <div v-if="indexListOne?.idNumber">
                <el-icon
                  v-if="iconidNum === true"
                  style="margin-left: 24px; margin-top: 4px"
                  @click="idNumAdd()"
                >
                  <View />
                </el-icon>
                <el-icon
                  v-else
                  style="margin-left: 24px; margin-top: 4px"
                  @click="idNumAdd()"
                >
                  <Hide />
                </el-icon>
              </div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="家长" width="107px">
            <span
              v-for="it in indexListOne?.parentDTOS"
              :key="it"
              class="text"
              >{{ it.name }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="课程名" width="107px">
            {{ "课程名" || "--" }}
          </el-descriptions-item>
          <el-descriptions-item label="上课时间" width="107px">
            {{ "上课时间" || "--" }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
    <div v-if="titl.title === '实践感悟情况'" class="common">实践感悟情况</div>
    <div v-else-if="titl.title === '家长评价'" class="common">家长评价</div>
    <div v-else-if="titl.title === '关联订单'" class="common">
      <div class="puretable">
        <el-descriptions :column="2" border label-class-name="my-label">
          <el-descriptions-item label="订单号">
            {{ indexList?.ordersId || "--" }}
          </el-descriptions-item>
          <el-descriptions-item label="子订单号">
            {{ indexList?.ordersId || "--" }}
          </el-descriptions-item>
          <el-descriptions-item label="团购发起人ID">
            {{ indexList?.groupOrder || "--" }}
          </el-descriptions-item>
          <el-descriptions-item label="团购发起人">
            {{ indexList?.groupOrder || "--" }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ indexList?.createdAt || "--" }}
          </el-descriptions-item>
          <el-descriptions-item label="订单状态">
            {{ stateAdd(indexList?.orderStatus) || "--" }}
          </el-descriptions-item>
          <el-descriptions-item label="付款时间">
            {{
              indexList?.orderStatus === "UNPAID" ? "--" : indexList?.payTime
            }}
          </el-descriptions-item>
          <el-descriptions-item :label="isComplete(indexList?.orderStatus)">
            {{ isTime(indexList?.orderStatus) || "--" }}
          </el-descriptions-item>

          <el-descriptions-item label="课程名">
            {{ indexList?.courseName || "--" }}
          </el-descriptions-item>
          <el-descriptions-item label="课程期号">
            {{ indexList?.termNumber || "--" }}
          </el-descriptions-item>
          <el-descriptions-item label="机构">
            {{ indexList?.organizationName || "--" }}
          </el-descriptions-item>
          <el-descriptions-item label="机构客服热线">
            <div class="iconteyp">
              <div v-if="iconserv === true">
                {{ indexList?.organizationServicePhone || "--" }}
              </div>
              <div v-else>
                {{
                  hide_tm(indexList?.organizationServicePhone, "phone") || "--"
                }}
              </div>
              <div v-if="indexList?.organizationServicePhone">
                <el-icon
                  v-if="iconserv === true"
                  style="margin-left: 24px; margin-top: 4px"
                  @click="servAdd()"
                >
                  <View />
                </el-icon>
                <el-icon
                  v-else
                  style="margin-left: 24px; margin-top: 4px"
                  @click="servAdd()"
                >
                  <Hide />
                </el-icon>
              </div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="机构管理员">
            {{ indexList?.organizationManager || "--" }}
          </el-descriptions-item>
          <el-descriptions-item label="管理员电话">
            <div class="iconteyp">
              <div v-if="iconorgan === true">
                {{ indexList?.organizationManagerPhone || "--" }}
              </div>
              <div v-else>
                {{
                  hide_tm(indexList?.organizationManagerPhone, "phone") || "--"
                }}
              </div>
              <div v-if="indexList?.organizationManagerPhone">
                <el-icon
                  v-if="iconorgan === true"
                  style="margin-left: 24px; margin-top: 4px"
                  @click="organAdd()"
                >
                  <View />
                </el-icon>
                <el-icon
                  v-else
                  style="margin-left: 24px; margin-top: 4px"
                  @click="organAdd()"
                >
                  <Hide />
                </el-icon>
              </div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="购买人">
            {{ indexList?.buyer || "--" }}
          </el-descriptions-item>
          <el-descriptions-item label="购买人电话">
            <div class="iconteyp">
              <div v-if="iconbuyer === true">
                {{ indexList?.buyerPhone || "--" }}
              </div>
              <div v-else>
                {{ hide_tm(indexList?.buyerPhone, "phone") || "--" }}
              </div>
              <div v-if="indexList?.buyerPhone">
                <el-icon
                  v-if="iconbuyer === true"
                  style="margin-left: 24px; margin-top: 4px"
                  @click="buyerAdd()"
                >
                  <View />
                </el-icon>
                <el-icon
                  v-else
                  style="margin-left: 24px; margin-top: 4px"
                  @click="buyerAdd()"
                >
                  <Hide />
                </el-icon>
              </div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="学生">
            {{ indexList?.studentName || "--" }}
          </el-descriptions-item>
          <el-descriptions-item label="购买规格">
            {{ indexList?.specification || "--" }}
          </el-descriptions-item>
          <el-descriptions-item label="购买价格" span="2">
            {{ indexList?.price || "--" }}
          </el-descriptions-item>
          <el-descriptions-item label="订单附带文件" span="2">
            <span
              v-for="(it, index) in data"
              :key="index"
              class="files"
              @click="filesAdd(it)"
              >{{ it.name }}</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <el-dialog v-model="currentPdf" width="100%" center>
        <iframe
          :src="currentPdf"
          :style="{ width: '100%', height: '70vh', display: 'block' }"
        />
      </el-dialog>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.files {
  margin-right: 10px;
  color: #409eff;
  cursor: pointer;
}
.common {
  width: 100%;
  height: 100%;
  padding: 20px;
  background-color: #fff;
  .title {
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: bold;
    color: #606266;
  }
  .puretable {
    margin-left: 25px;
  }
}

.bottom {
  margin-bottom: 20px;
}
.text {
  margin-right: 10px;
}
.iconteyp {
  display: flex;
  justify-content: space-between;
  width: 140px;
}
.iconteyp1 {
  display: flex;
  justify-content: space-between;
  width: 200px;
}

:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  background: #e1f5ff;
}
</style>
