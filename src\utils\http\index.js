import { useUserStoreHook } from "@/store/modules/user";
import { formatToken, getToken } from "@/utils/auth";
import { encrypt } from "@/utils/auth/sign";
import Axios from "axios";
import { stringify } from "qs";
import NProgress from "../progress";
import baseUrl from "./base";
import { isEmpty } from "@iceywu/utils";
import { saveLog } from "@/utils/saveLog";
import { ElMessage } from "element-plus";
// import { AxiosCanceler } from "./axiosCancel";
// 相关配置请参考：www.axios-js.com/zh-cn/docs/#axios-request-config-1
const defaultConfig = {
  // 当前使用mock模拟请求，将baseURL制空
  baseURL: baseUrl.apiServer,
  // 请求超时时间
  timeout: 10000,
  // headers: {
  //   Accept: "application/json, text/plain, */*",
  //   "Content-Type": "application/json",
  //   "X-Requested-With": "XMLHttpRequest"
  // },
  // 数组格式参数序列化（https://github.com/axios/axios/issues/5142）
  paramsSerializer: {
    serialize: stringify
  }
};

class PureHttp {
  constructor() {
    PureHttp.httpInterceptorsRequest();
    PureHttp.httpInterceptorsResponse();
  }

  /** token过期后，暂存待执行的请求 */
  static requests = [];

  /** 防止重复刷新token */
  static isRefreshing = false;
  /** 防止重复刷新token */
  static isNeedLoading = false;

  /** 防止重复登出 */
  static isLoggingOut = false;

  // 接口是否异常
  static isApiError = false;

  // 业务异常code名单
  static errorCodes = [401, 403, 11014, 11011, 11014, 11012, 11016, -1];

  // 异常
  static codeList = [11011, 11014, 11012, 11016, -1];

  /** 初始化配置对象 */
  static initConfig = {};

  /** 保存当前Axios实例对象 */
  static axiosInstance = Axios.create(defaultConfig);

  /** 重连原始请求 */
  static retryOriginalRequest(config) {
    return new Promise(resolve => {
      PureHttp.requests.push(token => {
        config.headers["Authorization"] = formatToken(token);
        resolve(config);
      });
    });
  }

  /** 请求拦截 */
  static httpInterceptorsRequest() {
    PureHttp.axiosInstance.interceptors.request.use(
      async config => {
        const {
          isNeedToken = true,
          isNeedLoading = false,
          isNeedEncrypt = false,
          serverName,
          headers = {}
          // operateLog = {}
        } = config;
        PureHttp.isNeedLoading = isNeedLoading;
        if (serverName) {
          config.baseURL = baseUrl[serverName] || baseUrl["apiServer"];
        }
        // if (!isEmpty(operateLog)) {
        //   saveLog(operateLog);
        // }
        if (import.meta.env.VITE_APP_MOCK_IN_DEVELOPMENT === "true") {
          config.baseURL = "";
        }
        // header信息
        if (!isEmpty(headers)) {
          Object.keys(headers).forEach(key => {
            config.headers.set(key, headers[key]);
          });
        }
        // config.baseURL = "";
        // 参数处理
        if (isNeedEncrypt) {
          const { data, method, params } = config;
          const { tempData, nonce, timestamp, sign } = encrypt(
            method === "get" ? params : data
          );
          config.headers.timestamp = timestamp;
          config.headers.nonce = nonce;
          config.headers.sign = sign;
          if (method === "get") {
            config.params = tempData;
          } else {
            config.data = tempData;
            // config.params = encrypt(params);
          }
        }
        // config.baseURL = "";

        // 开启进度条动画
        isNeedLoading && NProgress.start();

        // 优先判断post/get等方法是否传入回调，否则执行初始化设置等回调
        if (typeof config.beforeRequestCallback === "function") {
          config.beforeRequestCallback(config);
          return config;
        }
        if (PureHttp.initConfig.beforeRequestCallback) {
          PureHttp.initConfig.beforeRequestCallback(config);
          return config;
        }

        /** 请求白名单，放置一些不需要token的接口（通过设置请求白名单，防止token过期后再请求造成的死循环问题） */

        return !isNeedToken
          ? config
          : new Promise(resolve => {
              const data = getToken();

              if (data) {
                const now = new Date().getTime();
                const expired = parseInt(data.expires) - now <= 0;

                if (expired) {
                  if (!PureHttp.isRefreshing) {
                    PureHttp.isRefreshing = true;
                    // token过期刷新
                    // useUserStoreHook()
                    //   .handRefreshToken({ refreshToken: data.refreshToken })
                    //   .then(res => {
                    //     const token = res.data.accessToken;
                    //     config.headers["Authorization"] = formatToken(token);
                    //     PureHttp.requests.forEach(cb => cb(token));
                    //     PureHttp.requests = [];
                    //   })
                    //   .finally(() => {
                    //     PureHttp.isRefreshing = false;
                    //   });
                  }
                  // resolve(PureHttp.retryOriginalRequest(config));
                  resolve(config);
                } else {
                  config.headers["Authorization"] = formatToken(
                    data.accessToken
                  );
                  resolve(config);
                }
              } else {
                resolve(config);
              }
            });
      },
      error => {
        return Promise.reject(error);
      }
    );
  }

  /** 响应拦截 */
  static httpInterceptorsResponse() {
    const instance = PureHttp.axiosInstance;
    instance.interceptors.response.use(
      response => {
        const $config = response.config;
        // 关闭进度条动画
        if (PureHttp.isNeedLoading && !PureHttp.isApiError) {
          NProgress.done();
        }
        const { code } = response.data;
        // 业务异常code名单
        if (PureHttp.errorCodes.includes(code) && !PureHttp.isApiError) {
          PureHttp.isApiError = true;
          const coseMsg = {
            11012: "登录状态异常，请重新登录",
            11014: "您的账号已在其他地方登录，请重新登录",
            11016: "登录授权信息已过期，请重新登录",
            "-1": "登录授权信息已过期，请重新登录"
          };

          if (!PureHttp.isLoggingOut) {
            PureHttp.isLoggingOut = true; // 标记正在登出

            // 同时显示消息并立即执行登出
            ElMessage({
              message: coseMsg[code] || "登录状态异常，请重新登录",
              type: "error",
              showClose: true
            });

            // 立即执行登出操作，不等待消息关闭
            useUserStoreHook().logOut();

            // 延迟重置标记，确保登出流程完成
            setTimeout(() => {
              PureHttp.isLoggingOut = false;
              PureHttp.isApiError = false;
            }, 500);

            // 阻止后续请求的处理，因为已经登出
            return new Promise(() => {});
          } else {
            // 如果已经在登出过程中，则阻止后续请求的重复处理
            return new Promise(() => {}); // 返回一个永远不 resolve 的 Promise 来中断后续操作
          }
        }
        // 优先判断post/get等方法是否传入回调，否则执行初始化设置等回调
        if (typeof $config.beforeResponseCallback === "function") {
          $config.beforeResponseCallback(response);
          return response.data;
        }
        if (PureHttp.initConfig.beforeResponseCallback) {
          PureHttp.initConfig.beforeResponseCallback(response);
          return response.data;
        }
        const { operateLog } = $config;
        // 只有在 code 为 200 时才执行 saveLog(operateLog)
        if (code === 200 && !isEmpty(operateLog)) {
          saveLog(operateLog);
        }
        return response.data;
      },
      error => {
        const $error = error;
        $error.isCancelRequest = Axios.isCancel($error);
        // 关闭进度条动画
        NProgress.done();
        // 所有的响应异常 区分来源为取消请求/非取消请求
        return Promise.reject($error);
        // return Promise.resolve({
        //   ...error.response.data
        // });
      }
    );
  }

  /** 通用请求工具函数 */
  request(method, url, param, axiosConfig) {
    const config = {
      method,
      url,
      ...param,
      ...axiosConfig
    };

    // 单独处理自定义请求/响应回调
    return new Promise((resolve, reject) => {
      PureHttp.axiosInstance
        .request(config)
        .then(response => {
          resolve(response);
        })
        .catch(error => {
          reject(error);
        });
    });
  }

  /** 单独抽离的`post`工具函数 */
  post(url, params, config) {
    return this.request("post", url, params, config);
  }

  /** 单独抽离的`get`工具函数 */
  get(url, params, config) {
    return this.request("get", url, params, config);
  }
}

export const http = new PureHttp();
