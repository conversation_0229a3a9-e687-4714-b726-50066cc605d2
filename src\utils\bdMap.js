/* global BMapGL, BMAP_STATUS_SUCCESS */

// 获取当前位置信息
export const getLocation = () => {
  return new Promise((resolve, reject) => {
    // 检查百度地图API是否加载
    if (!window.BMapGL || !window.BMapGL.Geolocation) {
      reject(new Error("百度地图API未加载"));
      return;
    }

    const geolocation = new BMapGL.Geolocation();
    geolocation.getCurrentPosition(
      function (r) {
        // 使用数字状态码，0表示成功
        if (this.getStatus() === 0) {
          console.log("获取用户位置成功：", r.point);
          resolve(r.point);
        } else {
          console.log("获取位置失败，状态码：" + this.getStatus());
          reject(new Error("获取位置失败，状态码：" + this.getStatus()));
        }
      },
      { enableHighAccuracy: true }
    );
  });
};

// 传入当前经纬度，目标经纬度，检测范围（m）,返回是否在范围内
export const checkLocation = (current = [], target = [], range = 100) => {
  // eslint-disable-next-line no-use-before-define
  const distance = getDistance(current, target);
  console.log("🌵-----distance-----", distance);
  return distance <= range;
};

// 传入两个经纬度，返回距离（m）
export const getDistance = (current, target) => {
  const map = new BMapGL.Map("bdMapTemp");
  const pointA = new BMapGL.Point(current[0], current[1]);
  const pointB = new BMapGL.Point(target[0], target[1]);
  return map.getDistance(pointA, pointB);
};

// 通过经纬度获取地点
export const getLocationInfo = location => {
  return new Promise((resolve, reject) => {
    const geoc = new BMapGL.Geocoder();
    const point = new BMapGL.Point(location[0], location[1]);
    geoc.getLocation(point, function (rs) {
      const addComp = rs.addressComponents;
      // console.log(addComp);
      // resolve(addComp);
      resolve(rs);
    });
  });
};
// 通过名称搜索地点
export const searchLocationByName = (name, mapObj = "全国") => {
  return new Promise((resolve, reject) => {
    const local = new BMapGL.LocalSearch(mapObj, {
      onSearchComplete: function (result) {
        resolve({ result, local });
      }
    });
    local.search(name);
  });
};
// 添加范围圆形
export const addRangeCircle = (map, center, range = 100) => {
  const point = new BMapGL.Point(center[0], center[1]);
  const circle = new BMapGL.Circle(point, range, {
    strokeColor: "blue",
    strokeWeight: 2,
    strokeOpacity: 0.5
  });
  map.addOverlay(circle);
};
