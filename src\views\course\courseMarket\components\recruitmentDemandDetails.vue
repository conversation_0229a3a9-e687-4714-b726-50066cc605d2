<script lang="js" setup>
import { ref, onMounted } from "vue";
// import { organizationFindById } from "@/api/institution";
import { useRouter, useRoute } from "vue-router";
import RichEditor from "@/components/Base/RichEditor.vue";
import { Hide, View } from "@element-plus/icons-vue";
import { decrypt } from "@/utils/SM4.js";
import { formatTime } from "@/utils/index";
import FileItem from "@/components/PreviewV2/FileItem.vue";

const router = useRouter();
const route = useRoute();

onMounted(() => {
  // getTableList();
});

const form = ref({
  time: "2025-004-21 16:12:34",
  institutionID: "***************",
  name: "",
  alias: "",
  fileDTOs: [],
  organizationCode: "",
  customerServiceHotline: "",
  introduction: "",
  organizationAdmin: {
    name: "",
    account: "",
    phone: "",
    name: ""
  },
  organizationCategory: [],
  trainingCategory: []
});

const formFile = ref({
  institutionLicense: [],
  qualificationDocuments: [],
  video: [],
  environment: []
});
// 课程信息
const courseInformation = ref([
  {
    id: "1",
    label: "课程名",
    prop: "name",
    show: true
  },
  {
    id: "2",
    label: "开课时间",
    prop: "id",
    show: true
  },
  {
    id: "3",
    label: "课期分类",
    prop: "alias"
  },
  {
    id: "4",
    label: "课程亮点标签",
    prop: "code"
  }
]);
const newData = ref();
// const getTableList = async data => {
//   // let params = { id: route.query.id };
//   try {
//     const { code, data, msg } = await organizationFindById();
//     // console.log("🎁-----data-----", code, data, msg);
//     if (code == 200) {
//       form.value = data;
//       if (data.organizationCategory.length > 0) {
//         form.value.organizationCategory = data.organizationCategory.join("、");
//       }
//       if (data.trainingCategory.length > 0) {
//         form.value.trainingCategory = data.trainingCategory.join("、");
//       }
//       formFile.value.institutionLicense = [];
//       formFile.value.qualificationDocuments = [];
//       if (data?.fileDTOS) {
//         data.fileDTOS.forEach(item => {
//           if (item.fileType == "BUSINESS_LICENSE") {
//             formFile.value.institutionLicense.push(item.uploadFile);
//           } else if (item.fileType == "QUALIFICATION_DOCUMENT") {
//             formFile.value.qualificationDocuments.push(item.uploadFile);
//           } else if (item.fileType == "PROMOTIONAL_VIDEO") {
//             formFile.value.video.push(item.uploadFile);
//           } else if (item.fileType == "ENVIRONMENT") {
//             formFile.value.environment.push(item.uploadFile);
//           }
//         });
//       }
//       newData.value = JSON.parse(JSON.stringify(data));
//     }
//   } catch (error) {
//     console.log("catch-----error-----", error);
//   }
// };
const isView = ref(true);
const isViewFn = () => {
  isView.value = !isView.value;
  if (isView.value) {
    form.value.organizationAdmin.phone = newData.value.organizationAdmin.phone;
  } else {
    form.value.organizationAdmin.phone = decrypt(
      newData.value.organizationAdmin.phoneCt
    );
  }
};
//删除文件
const getDeleted = (item, index) => {
  // form.value[item].splice(index, 1);
  formFile.value[item].splice(index, 1);
};

// 获取图片URL列表用于预览
const getImageUrlList = prop => {
  if (!formFile.value[prop] || formFile.value[prop].length === 0) {
    return [];
  }
  return formFile.value[prop].map(item => item.url);
};
</script>

<template>
  <div class="main">
    <el-scrollbar class="scrollbar">
      <div>
        <div class="account_management">课程信息</div>
        <el-descriptions
          class="margin-top"
          title=""
          :column="1"
          border
          style="width: 100%"
          :label-width="'200px'"
        >
          <template v-for="(item, index) in courseInformation" :key="index">
            <el-descriptions-item
              v-if="item.show || form[item.prop]"
              label-class-name="my-label"
              label-align="right"
              :span="item.span || 1"
            >
              <template #label>
                <div class="cell-item">
                  {{ item.label }}
                </div>
              </template>
              <div v-if="item.type !== 'time'">
                <div>{{ form[item.prop] || "--" }}</div>
              </div>
              <div v-else>
                {{
                  item.prop === "createdAt" || item.prop === "establishmentTime"
                    ? formatTime(form.createdAt, "YYYY-MM-DD HH:mm:ss")
                    : form[item.prop] || "--"
                }}
              </div>
            </el-descriptions-item>
          </template>
        </el-descriptions>
        <div class="account_management">招募需求</div>
        <div class="text-box">我是招募需求</div>
      </div>
    </el-scrollbar>
  </div>
</template>

<style lang="scss" scoped>
.main {
  background: #fff;
}
.scrollbar {
  padding: 20px;
  height: calc(100vh - 115px);
  background-color: #fff;
}
.Editor {
  border: 1px solid #e4e4e4e3;
}

.account_management {
  color: #0e1832;
  font-family: Source Han Sans CN;
  font-size: 16px;
  display: flex;
  align-items: center;
  margin: 20px 0 20px 10px;
  font-weight: 500;

  &::after {
    content: "";
    flex: 1;
    height: 1px;
    background: #e4e7ed;
    margin-left: 16px;
  }
}

:deep(.my-label) {
  background: #fff !important;
}
.fileOther {
  // margin-top: 26px;
  margin-left: 20px;
  display: flex;
  align-items: center;
  line-height: 22px;
  width: 420px;
  // margin-bottom: 56px;
  .title {
    color: #0e1832;
    font-family: Source Han Sans CN;
    font-size: 16px;
    font-weight: 400;
    height: 24px;
    white-space: nowrap;
  }
}
.cell_item {
  display: flex;
  .icon {
    display: flex;
    align-items: center;
    margin-left: 5px;
  }
}
:deep(.fileOther) {
  margin-left: 0;
  width: auto;
}
:deep(
  .el-descriptions__body
    .el-descriptions__table.is-bordered
    .el-descriptions__cell
) {
  border: 0px !important;
}
.text-box {
  border: 1px solid #e4e7ed;
  padding: 20px;
  margin: 20px 20px 0 20px;
  height: 500px;
  font-size: 14px;
  //   color: #e4e7ed;
}
</style>
