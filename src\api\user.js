import { http } from "@/utils/http";

/** 登录 */
export const getLogin = data => {
  return http.request("post", "/login", { data }, { isNeedToken: false });
};

/** 刷新`token` */
export const refreshTokenApi = data => {
  return http.request("post", "/refresh-token", { data });
};

/** 账户设置-个人信息 */
export const getMine = data => {
  return http.request("get", "/mine", { data });
};

/** 账户设置-个人安全日志 */
export const getMineLogs = data => {
  return http.request("get", "/mine-logs", { data });
};

// 手机号验证码获取机构账号
export const getLoginPhone = data => {
  return http.request("post", "/platform/admin/phoneLogin", { data });
};

// 根据code登录
export const getLoginByCode = data => {
  return http.request(
    "post",
    "/platform/admin/codeLogin",
    { data },
    {
      isNeedEncrypt: true
    }
  );
};

// 根据code绑定微信 平台
export const getBindCodePlatform = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/admin/bindPhone",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};

// 根据code解绑微信 平台
export const getUnbindPlatform = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/admin/unbind",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};

// 根据code绑定微信 机构
export const getBindCodeOrganization = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/organizationAdmin/bindPhone",
    {
      data
    },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};

// 根据code绑定微信 机构
export const getUnbindOrganization = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/organizationAdmin/unbind",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
