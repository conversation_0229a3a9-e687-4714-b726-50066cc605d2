<script setup>
import { ref, reactive, onMounted, onActivated } from "vue";
import { ordersFindAll, ordersRefund } from "@/api/parentManage.js";
import { requestTo } from "@/utils/http/tool";
import { useRouter, useRoute } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import dayjs from "dayjs";

defineOptions({
  name: "ParentManagementCompontsParentOrderManage"
});

const router = useRouter();
const route = useRoute();
const from = reactive({
  time: [],
  courseName: "",
  organizationName: "",
  orderStatus: ""
});

// const openDialog = (text, val) => {
//   console.log("🎉-----val--详情---", val);
//   router.push({
//     path: "/parentManagement/componts/orderDetails",
//     query: { id: val.id }
//   });
// };

// 查看详情
const openDialog = (text, val) => {
  router.push({
    path: "/parentManagement/orderManagement/orderDetails",
    query: { id: val.id, title: "parentManagement" }
  });
};

const open = (text, val) => {
  ElMessageBox.confirm("确定要退单吗？", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      isOpen(text, val);
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "取消"
      });
    });
};

const isOpen = async (text, val) => {
  const paramsArg = {
    id: val.ordersId
  };
  const operateLog = {
    operateLogType: "PARENT_MANAGEMENT",
    operateType: "退了订单号" + val.ordersId
    // operatorTarget: form.value.name,
  };
  const { code } = await ordersRefund(paramsArg, operateLog);
  if (code === 200) {
    ElMessage({
      type: "success",
      message: "已退单"
    });
  } else {
    ElMessage({
      type: "error",
      message: "退单失败"
    });
  }
  onSearch();
};

const dataList = ref([
  {
    ordersId: 1,
    createdAt: 1742287265805,
    courseName: "课程1",
    termNumber: "11",
    organizationName: "机构1",
    orderStatus: "COMPLETED"
  }
]);
const pagination = {
  total: 0,
  pageSize: 10,
  currentPage: 1,
  background: true
};

const loadingTable = ref(false);

const columns = [
  {
    label: "订单号", // 如果需要表格多选，此处label必须设置
    prop: "orderNo",
    width: 90,
    formatter: ({ orderNo }) => {
      return orderNo || "--";
    }
  },
  {
    label: "创建时间",
    width: 180,
    prop: "createdAt",
    formatter: ({ createdAt }) => {
      return createdAt ? dayjs(createdAt).format("YYYY-MM-DD HH:mm:ss") : "--";
    }
  },
  {
    label: "课程名",
    prop: "courseName",
    minWidth: 130,
    formatter: ({ courseName }) => {
      return courseName || "--";
    }
  },
  {
    label: "课程期号",
    prop: "termNumber",
    width: 120,
    formatter: ({ termNumber }) => {
      return termNumber || "--";
    }
  },
  {
    label: "机构",
    prop: "organizationName",
    minWidth: 90,
    formatter: ({ organizationName }) => {
      return organizationName || "--";
    }
  },
  {
    label: "订单状态",
    prop: "orderStatus",
    width: 90,
    formatter: ({ orderStatus }) => {
      return orderStatusTypeoptions[orderStatus] || "--";
    }
  },
  {
    label: "退款状态",
    prop: "orderStatus",
    width: 90,
    formatter: ({ refundStatus }) => {
      return orderStatusTypeoptions[refundStatus] || "--";
    }
  },
  {
    label: "操作",
    fixed: "right",
    width: 120,
    slot: "operation"
  }
];
const orderStatusTypeoptions = {
  UNPAID: "未支付",
  PAID: "已支付",
  PAY_FAIL: "支付失败",
  CANCELLED: "已取消",
  COMPLETED: "已完成",
  PAY: "支付中",
  REFUNDING: "退款中",
  REFUNDED: "已退款",
  PARTIALLY_REFUNDED: "部分退款",
  APPLICATION: "申请退款",
  REFUND_REJECT: "退款驳回",
  REFUND_FAILED: "退款失败",
  PARTIAL_REFUND: "部分退款",
  PARTIAL_CANCEL: "部分取消",
  CANCEL: "取消"
};

const options = [
  { value: "全部", label: "全部" },
  { value: "UNPAID", label: "未支付" },
  { value: "PAY", label: "支付中" },
  { value: "PAID", label: "已支付" },
  { value: "REFUNDING", label: "退款中" },
  { value: "REFUND", label: "已退款" },
  { value: "PARTIALLY_REFUNDED", label: "部分退款" },
  { value: "CANCELLED", label: "已取消" },
  { value: "COMPLETED", label: "已完成" },
  { value: "CLOSED", label: "已关闭" },
  { value: "ERROR", label: "异常" }
];

// 每页多少条
async function handleSizeChange(val) {
  pagination.pageSize = val;
  let time1 = from.time !== null ? new Date(from.time[0]).getTime() : "";
  let time2 = from.time !== null ? new Date(from.time[1]).getTime() : "";
  const paramsArg = {
    courseName: from.courseName || "",
    organizationName: from.organizationName || "",
    orderStatus: from.orderStatus || "",
    startTime: time1 || "",
    endTime: time2 || "",
    page: 0,
    size: val
  };
  startAdd1(paramsArg);
}
// 前往页数
async function handleCurrentChange(val) {
  pagination.currentPage = val;
  let time1 = from.time !== null ? new Date(from.time[0]).getTime() : "";
  let time2 = from.time !== null ? new Date(from.time[1]).getTime() : "";
  const paramsArg = {
    courseName: from.courseName || "",
    organizationName: from.organizationName || "",
    orderStatus: from.orderStatus || "",
    startTime: time1 || "",
    endTime: time2 || "",
    page: val - 1,
    size: pagination.pageSize
  };
  startAdd1(paramsArg);
}
// 搜索
const onSearch = () => {
  let time1 = from.time !== null ? new Date(from.time[0]).getTime() : "";
  let time2 = from.time !== null ? new Date(from.time[1]).getTime() : "";
  const text = {
    courseName: from.courseName || "",
    organizationName: from.organizationName || "",
    orderStatus: from.orderStatus || "",
    startTime: time1 || "",
    endTime: time2 || "",
    page: pagination.currentPage - 1 || "",
    size: pagination.pageSize || ""
  };
  startAdd1(text);
};

// 重置
const setData = () => {
  from.courseName = "";
  from.organizationName = "";
  from.orderStatus = "";
  from.time = [];
};

const removeEmptyValues = obj => {
  return Object.fromEntries(
    Object.entries(obj).filter(
      ([key, value]) =>
        value !== "" && !(Array.isArray(value) && value.length === 0)
    )
  );
};

const startAdd1 = async val => {
  const paramsArg = {
    parentId: teId.value.id,
    courseName: val.courseName || "",
    organizationName: val.organizationName || "",
    orderStatus:
      from.orderStatus === ("全部" || "" || null || undefined)
        ? ""
        : from.orderStatus || "",
    startTime: val.startTime || "",
    endTime: val.endTime || "",
    page: val.page || 0,
    size: val.size || "",
    sort: "createdAt,desc"
  };
  let aee = removeEmptyValues(paramsArg);
  console.log("🐬-----aee-----", aee);
  const [err, res] = await requestTo(ordersFindAll(aee));
  if (res) {
    console.log("🐬-----res-----", res);
    dataList.value = res.content;
    pagination.total = res.totalElements;
    // pagination.pageSize = res.size;
    // pagination.currentPage = res.totalPages === 0 ? 1 : res.totalPages;
  }
  if (err) {
  }
};

const startAdd = async () => {
  const paramsArg = {
    page: 0,
    size: 10,
    parentId: teId.value.id,
    sort: "createdAt,desc"
  };
  console.log("🐳-----paramsArg-----", paramsArg);
  const [err, res] = await requestTo(ordersFindAll(paramsArg));
  if (res) {
    console.log("🌳-----res-----", res);
    dataList.value = res.content;
    pagination.total = res.totalElements;
  }
  if (err) {
  }
};

onActivated(() => {
  let time1 = from.time !== null ? new Date(from.time[0]).getTime() : "";
  let time2 = from.time !== null ? new Date(from.time[1]).getTime() : "";
  const text = {
    courseName: from.courseName || "",
    organizationName: from.organizationName || "",
    orderStatus: from.orderStatus || "",
    startTime: time1 || "",
    endTime: time2 || "",
    page: pagination.currentPage - 1 || "",
    size: pagination.pageSize || ""
  };
  startAdd1(text);
});

const teId = ref();
onMounted(() => {
  teId.value = route.query;
  startAdd();
});
const defaultTime = [
  new Date(2024, 4, 1, 0, 0, 0),
  new Date(2024, 4, 1, 23, 59, 59)
];
</script>

<template>
  <div>
    <div class="common bottom">
      <!-- <div class="title">家长管理 \ 订单管理</div> -->
      <div class="search">
        <el-form :inline="true" :model="from" class="demo-form-inline">
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="from.time"
              type="daterange"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              :default-time="defaultTime"
            />
          </el-form-item>
          <el-form-item label="课程名">
            <el-input
              v-model="from.courseName"
              placeholder="请输入课程名"
              clearable
            />
          </el-form-item>
          <el-form-item label="机构">
            <el-input
              v-model="from.organizationName"
              placeholder="请输入机构"
              clearable
            />
          </el-form-item>
          <el-form-item label="订单类型">
            <el-select
              v-model="from.orderStatus"
              placeholder="请选择订单类型"
              style="width: 200px"
              clearable
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <div class="button">
              <el-button type="primary" @click="onSearch(from)">搜索</el-button>
              <el-button @click="setData(from)">重置</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="common">
      <div class="puretable">
        <pure-table
          ref="tableRef"
          row-key="id"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="left"
          table-layout="auto"
          :loading="loadingTable"
          :data="dataList"
          :columns="columns"
          :pagination="{ ...pagination }"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #operation="{ row }">
            <div class="botlist">
              <div class="u">
                <el-button
                  class="reset-margin"
                  link
                  type="primary"
                  @click="openDialog('详情', row)"
                >
                  详情
                </el-button>
              </div>
              <!-- <div class="u">
                <el-button
                  v-if="row?.orderStatus === 'REFUNDING'"
                  link
                  type="primary"
                  @click="open('退单', row)"
                >
                  退单
                </el-button>
              </div> -->
            </div>
          </template>
        </pure-table>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.common {
  width: 100%;
  height: 100%;
  padding: 20px 20px 2px;
  background-color: #fff;
  .title {
    margin-bottom: 24px;
    font-size: 20px;
    font-weight: bold;
    color: #606266;
  }
  .search {
    display: flex;
    justify-content: space-between;
    // margin-bottom: 20px;
    .button {
      display: flex;
      justify-content: right;
    }
  }
}
.botlist {
  min-width: 120px;
  display: flex;
  // justify-content: center;
  // .u {
  //   width: 60px;
  // }
}
.bottom {
  margin-bottom: 24px;
  padding-bottom: 2px;
}
</style>
