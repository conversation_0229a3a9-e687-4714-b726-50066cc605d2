<script lang="js" setup>
import { ref, onMounted } from "vue";
// import { organizationFindById } from "@/api/institution";
import { useRouter, useRoute } from "vue-router";
import RichEditor from "@/components/Base/RichEditor.vue";
import { Hide, View } from "@element-plus/icons-vue";
import { decrypt } from "@/utils/SM4.js";
import { formatTime } from "@/utils/index";
import FileItem from "@/components/PreviewV2/FileItem.vue";

const router = useRouter();
const route = useRoute();

onMounted(() => {
  // getTableList();
});

const form = ref({
  time: "2025-004-21 16:12:34",
  institutionID: "***************",
  name: "",
  alias: "",
  fileDTOs: [],
  organizationCode: "",
  customerServiceHotline: "",
  introduction: "",
  organizationAdmin: {
    name: "",
    account: "",
    phone: "",
    name: ""
  },
  organizationCategory: [],
  trainingCategory: []
});

const formFile = ref({
  institutionLicense: [],
  qualificationDocuments: [],
  video: [],
  environment: []
});
// 基本信息
const tableHeatder = ref([
  {
    id: "1",
    label: "教师姓名",
    prop: "name"
  },
  {
    id: "2",
    label: "年龄",
    prop: "id"
  },
  {
    id: "3",
    label: "性别",
    prop: "alias"
  },
  {
    id: "4",
    label: "联系方式",
    prop: "code"
  },
  {
    id: "5",
    label: "教师编号",
    prop: "customerServiceHotline"
  },
  {
    id: "6",
    label: "政治面貌",
    prop: "organizationCode"
  },
  {
    id: "7",
    label: "身份证",
    prop: "legalPerson"
  },
  {
    id: "8",
    label: "健康证明",
    prop: "principalContact"
  },
  {
    id: "9",
    label: "无犯罪证明",
    prop: "establishmentTime",
    type: "time"
  },
  {
    id: "10",
    label: "心理健康筛查证明",
    prop: "administrativeDivision"
  }
]);
// 教学背景
const Taching = ref([
  {
    id: "1",
    label: "学历",
    prop: "logo"
  },
  {
    id: "2",
    label: "所学专业",
    prop: "oneSentenceIntroduction"
  },
  {
    id: "3",
    label: "从教时长",
    prop: "introduction"
  },
  {
    id: "4",
    label: "在职",
    prop: "video"
  },
  {
    id: "5",
    label: "职称级别",
    prop: "environment"
  },
  {
    id: "6",
    label: "毕业证",
    prop: "oneSentenceIntroduction"
  },
  {
    id: "7",
    label: "教师资格证",
    prop: "oneSentenceIntroduction"
  },
  {
    id: "8",
    label: "教师资格证号码",
    prop: "oneSentenceIntroduction"
  },
  {
    id: "9",
    label: "可任教课程",
    prop: "oneSentenceIntroduction"
  }
]);
// 个人风采
const person = ref([
  {
    id: "1",
    label: "个人头像",
    type: "img",
    prop: "logo"
  },
  {
    id: "2",
    label: "擅长课程",
    type: "text",
    prop: "qualificationDocuments"
  },
  {
    id: "3",
    label: "兴趣爱好",
    type: "text",
    prop: "qualificationDocuments"
  },
  {
    id: "4",
    label: "个人风采",
    type: "images",
    prop: "environment"
  }
]);

const tableFooter = ref([
  {
    id: "1",
    label: "姓名",
    // prop: form.value.organizationAdmin.name
    prop: "name"
  },
  {
    id: "2",
    label: "账号",
    prop: "account"
  },
  {
    id: "3",
    label: "手机号",
    prop: "phone"
  }
]);
const newData = ref();
// const getTableList = async data => {
//   // let params = { id: route.query.id };
//   try {
//     const { code, data, msg } = await organizationFindById();
//     // console.log("🎁-----data-----", code, data, msg);
//     if (code == 200) {
//       form.value = data;
//       if (data.organizationCategory.length > 0) {
//         form.value.organizationCategory = data.organizationCategory.join("、");
//       }
//       if (data.trainingCategory.length > 0) {
//         form.value.trainingCategory = data.trainingCategory.join("、");
//       }
//       formFile.value.institutionLicense = [];
//       formFile.value.qualificationDocuments = [];
//       if (data?.fileDTOS) {
//         data.fileDTOS.forEach(item => {
//           if (item.fileType == "BUSINESS_LICENSE") {
//             formFile.value.institutionLicense.push(item.uploadFile);
//           } else if (item.fileType == "QUALIFICATION_DOCUMENT") {
//             formFile.value.qualificationDocuments.push(item.uploadFile);
//           } else if (item.fileType == "PROMOTIONAL_VIDEO") {
//             formFile.value.video.push(item.uploadFile);
//           } else if (item.fileType == "ENVIRONMENT") {
//             formFile.value.environment.push(item.uploadFile);
//           }
//         });
//       }
//       newData.value = JSON.parse(JSON.stringify(data));
//     }
//   } catch (error) {
//     console.log("catch-----error-----", error);
//   }
// };
const isView = ref(true);
const isViewFn = () => {
  isView.value = !isView.value;
  if (isView.value) {
    form.value.organizationAdmin.phone = newData.value.organizationAdmin.phone;
  } else {
    form.value.organizationAdmin.phone = decrypt(
      newData.value.organizationAdmin.phoneCt
    );
  }
};
//删除文件
const getDeleted = (item, index) => {
  // form.value[item].splice(index, 1);
  formFile.value[item].splice(index, 1);
};

// 获取图片URL列表用于预览
const getImageUrlList = prop => {
  if (!formFile.value[prop] || formFile.value[prop].length === 0) {
    return [];
  }
  return formFile.value[prop].map(item => item.url);
};
</script>

<template>
  <div class="main">
    <el-scrollbar class="scrollbar">
      <div>
        <div class="account_management">基本信息</div>
        <el-descriptions
          class="margin-top"
          title=""
          :column="1"
          border
          style="width: 100%"
          :label-width="'200px'"
        >
          <template v-for="(item, index) in tableHeatder" :key="index">
            <el-descriptions-item
              label-class-name="my-label"
              label-align="right"
              :span="item.span || 1"
            >
              <template #label>
                <div class="cell-item">
                  {{ item.label }}
                </div>
              </template>
              <div v-if="item.type !== 'time'">
                <div>{{ form[item.prop] || "--" }}</div>
              </div>
              <div v-else>
                {{
                  item.prop === "createdAt" || item.prop === "establishmentTime"
                    ? formatTime(form.createdAt, "YYYY-MM-DD HH:mm:ss")
                    : form[item.prop] || "--"
                }}
              </div>
            </el-descriptions-item>
          </template>
        </el-descriptions>
        <div class="account_management">教学背景</div>
        <el-descriptions
          class="margin-top"
          title=""
          :column="1"
          border
          style="width: 100%"
          :label-width="'200px'"
        >
          <template v-for="(item, index) in Taching" :key="index">
            <el-descriptions-item
              label-class-name="my-label"
              label-align="right"
              :span="item.span || 1"
            >
              <template #label>
                <div class="cell-item">
                  {{ item.label }}
                </div>
              </template>
              <div v-if="item.type !== 'time'">
                <div>{{ form[item.prop] || "--" }}</div>
              </div>
              <div v-else>
                {{
                  item.prop === "createdAt" || item.prop === "establishmentTime"
                    ? formatTime(form.createdAt, "YYYY-MM-DD HH:mm:ss")
                    : form[item.prop] || "--"
                }}
              </div>
            </el-descriptions-item>
          </template>
        </el-descriptions>

        <div class="account_management">个人风采</div>
        <el-descriptions
          class="margin-top"
          title=""
          :column="1"
          border
          style="width: 100%"
          :label-width="'200px'"
        >
          <template v-for="(item, index) in person" :key="index">
            <el-descriptions-item
              label-class-name="my-label"
              label-align="right"
            >
              <template #label>
                <div class="cell-item">
                  {{ item.label }}
                </div>
              </template>
              <div v-if="item.type === 'img'">
                <text v-if="!form[item.prop]">--</text>
                <el-image
                  v-else
                  class="img_item"
                  :src="form[item.prop]"
                  fit="cover"
                  :preview-src-list="form[item.prop] ? [form[item.prop]] : []"
                />
              </div>
              <div v-if="item.type === 'images'">
                <text v-if="formFile[item.prop].length === 0">--</text>
                <template v-else>
                  <el-image
                    v-for="(item2, index2) in formFile[item.prop]"
                    :key="index2"
                    style="width: 100px; height: 100px; margin-right: 10px"
                    :src="item2.url"
                    fit="contain"
                    :preview-src-list="getImageUrlList(item.prop)"
                    :initial-index="index2"
                  />
                </template>
              </div>
              <div v-if="item.type === 'text'">
                {{ form[item.prop] || "--" }}
              </div>
            </el-descriptions-item>
          </template>
        </el-descriptions>
      </div>
    </el-scrollbar>
  </div>
</template>

<style lang="scss" scoped>
.main {
  background: #fff;
}
.scrollbar {
  padding: 20px;
  height: calc(100vh - 115px);
  background-color: #fff;
}
.Editor {
  border: 1px solid #e4e4e4e3;
}

.header {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 20px;

  .title_left {
    display: flex;

    :nth-child(2) {
      margin-left: 40px;
    }
  }

  .title_rigth {
    display: flex;

    :nth-child(2) {
      margin-left: 40px;
    }
  }
}

.account_management {
  color: #0e1832;
  font-family: Source Han Sans CN;
  font-size: 16px;
  display: flex;
  align-items: center;
  margin: 20px 0 20px 10px;
  font-weight: 500;

  &::after {
    content: "";
    flex: 1;
    height: 1px;
    background: #e4e7ed;
    margin-left: 16px;
  }
}

:deep(.my-label) {
  background: #fff !important;
}
.fileOther {
  // margin-top: 26px;
  margin-left: 20px;
  display: flex;
  align-items: center;
  line-height: 22px;
  width: 420px;
  // margin-bottom: 56px;
  .title {
    color: #0e1832;
    font-family: Source Han Sans CN;
    font-size: 16px;
    font-weight: 400;
    height: 24px;
    white-space: nowrap;
  }
  .link {
    width: 20px;
    height: 20px;
    margin: 0 10px;
    cursor: pointer;
  }
  .fileName {
    color: #3876fd;
    font-family: Source Han Sans CN;
    font-size: 16px;
    font-weight: 400;
    height: 24px;
    cursor: pointer;
    // width: 350px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .linkDetele {
    width: 20px;
    height: 20px;
    margin-left: 10px;
    cursor: pointer;
  }
}
.cell_item {
  display: flex;
  .icon {
    display: flex;
    align-items: center;
    margin-left: 5px;
  }
}
:deep(.fileOther) {
  margin-left: 0;
  width: auto;
}
// :deep(.el-descriptions__cell){
// width: auto;
// }
:deep(
  .el-descriptions__body
    .el-descriptions__table.is-bordered
    .el-descriptions__cell
) {
  border: 0px !important;
}
.img_item {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  border: 1px solid #e4e4e4e3;
  overflow: hidden;
  object-fit: cover;
}
</style>
