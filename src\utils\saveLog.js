import { useUserStoreHook } from "@/store/modules/user";
import { isEmpty, removeEmptyValues } from "@iceywu/utils";
import { operateLogSave } from "@/api/institution";
import { operateLogSaveLocalEnd } from "@/api/localEnd";

export const saveLog = data => {
  console.log("🌵-----data-----", data);

  const {
    operateLogType,
    detail = "",
    additionalParameter,
    operator,
    operatorTarget,
    operateType
  } = data;
  let detailVal = detail;
  if (isEmpty(detail)) {
    const userName = operator ?? useUserStoreHook()?.username;
    detailVal = `${userName}${operateType}${operatorTarget}`;
  }
  const params = {
    operateLogType,
    detail: detailVal,
    additionalParameter
  };
  // console.log("🐳-----params-----", params);
  if (useUserStoreHook().roleTarget === "局端管理员") {
    operateLogSaveLocalEnd(removeEmptyValues(params));
  } else {
    operateLogSave(removeEmptyValues(params));
  }
};

export const saveLoginLog = async (username, roleTarget, type = "") => {
  const logParams = {
    operateLogType: "ACCOUNT_MANAGEMENT",
    detail: `${username + type}登录了系统`,
    operateType: "登录",
    operatorTarget: "平台端"
  };
  try {
    //TODO: hardcode 后续可能需要修改
    roleTarget !== "局端管理员" && (await operateLogSave(logParams));
  } catch (err) {
    console.error("登录日志记录失败", err);
  }
};
