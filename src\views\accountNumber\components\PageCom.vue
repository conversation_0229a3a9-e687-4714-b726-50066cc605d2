<script setup>
import { ref, onMounted } from "vue";
let prop = defineProps({
  title: { default: "", type: String },
  // eslint-disable-next-line vue/require-valid-default-prop
  clickPage: { default: "", type: Boolean }
});
const contentRef = ref();
// 重置scrollTop
const resetScrollTop = () => {
  contentRef.value.setScrollTop(0);
};

defineExpose({
  resetScrollTop
});

//判断是否点击分页，点击分页，页面置顶
// props.clickPage
// watch(()=>prop.clickPage,
// (val)=>{
//     console.log('🌳-----val-----', val);
//     if(val){
//         console.log('🍪-----val-----', val);
//         window.scrollTo(0,0);
//     }
// })
</script>

<template>
  <div class="page">
    <div class="title">
      <div v-if="title" class="titleName">{{ title }}</div>
      <slot name="title" />
      <slot name="form" />
    </div>
    <el-scrollbar ref="contentRef" class="content">
      <slot />
    </el-scrollbar>
    <div>
      <slot name="footer" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.page {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background: #fff;
  .title {
    width: 100%;
    height: 80px;
    display: flex;
    align-items: center;
    padding-left: 50px;
    box-sizing: border-box;
    justify-content: space-between;

    .titleName {
      font-size: 15px;
      font-weight: bold;
      color: #606266;
      cursor: text;
    }
  }
  .content {
    width: 100%;
    height: 100%;
    overflow: auto;
  }
  .footer {
    width: 100%;
    height: fit-content;
  }
}
</style>
