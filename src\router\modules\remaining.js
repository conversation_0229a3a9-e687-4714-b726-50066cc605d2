import { $t } from "@/plugins/i18n";
import { reCodesList } from "@/router/accidCode.js";
const Layout = () => import("@/layout/index.vue");

export default [
  {
    path: "/login",
    name: "Login",
    component: () => import("@/views/login/index.vue"),
    meta: {
      title: $t("menus.pureLogin"),
      showLink: false,
      rank: 101
    }
  },
  {
    path: "/redirect",
    component: Layout,
    meta: {
      title: $t("status.pureLoad"),
      showLink: false,
      rank: 102
    },
    children: [
      {
        path: "/redirect/:path(.*)",
        name: "Redirect",
        component: () => import("@/layout/redirect.vue")
      },
      // {
      //   path: "/account/localend/account/add",
      //   name: "localendAccountAdd",
      //   component: () =>
      //     import("@/views/accountNumber/localEndAccount/add.vue"),
      //   meta: {
      //     // title: $t("menus.pureFourZeroOne")
      //     title: "局端账号创建",
      //     idCode: reCodesList.localendAccountAdd
      //   }
      // },
      // {
      //   path: "/account/platform/account/detail",
      //   name: "platformAccountDetail",
      //   component: () =>
      //     import("@/views/accountNumber/platformAccount/detail.vue"),
      //   meta: {
      //     // title: $t("menus.pureFourZeroOne")
      //     title: "平台账号详情",
      //     idCode: reCodesList.platformAccountDetail
      //   }
      // },
      // {
      //   path: "/account/platform/account/add",
      //   name: "platformAccountAdd",
      //   component: () =>
      //     import("@/views/accountNumber/platformAccount/add.vue"),
      //   meta: {
      //     // title: $t("menus.pureFourZeroOne")
      //     title: "平台账号创建",
      //     idCode: reCodesList.platformAccountAdd
      //   }
      // },
      {
        path: "/course/examine/detail",
        name: "courseExamineDetail",
        component: () => import("@/views/course/courseExamine/detail.vue"),
        meta: {
          // title: $t("menus.pureFourZeroOne")
          title: "审核详情",
          idCode: reCodesList.courseExamineDetail
        }
      },
      // {
      //   path: "/course/management/detail",
      //   name: "courseManagementDetail",
      //   component: () =>
      //     import("@/views/course/courseManagement/courseDetail.vue"),
      //   meta: {
      //     // title: $t("menus.pureFourZeroOne")
      //     title: "课程详情",
      //     idCode: reCodesList.courseManagementDetail
      //   }
      // },
      // {
      //   path: "/course/management/information",
      //   name: "courseManagementInformation",
      //   component: () =>
      //     import("@/views/course/courseManagement/detailInformation.vue"),
      //   meta: {
      //     // title: $t("menus.pureFourZeroOne")
      //     title: "详细资料",
      //     idCode: reCodesList.courseManagementInformation
      //   }
      // },
      // {
      //   path: "/course/management/current/details",
      //   name: "courseManagementCurrentDetails",
      //   component: () =>
      //     import("@/views/course/courseManagement/currentDetails.vue"),
      //   meta: {
      //     // title: $t("menus.pureFourZeroOne")
      //     title: "当期详情",
      //     idCode: reCodesList.courseManagementCurrentDetails
      //   }
      // },
      // {
      //   path: "/parentManagement/componts/parentOrderManage",
      //   name: "parentManagementCompontsParentOrderManage",
      //   component: () =>
      //     import(
      //       "@/views/accountNumber/parentManagement/componts/parentOrderManage.vue"
      //     ),
      //   meta: {
      //     // title: $t("menus.pureFourZeroOne")
      //     title: "订单管理",
      //     idCode: reCodesList.parentManagementCompontsParentOrderManage
      //   }
      // },
      // {
      //   path: "/parentManagement/componts/parentDetails",
      //   name: "parentManagementCompontsParentDetails",
      //   component: () =>
      //     import(
      //       "@/views/accountNumber/parentManagement/componts/parentDetails.vue"
      //     ),
      //   meta: {
      //     // title: $t("menus.pureFourZeroOne")
      //     title: "详情",
      //     a:["账号","家长管理","家长管理详情"],
      //     idCode: reCodesList.parentManagementCompontsParentDetails
      //   }
      // },
      // {
      //   path: "/parentManagement/componts/childrenDetails",
      //   name: "parentManagementCompontsChildrenDetails",
      //   component: () =>
      //     import(
      //       "@/views/accountNumber/parentManagement/componts/childrenDetails.vue"
      //     ),
      //   meta: {
      //     // title: $t("menus.pureFourZeroOne")
      //     title: "子女详情",
      //     idCode: reCodesList.parentManagementCompontsChildrenDetails
      //   }
      // },
      // {
      //   path: "/studentManagement/componts/studentDetails",
      //   name: "studentManagementCompontsStudentDetails",
      //   component: () =>
      //     import(
      //       "@/views/accountNumber/studentManagement/componts/studentDetails.vue"
      //     ),
      //   meta: {
      //     // title: $t("menus.pureFourZeroOne")
      //     title: "学生详情",
      //     idCode: reCodesList.studentManagementCompontsStudentDetails
      //   }
      // },
      // {
      //   path: "/institution/baseAdd",
      //   name: "baseAdd",
      //   component: () => import("@/views/institution/baseAdd.vue"),
      //   meta: {
      //     title: "机构-新建机构",
      //     idCode: reCodesList.baseAdd
      //   }
      // },
      // {
      //   path: "/institution/baseEdit",
      //   name: "baseEdit",
      //   component: () => import("@/views/institution/baseEdit.vue"),
      //   meta: {
      //     title: "机构-机构编辑",
      //     idCode: reCodesList.baseEdit
      //   }
      // },
      // {
      //   path: "/institution/accounting",
      //   name: "accounting",
      //   component: () => import("@/views/institution/accounting.vue"),
      //   meta: {
      //     title: "机构-财务",
      //     idCode: reCodesList.accounting
      //   }
      // },
      // {
      //   path: "/institution/baseDetails",
      //   name: "baseDetails",
      //   component: () => import("@/views/institution/baseDetails.vue"),
      //   meta: {
      //     title: "基地管理-详情",
      //     idCode: reCodesList.baseDetails
      //   }
      // },
      // {
      //   path: "/accountNumber/components/teacherDetails",
      //   name: "teacherDetails",
      //   component: () =>
      //     import("@/views/accountNumber/components/teacherDetails.vue"),
      //   meta: {
      //     title: "讲师详情",
      //     idCode: reCodesList.teacherDetails
      //   }
      // },
      // {
      //   path: "/accountNumber/components/qualificationsFile",
      //   name: "qualificationsFile",
      //   component: () =>
      //     import("@/views/accountNumber/components/qualificationsFile.vue"),
      //   meta: {
      //     title: "资质文件",
      //     idCode: reCodesList.qualificationsFile
      //   }
      // },
      // {
      //   path: "/accountNumber/components/editInformation",
      //   name: "editInformation",
      //   component: () =>
      //     import("@/views/accountNumber/components/editInformation.vue"),
      //   meta: {
      //     title: "详情-编辑信息",
      //     idCode: reCodesList.editInformation
      //   }
      // },
      // {
      //   path: "/institution/orderDetails",
      //   name: "orderDetails",
      //   component: () => import("@/views/institution/orderDetails.vue"),
      //   meta: {
      //     title: "机构管理-账务",
      //     idCode: reCodesList.orderDetails
      //   }
      // },
      {
        path: "/institution/baseExamineDtl",
        name: "baseExamineDtl",
        component: () => import("@/views/institution/baseExamineDtl.vue"),
        meta: {
          title: "审核-详情",
          idCode: reCodesList.baseExamineDtl
        }
      },
      {
        path: "/institution/institutionExamineDtl",
        name: "institutionExamineDtl",
        component: () =>
          import("@/views/institution/institutionExamineDtl.vue"),
        meta: {
          title: "审核",
          idCode: reCodesList.institutionExamineDtl
        }
      },
      {
        path: "/course/orderManagement/orderDetails",
        name: "courseOrderDetails",
        component: () =>
          import("@/views/course/orderManagement/orderDetails.vue"),
        meta: {
          title: "订单管理-订单详情",
          idCode: reCodesList.courseOrderDetails
        }
      }
      // {
      //   path: "/welcome/homeEdit",
      //   name: "homeEdit",
      //   component: () => import("@/views/welcome/homeEdit.vue"),
      //   meta: {
      //     title: "编辑信息",
      //     idCode: reCodesList.homeEdit
      //   }
      // },
      // {
      //   path: "/welcome/changePassword",
      //   name: "changePassword",
      //   component: () => import("@/views/welcome/changePassword.vue"),
      //   meta: {
      //     title: "修改密码",
      //     idCode: reCodesList.changePassword
      //   }
      // }
    ]
  },
  // 下面是一个无layout菜单的例子（一个全屏空白页面），因为这种情况极少发生，所以只需要在前端配置即可（配置路径：src/router/modules/remaining.ts）
  {
    path: "/empty",
    name: "Empty",
    component: () => import("@/views/empty/index.vue"),
    meta: {
      title: $t("menus.pureEmpty"),
      showLink: false,
      rank: 103
    }
  },
  {
    path: "/account-settings",
    name: "AccountSettings",
    component: () => import("@/views/account-settings/index.vue"),
    meta: {
      title: $t("buttons.pureAccountSettings"),
      showLink: false,
      rank: 104
    }
  },
  {
    path: "/data/analysis/bi",
    name: "analysisBi",
    component: () => import("@/views/platform/components/analysisDetail.vue"),
    meta: {
      title: "数据分析详情",
      showLink: false,
      rank: 104
    }
  }
];
