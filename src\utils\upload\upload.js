import {
  getByIdentifier,
  initUpload,
  isupload,
  findFilePart,
  chunkUpload,
  merge
} from "./set_upload.js";
import { getFileMD5 } from "./md5";
import { ElLoading, ElMessage } from "element-plus";

/**
 * 验证文件类型是否允许上传
 * @param {File} file - 要上传的文件对象
 * @param {Array} allowedTypes - 允许的文件类型数组，如 ['image', 'pdf', 'excel']
 * @returns {Object} - 包含验证结果和错误消息的对象
 */
function validateFileType(file, allowedTypes = []) {
  // 检查文件对象是否有效
  if (!file || !file.name) {
    return {
      valid: false,
      message: "无效的文件对象"
    };
  }

  // 常见文件类型映射表
  const typeMap = {
    // 图片类型
    image: {
      mimeTypes: [
        "image/jpeg",
        "image/png",
        "image/gif",
        "image/bmp",
        "image/webp"
      ],
      extensions: ["jpg", "jpeg", "png", "gif", "bmp", "webp"]
    },
    // 文档类型
    pdf: {
      mimeTypes: ["application/pdf"],
      extensions: ["pdf"]
    },
    // PPT类型
    ppt: {
      mimeTypes: [
        "application/vnd.ms-powerpoint",
        "application/vnd.openxmlformats-officedocument.presentationml.presentation"
      ],
      extensions: ["ppt", "pptx"]
    },
    word: {
      mimeTypes: [
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
      ],
      extensions: ["doc", "docx"]
    },
    excel: {
      mimeTypes: [
        "application/vnd.ms-excel",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      ],
      extensions: ["xls", "xlsx"]
    },
    // 文本类型
    text: {
      mimeTypes: ["text/plain"],
      extensions: ["txt"]
    },
    // 视频类型
    video: {
      mimeTypes: ["video/mp4", "video/mpeg", "video/quicktime"],
      extensions: ["mp4", "mpeg", "mov"]
    }
  };

  // 获取文件扩展名
  const fileName = file.name;
  const lastDotIndex = fileName.lastIndexOf(".");
  // 如果没有扩展名，返回错误
  if (lastDotIndex === -1) {
    return {
      valid: false,
      message: "文件没有扩展名"
    };
  }
  const fileExtension = fileName.substring(lastDotIndex + 1).toLowerCase();

  // 如果没有指定允许的类型，则允许所有类型
  if (!allowedTypes || allowedTypes.length === 0) {
    return { valid: true };
  }

  // 汇总所有允许的MIME类型和扩展名
  let validMimeTypes = [];
  let validExtensions = [];

  allowedTypes.forEach(type => {
    if (typeMap[type]) {
      validMimeTypes = [...validMimeTypes, ...typeMap[type].mimeTypes];
      validExtensions = [...validExtensions, ...typeMap[type].extensions];
    }
  });

  // 如果找不到任何有效类型映射，可能是传入了无效的类型名称
  if (validMimeTypes.length === 0 && validExtensions.length === 0) {
    return {
      valid: false,
      message: `无效的文件类型限制: ${allowedTypes.join(", ")}`
    };
  }

  // 检查MIME类型 (如果文件有type属性)
  const isValidType =
    !file.type || file.type === "" || validMimeTypes.includes(file.type);

  // 检查扩展名
  const isValidExtension = validExtensions.includes(fileExtension);

  // 如果文件没有type属性或type为空，则只根据扩展名判断
  if (!file.type || file.type === "") {
    if (!isValidExtension) {
      return {
        valid: false,
        message: `不支持的文件类型。允许的类型: ${allowedTypes.join(", ")}`
      };
    }
    return { valid: true };
  }

  // 文件有type属性，同时验证type和扩展名
  if (!isValidType || !isValidExtension) {
    return {
      valid: false,
      message: `不支持的文件类型。允许的类型: ${allowedTypes.join(", ")}`
    };
  }

  return { valid: true };
}

/**
 * @param file 文件
 */

async function uploadFile(file, callback = () => {}, allowedTypes = []) {
  return new Promise((resolve, reject) => {
    // 验证文件类型
    const validation = validateFileType(file, allowedTypes);
    if (!validation.valid) {
      reject(validation);
      return;
    }

    // 获取文件md5
    getFileMD5(file, async (md5, chunkArr) => {
      // Bytes转kb
      let kb = (file.size / 1024).toFixed(2);
      // 转为number
      kb = Number(kb);
      const chunkSize = 1024 * 1024 * 5; // 分片大小（默认5M）
      let count = 0; //成功上传了几个分片
      let loadingtext = "";
      let size = file.size; // 文件大小
      let chunks = Math.ceil(size / chunkSize); // 分片总数
      let i = file.name.lastIndexOf(".");
      let suffix = i == -1 ? "" : file.name.substring(i + 1); // 文件名后缀
      let checkData = { identifier: md5 };
      // console.log("🌈-----checkData-----", checkData);

      // 更新进度的函数
      const updateProgress = (current, total) => {
        const percent = Math.min(Math.floor((current / total) * 100), 100);
        callback({
          percent,
          uploaded: current,
          total: total,
          size: kb,
          fileName: file.name
        });
      };
      // 初始进度
      updateProgress(0, size);

      const resById = await getByIdentifier(checkData);
      // console.log("resById:===== ", resById);
      let urldata;
      let resuploadData;
      if (resById && resById.code == 200 && resById.data) {
        // 文件已经存在，秒传
        updateProgress(size, size); // 设置为100%完成
        resolve(resById);
        urldata = resById.data;
        return callback({ ...resById, size: kb, percent: 100 });
      } else {
        let uploadFileVO = {
          fileIdentifier: md5,
          fileName: file.name,
          totalSize: size,
          chunkSize: chunkSize
        };
        const resupload = await initUpload(uploadFileVO);
        console.log("生成uploadId:===== ", resupload);
        if (resupload && resupload.code == 200) {
          resuploadData = resupload.data;
        } else {
          ElLoading.service().close();
          ElMessage.error(resupload.msg);
        }
      }
      if (size > chunkSize) {
        let Partobj = {
          uploadId: resuploadData.uploadId
          // objectKey: resuploadData.objectKey,
        };

        let exist = []; //已存在的分片
        //获取已上传的分片
        const resPart = await findFilePart(Partobj);
        console.log("🎁-----resPart-----", resPart);

        if (resPart && resPart.code == 200) {
          exist = resPart.data;
        }
        console.log("🐠-----chunkArr-----", chunkArr, chunkArr.length);

        // 计算已存在的分片大小总和
        let existingSize = 0;
        exist.forEach(item => {
          existingSize += chunkSize; // 假设每个已存在的分片都是完整的chunkSize
        });

        // 更新初始进度，考虑已存在的分片
        if (existingSize > 0) {
          updateProgress(existingSize, size);
        }
        // const loading  = ElLoading.service({
        //   lock: true,
        //   text: '',
        //   background: 'rgba(0, 0, 0, 0.7)',
        // })

        for (let t = 0; t < chunkArr.length; t++) {
          let uploadFilePartVO = new FormData(); //上传参数
          uploadFilePartVO.set("file", chunkArr[t].file); //分片文件
          uploadFilePartVO.set("uploadId", resuploadData.uploadId); //
          uploadFilePartVO.set("partNumber", t + 1); //
          uploadFilePartVO.set("partSize", chunkArr[t].currentSize); //

          let type = exist.some(item => item.partNumber == t + 1);
          if (!type) {
            let reschunkUpload = await chunkUpload(uploadFilePartVO);
            // console.log("🐬-----reschunkUpload-----", reschunkUpload);
            if (reschunkUpload.code === 200) {
              count++;
              let text =
                "上传" +
                ((count / Math.ceil(file.size / chunkSize)) * 100).toFixed(2) +
                "%";
              // loading.text = text;
              console.log("🐬-----loadingtext-----", text);
              // 计算当前上传的总大小
              const uploadedSize = count * chunkSize + existingSize;
              // 确保不超过文件总大小
              const actualUploadedSize = Math.min(uploadedSize, size);

              // 更新进度
              updateProgress(actualUploadedSize, size);
            } else {
              // loading.close();
              break;
            }
          }
        }
        console.log(
          "🍪-----count-----",
          count,
          Math.ceil(file.size / chunkSize)
        );

        if (count == Math.ceil(file.size / chunkSize) - exist.length) {
          let uploadIdVO = {
            uploadId: resuploadData.uploadId
          };
          let resmerge = await merge(uploadIdVO); ///
          console.log("🍪-----resmerge-----", resmerge);
          if (resmerge.code === 200) {
            console.log("🍭新的-----resmerge-----", resmerge);
            updateProgress(size, size);
            resolve(resmerge);
            // loading.close();
            return callback({ ...resmerge, percent: 100 });
          }
        }
      } else {
        // 不分片
        console.log("🌈-----不分片-----");
        let formData = new FormData();
        formData.append("fileMd5", md5);
        formData.append("name", `${file.name}`);
        formData.append("file", chunkArr[0].file);
        console.log("🌵-----formData-----", formData);

        let obj = {
          fileIdentifier: md5,
          fileName: file.name,
          totalSize: file.size,
          chunkSize: chunkSize
        };
        const { data, code, msg } = await initUpload(obj);
        console.log("🦄-----data,code-----", data, code, msg);
        if (code == 200) {
          // 更新进度到50%，表示准备工作完成
          updateProgress(size / 2, size);
          let obj2 = new FormData();

          obj2.append("uploadId", data.uploadId);
          obj2.append("file", chunkArr[0].file);

          console.log("🐠-----obj2-----", obj2);

          const res = await isupload(obj2);
          console.log("🦄-----res-----", res);
          if (res.code == 200) {
            // 完成上传，设置进度为100%
            updateProgress(size, size);
            resolve(res);
            return callback({ ...res, size: kb, percent: 100 });
          } else {
            reject(res);
          }
        } else {
          ElLoading.service().close();
          ElMessage.error(msg);
        }
      }
    });
  }).catch(err => {
    const error = err.message ? err : { message: err };
    callback({
      errMessage: err
    });
    return Promise.reject(error);
  });
}

/**
 * 文件上传大小限制
 * @param {File} file - 上传的文件对象
 * @param {number} limit - 限制的大小，单位为Mb
 * @returns {boolean} - 是否超出限制
 */
function isOverSizeLimit(file, limit) {
  // 检查文件是否存在
  if (!file) {
    return {
      valid: false,
      message: "未选择文件"
    };
  }
  // 上传的文件大小（MB)
  let limitSize = file.size / 1024 / 1024;

  // 如果没有指定允许的大小，则允许所有
  if (!limit) {
    return { valid: true };
  }
  // 检查文件大小
  if (limitSize > limit) {
    return {
      valid: false,
      message: `文件大小超过${limit}MB`
    };
  }

  // 文件大小符合要求
  return {
    valid: true
  };
}

export { isOverSizeLimit, uploadFile, validateFileType };
