<script setup>
import { ref, onMounted, onActivated, nextTick } from "vue";
import {
  platformFindAll,
  platformIsFreeze,
  platformRole
} from "@/api/platform.js";
import { requestTo } from "@/utils/http/tool";
import dayjs from "dayjs";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
import { View, Hide } from "@element-plus/icons-vue";
import { decrypt, encryption } from "@/utils/SM4.js";
defineOptions({
  name: "PlatformAccount"
});

const router = useRouter();

onActivated(() => {
  getPlatformFindAll();
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);
});

// 计算表格高度
const searchFormHeight = ref(0);
const tableHeight = ref("calc(100vh - 300px)");
const calculateTableHeight = async () => {
  await nextTick();
  const searchForm = document.querySelector(".con_search");
  if (searchForm) {
    searchFormHeight.value = searchForm.offsetHeight;
    // 动态计算表格高度，减去表单高度、页面其他元素高度和边距
    tableHeight.value = `calc(100vh - 300px - ${searchFormHeight.value}px)`;
  }
};

// 搜索表单
const form = ref({
  startTime: "", //时间
  endTime: "",
  name: "", //姓名
  phone: "", //手机号
  role: 0, //角色
  freeze: 0 //账号状态
});
// 表格数据
const tableData = ref([]);

const params = ref({
  page: 1,
  size: 15,
  sort: "createdAt,desc",
  totalElements: 0
});

//分页
const handleCurrentChange = e => {
  params.value.page = e;
  getPlatformFindAll();
};

const handleSizeChange = e => {
  params.value.size = e;
  getPlatformFindAll();
};

// 查询
const getPlatformFindAll = async () => {
  const paramsData = {
    page: params.value.page - 1,
    size: params.value.size,
    sort: params.value.sort
  };
  for (const paramsDataKey in form.value) {
    let isArray = Array.isArray(form.value[paramsDataKey]);
    if (isArray) {
      if (form.value[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    } else {
      if (form.value[paramsDataKey]) {
        // 仅对 phone 进行加密
        paramsData[paramsDataKey] = paramsData[paramsDataKey] =
          paramsDataKey === "phone"
            ? encryption(String(form.value[paramsDataKey]))
            : form.value[paramsDataKey];
      }
    }
  }
  if (paramsData.freeze && form.value.freeze === 1) {
    paramsData.freeze = false;
  } else if (paramsData.freeze && form.value.freeze === 2) {
    paramsData.freeze = true;
  }
  const [err, res] = await requestTo(platformFindAll(paramsData));
  if (res) {
    // console.log("🐬res------------------------------>", res);
    res?.content.forEach(item => {
      item.show_phone = false;
      // item.show_card = false;
      item.roles = item.roles?.map(item => item.name).join("、") || "--";
    });
    params.value.totalElements = res.totalElements;
    tableData.value = res.content;
    await nextTick();
    calculateTableHeight();
  }
  if (err) {
    console.log("err------------------------------>", err);
  }
};
const eye_phone = id => {
  const item = tableData.value.find(item => item.id === id);
  if (item) {
    item.type_phone = !item.type_phone;
  }
};
const value1 = ref([]);
// 选择时间
const timeChange = async value => {
  // console.log("🐬-----value-----", value);
  if (value) {
    form.value.startTime = new Date(value[0])?.getTime();
    // form.value.endTime = new Date(value[1])?.getTime();
    const endDate = new Date(value[1]);
    endDate.setHours(23, 59, 59, 999); // 关键修改
    form.value.endTime = endDate.getTime();
    await nextTick();
    calculateTableHeight();
  }
};

// 重置
const setData = () => {
  form.value = {};
  params.value.page = 1;
  value1.value = [];
  getPlatformFindAll();
};
//搜索
const searchData = () => {
  params.value.page = 1;
  getPlatformFindAll();
};
// 详情
const detailEvt = id => {
  router.push({ path: "/account/platform/account/detail", query: { id } });
};
// 是否冻结
const freezeEvt = (row, bool) => {
  let freezeText =
    bool === true ? "确定要冻结该账号吗？" : "确定要解冻该账号吗？";
  ElMessageBox.confirm(`${freezeText}`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(() => {
      isFreezeApi(row, bool);
    })
    .catch(() => {
      // ElMessage({
      //   type: 'info',
      //   message: 'Delete canceled',
      // })
    });
};
const isFreezeApi = async (row, bool) => {
  const params = {
    id: row.id,
    freeze: bool
  };
  const operateLog = {
    operateLogType: "ACCOUNT_MANAGEMENT",
    operateType:
      bool === true
        ? `冻结了“${row.name}”的平台账号`
        : `解冻了“${row.name}”的平台账号`
  };
  const { code } = await platformIsFreeze(params, operateLog);
  if (code === 200) {
    ElMessage({
      type: "success",
      message: bool === true ? "冻结成功" : "解冻成功"
    });
    getPlatformFindAll();
  } else {
    ElMessage({
      type: "error",
      message: bool === true ? "冻结失败" : "解冻失败"
    });
  }
};
// 角色选项
const roleOptions = ref([
  {
    name: "全部",
    id: 0
  }
]);
// 账号状态选项
const stateOptions = ref([
  {
    name: "全部",
    id: 0
  },
  {
    name: "正常",
    id: 1
  },
  {
    name: "冻结",
    id: 2
  }
]);
// 查询角色
const getPlatformRole = async () => {
  const [err, res] = await requestTo(platformRole());
  if (res) {
    let res1 = res.map(item => {
      return {
        name: item.name,
        id: item.id
      };
    });
    roleOptions.value = roleOptions.value.concat(res1);
  }
  if (err) {
    console.log("err------------------------------>", err);
  }
};
// 清除数据
const clearEvt = val => {
  if (val === "name") {
    form.value.name = "";
  } else if (val === "time") {
    form.value.startTime = "";
    form.value.endTime = "";
  } else if (val === "phone") {
    form.value.phone = "";
  }
  // params.value.page = 1;
  getPlatformFindAll();
};
const getCourseStatus = freeze => {
  return freeze === true ? "冻结" : "正常";
};

const getCourseColor = freeze => {
  return freeze === true ? "#f56c6c" : "";
};
onMounted(() => {
  getPlatformFindAll();
  getPlatformRole();
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);
});
</script>

<template>
  <div class="containers">
    <!-- <div class="con_top">
      <div class="titles">平台账号</div>
    </div> -->
    <div class="search">
      <div class="con_search">
        <el-form :model="form" :inline="true">
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="value1"
              type="daterange"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              @change="timeChange"
              @clear="clearEvt('time')"
            />
          </el-form-item>

          <el-form-item label="姓名">
            <el-input
              v-model.trim="form.name"
              placeholder="请输入"
              clearable
              style="width: 180px"
              @clear="clearEvt('name')"
            />
          </el-form-item>
          <el-form-item label="手机号">
            <el-input
              v-model.trim="form.phone"
              placeholder="请输入"
              clearable
              style="width: 180px"
              @clear="clearEvt('phone')"
            />
          </el-form-item>
          <el-form-item label="角色">
            <el-select
              v-model="form.role"
              style="width: 120px"
              placeholder="请选择角色"
              value-key="id"
            >
              <el-option
                v-for="item in roleOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="账号状态">
            <el-select
              v-model="form.freeze"
              style="width: 120px"
              placeholder="请选择账号状态"
              value-key="id"
            >
              <el-option
                v-for="item in stateOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label=" ">
            <div class="flex">
              <el-button type="primary" @click="searchData">搜索</el-button>
              <el-button @click="setData">重置</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <div class="main">
      <div class="btns">
        <el-button
          v-code="['606']"
          type="primary"
          class="create-btn"
          @click="
            router.push({
              path: '/account/platform/account/addAdd',
              query: { type: 'create' }
            })
          "
        >
          新建账号
        </el-button>
      </div>
      <el-scrollbar :style="{ height: tableHeight }">
        <div class="con_table">
          <el-table
            :data="tableData"
            table-layout="fixed"
            :header-cell-style="{
              backgroundColor: '#fafafa',
              color: '#565353'
            }"
            :max-height="tableHeight"
          >
            <el-table-column
              prop="id"
              label="账号ID"
              min-width="100"
              align="left"
            >
              <template #default="scope">
                {{ scope.row.id || "--" }}
              </template>
            </el-table-column>
            <el-table-column prop="name" label="姓名" align="left">
              <template #default="scope">
                <div>
                  {{ scope.row.name || "--" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="phone"
              label="手机号"
              align="left"
              min-width="110"
            >
              <template #default="scope">
                <div class="eye_style">
                  {{
                    scope.row.phone
                      ? scope.row.type_phone
                        ? decrypt(scope.row.phoneCt)
                        : scope.row.phone
                      : "--"
                  }}
                  <div
                    v-if="scope.row.phone"
                    class="eye"
                    @click="eye_phone(scope.row.id, scope.row.phoneCt)"
                  >
                    <el-icon v-if="!scope.row.type_phone"><Hide /></el-icon>
                    <el-icon v-else><View /></el-icon>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="account" label="账号" align="left">
              <template #default="scope">
                <div>
                  {{ scope.row.account || "--" }}
                </div>
              </template>
            </el-table-column>

            <el-table-column prop="roles" label="角色" align="left">
              <template #default="scope">
                <div>
                  {{ scope.row.roles || "--" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              width="200px"
              prop="createdAt"
              label="创建时间"
              align="left"
            >
              <template #default="{ row }">
                <div>
                  {{
                    row.createdAt
                      ? dayjs(row.createdAt).format("YYYY-MM-DD HH:mm:ss")
                      : "--"
                  }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="freeze" label="账号状态" align="left">
              <template #default="scope">
                <div :style="{ color: getCourseColor(scope.row.freeze) }">
                  {{ getCourseStatus(scope.row.freeze) }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="address"
              fixed="right"
              label="操作"
              align="left"
              width="200px"
            >
              <template #default="{ row }">
                <div class="option">
                  <div
                    v-code="['605']"
                    class="btnse"
                    @click="detailEvt(row.id)"
                  >
                    详情
                  </div>
                  <div class="btnse">
                    <!-- <div class="freeze" v-if="row.freeze===1">冻结</div>
              <div class="nofreeze" v-else>解冻</div> -->
                    <div
                      v-if="row.freeze === true"
                      class="nofreeze"
                      @click="freezeEvt(row, false)"
                    >
                      解冻
                    </div>
                    <div v-else class="freeze" @click="freezeEvt(row, true)">
                      冻结
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-scrollbar>

      <div class="con_pagination">
        <!-- 分页 -->
        <el-pagination
          v-model:current-page="params.page"
          v-model:page-size="params.size"
          class="pagination"
          background
          :page-sizes="[15, 30, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="params.totalElements"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
// .scrollbar {
//   // padding-top: 20px;
//   // padding: 20px;
//   // height: calc(100vh - 181px);
//   height: calc(100vh - 379px);

//   background-color: #fff;
// }
// :deep(.el-table .cell) {
//   padding: 0;
// }
.containers {
  box-sizing: border-box;
  // width: calc(100% - 48px);
  // height: calc(100vh - 48px);
  // padding: 24px;
  // overflow-y: auto;
  // background: #fff;

  .con_top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: fit-content;
    margin-bottom: 24px;

    .titles {
      font-size: 20px;
      font-weight: bold;
      color: #606266;
    }
  }

  .search {
    padding: 20px 20px 2px 20px;
    background-color: #fff;
    margin-bottom: 20px;
    .con_search {
      display: flex;
      align-items: center;
      width: 100%;
      height: fit-content;
      // .input_width {
      //   width: 200px;
      // }
    }
  }
  .main {
    background: #fff;
    padding: 20px;
    box-sizing: border-box;
    height: 100%;
    .btns {
      width: 100%;
      display: flex;
      justify-content: flex-end;
    }
    .create-btn {
      // width: 100%;
      display: flex;
      justify-content: flex-end;
      margin-bottom: 20px;
    }
  }
  .con_table {
    // width: calc(100% - 25px);
    // height: 500px;

    // margin-left: 25px;
    // overflow-y: auto;
  }

  .con_pagination {
    display: flex;
    justify-content: flex-end;
    width: 100%;
    margin-top: 20px;
  }

  .option {
    display: flex;

    .btnse {
      display: flex;
      margin-right: 16px;
      color: #409eff;
      cursor: pointer;

      .nofreeze {
        color: #f56c6c;
        cursor: pointer;
      }
    }
  }
  .eye_style {
    display: flex;
    align-items: center;
    // justify-content: center;
    .eye {
      margin-left: 20px;
      cursor: pointer;
      :hover {
        color: #409eff;
      }
    }
  }
}
:deep(.el-form--inline .el-form-item) {
  margin-right: 32px !important;
}
</style>
