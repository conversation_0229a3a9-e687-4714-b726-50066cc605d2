<script setup>
import orderDetails from "@/views/institution/components/orderDetailsComponent.vue";
import { onMounted, ref } from "vue";
import { useRoute, useRouter } from "vue-router";

const router = useRouter();
const route = useRoute();
const form = ref({
  subOrdersDetails: []
});

const richFlag = ref(false);
const subOrdersId = ref(null);
const ordersData = ref([]);
const ordersDataJson = ref("");
onMounted(() => {
  if (route.query.data) {
    ordersData.value = JSON.parse(route.query.data);
    ordersDataJson.value = route.query.data;
  }
  if (sessionStorage.getItem("orderDetailsData")) {
    ordersDataJson.value = JSON.parse(
      sessionStorage.getItem("orderDetailsData")
    );
    // sessionStorage.removeItem("orderDetailsData");
  }
  console.log("🌵-----ordersDataJson.value-----", ordersDataJson.value);
  subOrdersId.value = ordersDataJson.value.id;
  // getData();
  richFlag.value = true;
});
</script>

<template>
  <div class="main">
    <orderDetails
      v-if="richFlag"
      :adminId="subOrdersId"
      :ordersDataJson="ordersDataJson"
      :styleTyle="true"
    />
  </div>
</template>

<style lang="scss" scoped>
// :deep(.el-descriptions__cell) {
//   width: 34%;
// }
.main {
  padding: 20px;
  background: #fff;
}
</style>
