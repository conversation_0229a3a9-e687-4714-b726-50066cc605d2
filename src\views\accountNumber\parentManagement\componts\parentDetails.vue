<script setup>
import { ref, reactive, onMounted } from "vue";
import { Hide, View } from "@element-plus/icons-vue";
import dayjs from "dayjs";
import { useRole } from "./hook.jsx";

defineOptions({
  name: "ParentManagementCompontsParentDetails"
});

const {
  indexList,
  pagination,
  dataList,
  columns,
  iconteyp,
  imgAdd,
  type,
  openDialog,
  handleCurrentChange,
  handleSizeChange,
  loadingTable,
  tableHeader,
  isFreezeApi
} = useRole();
</script>

<template>
  <div>
    <div class="common bottom">
      <!-- <div class="title">家长管理 \ 详情</div> -->
      <div class="puretable">
        <el-descriptions
          class="margin-top"
          style="width: 100%"
          :label-width="'15%'"
          :column="2"
          border
        >
          <el-descriptions-item
            v-for="(item, index) in tableHeader"
            :key="index"
            label-align="center"
            label-class-name="my-label"
          >
            <template #label>
              <div class="cell-item">{{ item.label }}</div>
            </template>
            <el-form-item
              :prop="item.prop"
              :inline-message="item.check"
              style="margin-bottom: 0"
              :show-message="true"
              error-placement="right"
            >
              <span v-if="item.type === 'text'">
                {{ indexList[item.prop] || "--" }}
              </span>
              <span v-else-if="item.type === 'createdAt'">
                {{
                  indexList[item.prop]
                    ? dayjs(indexList[item.prop]).format("YYYY-MM-DD HH:mm:ss")
                    : "--"
                }}
              </span>
              <span v-else-if="item.type === 'phone'">
                <div v-if="iconteyp === true">
                  {{ type(indexList[item.prop1]) }}
                </div>
                <div v-else>{{ indexList[item.prop] || "--" }}</div>
              </span>
              <span v-if="indexList[item.prop1] && item.isEye" class="icon">
                <el-icon
                  v-if="iconteyp === true"
                  style="cursor: pointer"
                  @click="imgAdd()"
                  ><View /></el-icon>
                <el-icon v-else style="cursor: pointer" @click="imgAdd()"><Hide /></el-icon>
              </span>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
        <div v-if="indexList.freeze === true" class="tabtn">
          <div class="butt">本账号已冻结</div>
          <el-button type="danger" @click="isFreezeApi">解冻</el-button>
        </div>
      </div>
    </div>
    <div class="common">
      <div class="title">关联子女</div>
      <div class="puretable">
        <pure-table
          ref="tableRef"
          row-key="id"
          adaptive
          :adaptiveConfig="{ offsetBottom: 62 }"
          align-whole="left"
          table-layout="auto"
          showOverflowTooltip
          :loading="loadingTable"
          :data="dataList"
          :columns="columns"
          :pagination="{ ...pagination }"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #operation="{ row }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              @click="openDialog('详情', row)"
            >
              详情
            </el-button>
          </template>
        </pure-table>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.common {
  width: 100%;
  height: 100%;
  padding: 20px;
  background-color: #fff;
  .title {
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: bold;
    color: #606266;
  }
  .puretable {
    display: flex;
    .tabtn {
      display: flex;
      justify-content: space-around;
      align-items: end;
      height: 100px;
      width: 240px;
      .butt {
        padding: 4px 15px;
        align-items: center;
        display: inline-flex;
        color: #f56c6c;
      }
    }
  }
}

.bottom {
  margin-bottom: 20px;
}

.iconteyp {
  display: flex;
  justify-content: space-between;
  width: 120px;
}

.icon {
  display: flex;
  align-items: center;
  margin-left: 10px;
}
:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  background: #e1f5ff;
}
:deep(.el-descriptions__cell) {
  width: 34%;
}
:deep(.el-popper.is-dark) {
  max-width: 300px !important;
  word-break: break-all !important;
}
</style>
