import axios from "axios";

// 联级地区公用方法
const valKey = val => {
  let list = [];
  val.map(item => {
    list.push({
      value: item.id,
      label: item.name,
      children: item.children ? val<PERSON>ey(item.children) : []
    });
  });
  return list;
};

const areaList = async () => {
  let jsonData = [];
  try {
    const response = await axios.get(
      "https://s.gostaredu.boran-tech.com/gostaredu-cdn/regions-data/20250811-01/regions-data.json"
    );
    if (response.data <= 0) return;
    jsonData = valKey(response.data);
  } catch (err) {
    console.error("请求 JSON 失败：", err);
  }
  return jsonData;
};

export default areaList;
