import { onMounted, reactive, ref, defineComponent } from "vue";
import { Edit, Check, View } from "@element-plus/icons-vue";
import {
  bannerFindAll,
  bannerSave,
  bannerDelete,
  bannerMove,
  bannerUpdate,
  organizationFindAll,
  courseFindAll,
  organizationFindByNoPage
} from "@/api/apiPlatform.js";
import { requestTo } from "@/utils/http/tool";
import ImgBlurHash from "@/components/ImgBlurHash";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  uploadFile,
  validateFileType,
  isOverSizeLimit
} from "@/utils/upload/upload.js";
import dayjs from "dayjs";
import { ImageThumbnail } from "@/utils/imageProxy";

export function useRole() {
  const ruleFormRef = ref();
  const loadingTable = ref(false);
  const moveUp = ref();
  const moveDown = ref();
  const buttonLoading = ref(false); // 按钮加载状态

  const dataList = ref([]);
  const pagination = {
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true
  };

  const dialogFormVisible = ref(false);
  const addressLinkDom = ref(false);
  const form = reactive({
    title: "",
    id: "",
    url: "",
    organizationName: "",
    organizationId: "",
    linkUrl: "",
    fileIdentifier: "",
    courseId: ""
  });
  const gridData = ref([]);
  const optionsData = ref([]);

  const removeEmptyValues = obj => {
    return Object.fromEntries(
      Object.entries(obj).filter(
        ([key, value]) =>
          value !== "" && !(Array.isArray(value) && value.length === 0)
      )
    );
  };

  // 轮播图-开始 列表数据
  const startAdd = async val => {
    const paramsArg = {
      page: val.page || 0,
      size: val.size || 10,
      sort: "sortOrder,asc"
    };
    let aee = removeEmptyValues(paramsArg);

    const [err, res] = await requestTo(bannerFindAll(aee));
    if (res) {
      dataList.value = res?.content;
      pagination.total = res?.totalElements;

      if (res.number === 0) {
        moveUp.value = res?.content[0]?.id;
      }
      if (res.number + 1 === res.totalPages) {
        moveDown.value = res?.content[res.content.length - 1]?.id;
      }

      // moveUp.value = res?.content[0]?.id;
      // moveDown.value = res?.content[res.content.length - 1]?.id;
    }
    if (err) {
    }
  };

  // 获取 机构名
  const obtainInstitution = async () => {
    const [err, result] = await requestTo(organizationFindByNoPage());
    if (result) {
      optionsData.value = result;
    } else {
      ElMessage.error(err);
    }
  };

  // 查询 课程名
  const institutionCourseA = async val => {
    const paramsData = { organizationId: val.organizationId };
    let api = removeEmptyValues(paramsData);
    console.log("🐬-----api-----", api);
    const [err, result] = await requestTo(courseFindAll(api));
    if (result) {
      if (result?.content.length > 0) {
        gridData.value = result?.content;
      } else {
        // ElMessage({
        //   type: "error",
        //   message: "该机构暂无课程名"
        // });
        gridData.value = [];
        form.courseName = "";
      }
    } else {
      ElMessage.error(err);
    }
  };

  // 轮播图-新增条目
  async function openDialog(text) {
    addressLinkDom.value = true;
    obtainInstitution();
    console.log("🎉-----text-----", text);
    if (ruleFormRef.value) ruleFormRef.value.resetFields();
    console.log("🍪-----form-----", form);
    form.title = text || "";
    form.id = "";
    form.url = "";
    form.organizationId = "";
    form.courseId = "";
    form.linkUrl = "";
  }
  // 轮播图-编辑 打开弹出窗口
  const inputAdd = async (text, val) => {
    console.log("🍪-----val-----", val);
    addressLinkDom.value = true;
    obtainInstitution();
    if (val.organizationId !== "") {
      institutionCourseA(val);
    }

    form.title = text || "";
    form.id = val?.id;
    form.url = val?.file?.url || "";
    form.organizationId = val?.organizationId || "";
    form.courseId = val?.courseId || "";
    form.linkUrl = val?.linkUrl || "";
  };
  // 弹框 课程名
  const focusCourseId = val => {
    // if (val.organizationName === "") {
    //   ElMessage({
    //     type: "error",
    //     message: "请先选择机构"
    //   });
    // }
  };
  // 弹框 搜索 课程名
  const changeSelect = val => {
    console.log("🐬-----val--搜索 课程名---", val);
    let paramsA = {
      organizationId: val.organizationId
    };
    institutionCourseA(paramsA);
  };
  // 弹框 机构名发生变化
  const changeOrganization = val => {
    console.log("🌵-----val--机构名发生变化---", val);
    let isOrganizationId = optionsData.value.filter(
      it => it.id === val.organizationId
    );
    form.organizationId = isOrganizationId[0]?.id || "";
    let paramsA = {
      organizationId: isOrganizationId[0]?.id
    };
    if (!paramsA.organizationId) return;
    console.log("🎉-----paramsA-----", paramsA);
    form.courseName = "";
    form.courseId = "";
    gridData.value = [];
    institutionCourseA(paramsA);
  };

  const clearOrganization = () => {
    form.courseName = "";
    form.courseId = "";
    gridData.value = [];
  };

  // 轮播图-编辑 取消或关闭弹出窗口
  const beforeCloseDom = async ruleRef => {
    ruleFormRef.value.resetFields();
    console.log("🍪-----form--轮播图-编辑 取消或关闭弹出窗口---", form);
    form.title = "";
    form.id = "";
    form.url = "";
    form.organizationId = "";
    form.courseId = "";
    form.linkUrl = "";
    addressLinkDom.value = false;
    buttonLoading.value = false;
  };
  // 轮播图-图片上传
  const fileUpload = async (file, row) => {
    // const validation = validateFileType(file, ['image']);
    // if (!validation.valid) {
    //   console.log('🌳-----validation-----', validation);
    //   return  ElMessage({
    //     type: "success",
    //     message: "请上传图片,不支持该文件类型。允许的类型:jpg,jpeg,png,gif,bmp,webp"
    //   });;
    // }
    let isSize = isOverSizeLimit(file, 10);
    if (isSize.valid) {
      try {
        const { code, data } = await uploadFile(file, () => {}, ["image"]);
        if (code === 200) {
          form.url = data.url;
          form.fileIdentifier = data?.fileIdentifier;
          if (ruleFormRef.value) {
            ruleFormRef.value.validateField("url", valid => {
              if (!valid) {
                console.log("验证失败");
              } else {
                console.log("验证成功");
              }
            });
          }
        }
      } catch (error) {
        ElMessage({
          type: "error",
          message: error.message
        });
      }
    } else {
      ElMessage.error(isSize.message);
    }
  };
  // 轮播图编辑 确认按钮
  async function confirmAdd(formEl, val) {
    console.log("🐳-----formEl,val-----", formEl, val); //  修改了编号为“13”的轮播图
    if (!formEl) return;
    await formEl.validate(async (valid, fields) => {
      if (valid) {
        buttonLoading.value = true;
        const operateLog = {
          operateLogType: "PLATFORM_SETTINGS",
          operateType:
            form.title === "编辑"
              ? "修改了编号“" + val?.id + "”的轮播图"
              : "新增了一条轮播图"
        };

        let isCourseId =
          gridData.value.filter(it => it.id == val.courseId) || "";

        if (form.title === "编辑") {
          const paramsArg = {
            id: val?.id,
            courseId: isCourseId[0]?.id || "",
            linkUrl: val?.linkUrl || "",
            fileIdentifier: val?.fileIdentifier || "",
            organizationId: val?.organizationId || ""
          };
          let aee = removeEmptyValues(paramsArg);
          const res = await bannerUpdate(aee, operateLog);
          if (res.code === 200) {
            const paramsArg = {
              page: pagination.currentPage - 1,
              size: pagination.pageSize || 10
            };
            startAdd(paramsArg);
            ElMessage({
              type: "success",
              message: "轮播图编辑成功"
            });
            addressLinkDom.value = false;
            buttonLoading.value = false;
          } else {
            ElMessage({
              type: "error",
              message: "轮播图编辑失败"
            });
            buttonLoading.value = false;
          }
        } else {
          const paramsArg = {
            courseId: isCourseId[0]?.id || "",
            linkUrl: val?.linkUrl || "",
            fileIdentifier: val?.fileIdentifier || "",
            organizationId: val?.organizationId || ""
          };
          let api = removeEmptyValues(paramsArg);
          console.log("🍪-----api---新增--", api);
          const res = await bannerSave(api, operateLog);
          if (res.code === 200) {
            const paramsArg = {
              page: pagination.currentPage - 1,
              size: pagination.pageSize || 10
            };
            startAdd(paramsArg);
            ElMessage({
              type: "success",
              message: "新增成功"
            });
            addressLinkDom.value = false;
            buttonLoading.value = false;
          } else {
            ElMessage({
              type: "error",
              message: "新增失败"
            });
            buttonLoading.value = false;
          }
        }
      } else {
        console.log("error submit!", fields);
      }
    });
  }

  // 移动
  const handleMenu = async (text, val) => {
    const operateLog = {
      operateLogType: "PLATFORM_SETTINGS",
      operateType: text + "了编号为“" + val?.id + "”的轮播图"
      // operatorTarget: form.value.name,
    };
    const params = {
      id: val.id,
      moveType: text === "上移" ? "UP" : "DOWN" // UP:上移，DOWN：下移
    };
    const { code } = await bannerMove(params, operateLog);
    if (code === 200) {
      const paramsArg = {
        page: pagination.currentPage - 1,
        size: pagination.pageSize || 10
      };
      startAdd(paramsArg);
      ElMessage({
        type: "success",
        message: text === "上移" ? "上移成功" : "下移成功"
      });
    } else {
      ElMessage({
        type: "error",
        message: text === "上移" ? "上移失败" : "下移失败"
      });
    }
  };

  // 轮播图 删除
  const isFreezeApi = async val => {
    const operateLog = {
      operateLogType: "PLATFORM_SETTINGS",
      operateType: "删除了编号为“" + val?.id + "”的轮播图"
      // operatorTarget: form.value.name,
    };
    const paramsArg = { id: val.id };
    const res = await bannerDelete(paramsArg, operateLog);
    if (res.code === 200) {
      const paramsArg = {
        page: pagination.currentPage - 1,
        size: pagination.pageSize || 10
      };
      startAdd(paramsArg);
      ElMessage({
        type: "success",
        message: "删除成功"
      });
    } else {
      ElMessage({
        type: "error",
        message: "删除失败"
      });
    }
  };
  // 轮播图 删除 二次弹框
  async function handleDelete(text, val) {
    let freezeText = "确定要删除吗？";
    ElMessageBox.confirm(`${freezeText}`, "确定删除", {
      confirmButtonText: "确定",
      cancelButtonText: "取消"
    })
      .then(() => {
        isFreezeApi(val);
      })
      .catch(() => {});
  }

  // 轮播图 每页多少条
  async function handleSizeChange(val) {
    pagination.pageSize = val;
    const paramsArg = {
      page: 0,
      size: val
    };
    startAdd(paramsArg);
  }
  // 轮播图 前往页数
  async function handleCurrentChange(val) {
    pagination.currentPage = val;
    const paramsArg = {
      page: val - 1,
      size: pagination.pageSize
    };
    startAdd(paramsArg);
  }

  const columns = [
    {
      label: "编号",
      prop: "id",
      minWidth: 60,
      cellRenderer: ({ row }) => (
        <div style="display: flex; justify-content: left; width: 60px;">
          {row.id || "--"}
        </div>
      )
    },
    {
      label: "图片",
      prop: "imgadd",
      width: 220,
      cellRenderer: ({ row }) => (
        // <div style="display: flex; justify-content: left; min-width: 220px;">
        //   <div style="width: 120px; height: 100px; display: flex; justify-content: center;  align-items: center;">
        //     <ImgBlurHash
        //       style=" margin-right: 10px; object-fit: cover; height: 100px;"
        //       v-preview={{ url: row?.file?.url, type: "image" }}
        //       src={ImageThumbnail(row?.file?.url, '100px')}
        //     />
        //   </div>
        // </div>
        <el-image
          preview-teleported
          loading="lazy"
          src={ImageThumbnail(row?.file?.url, "200x")}
          preview-src-list={[row?.file?.url]}
          hide-on-click-modal={true}
          fit="cover"
          class="w-[100px] h-[100px]"
        />
      )
    },
    {
      label: "地址链接",
      prop: "ip",
      width: 200,
      cellRenderer: ({ row }) => (
        <div style="display: flex; justify-content: left">
          <div style="width: 200px; margin-right: 10px;">
            {/* <div>{row.linkUrl? (row.linkUrl): (<div style="line-height: 100px;">--</div>)}</div> */}
            <div>{row.linkUrl ? row.linkUrl : <div>--</div>}</div>
          </div>
        </div>
      )
    },

    {
      label: "机构",
      prop: "organizationName",
      width: 300,
      formatter: ({ organizationName }) => {
        return organizationName || "--";
      }
    },
    {
      label: "课程",
      prop: "courseName",
      width: 300,
      formatter: ({ courseName }) => {
        return courseName || "--";
      }
    },
    {
      label: "创建时间",
      width: 180,
      prop: "createdAt",
      cellRenderer: ({ row }) => (
        <div style="display: flex; justify-content: left; min-width: 180px;">
          {row.createdAt
            ? dayjs(row.createdAt).format("YYYY-MM-DD HH:mm:ss")
            : "--"}
        </div>
      )
    },
    {
      label: "操作",
      fixed: "right",
      width: 120,
      slot: "operation"
    }
  ];
  const rules = reactive({
    url: [{ required: true, message: "请上传图片", trigger: "blur" }],
    linkUrl: [{ required: false, message: "请输地址链接", trigger: "blur" }],
    organizationId: [
      { required: false, message: "请选择机构", trigger: "change" }
    ],
    courseId: [{ required: false, message: "请选择课程", trigger: "change" }]
  });

  onMounted(async () => {
    const paramsArg = {
      page: 0,
      size: 10
    };
    startAdd(paramsArg);
    // obtainInstitution();
  });

  return {
    moveUp,
    moveDown,
    loadingTable,
    columns,
    dataList,
    pagination,
    openDialog,
    handleDelete,
    handleMenu,
    handleSizeChange,
    handleCurrentChange,
    startAdd,

    dialogFormVisible,
    form,
    ruleFormRef,
    rules,
    gridData,

    optionsData,
    changeSelect,
    changeOrganization,
    clearOrganization,

    addressLinkDom,
    inputAdd,
    beforeCloseDom,
    fileUpload,
    confirmAdd,
    focusCourseId,
    buttonLoading
  };
}
