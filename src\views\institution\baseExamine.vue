<script setup>
import { onMounted, ref } from "vue";
import { formatTime } from "@/utils/index";
import { courseFindId, courseFindAll, isFreeze } from "@/api/institution";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
onMounted(() => {
  // courseTypeFindApi();
  // getTableList();
});
const router = useRouter();
// 搜索表单
const form = ref({
  startTime: "",
  endTime: "",
  name: "",
  organizationName: "",
  courseTypeName: ""
});
// 表格数据
const tableData = ref([]);
const params = ref({
  page: 1,
  size: 15,
  sort: "createdAt,desc",
  totalElements: 0
});
const courseTypeFindApi = async () => {
  const params = {
    depth: 1
  };
  // let [err, res] = await requestTo(courseTypeFind(params));
  // console.log("🐠res------------------------------>", res);
  // let res1 = res.map(it => {
  //   return {
  //     ...it,
  //     label: it.name,
  //     value: it.id
  //   };
  // });
  // courseTypeoptions.value = courseTypeoptions.value.concat(res1);
  // console.log(
  //   "🌵courseTypeoptions.value------------------------------>",
  //   courseTypeoptions.value
  // );
};
// 获取列表信息
const getListLoading = ref(false);
const getTableList = async item => {
  if (getListLoading.value) return;
  // return;
  getListLoading.value = true;
  let paramsData = {
    page: params.value.page - 1,
    size: params.value.size,
    sort: params.value.sort
  };
  for (const paramsDataKey in form.value) {
    let isArray = Array.isArray(form.value[paramsDataKey]);
    if (isArray) {
      if (form.value[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    } else {
      if (form.value[paramsDataKey]) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    }
  }
  console.log("🍧-----paramsData---搜索条件--", paramsData);
  const { code, data, msg } = await courseFindAll(paramsData);
  console.log("🎁-----result-----", data);
  if (data) {
    tableData.value = data?.content;

    params.value.totalElements = data.totalElements;
  }
  getListLoading.value = false;
};
//分页
const handleCurrentChange = e => {
  params.value.page = e;
  getTableList();
};
const handleSizeChange = val => {
  params.value.page = 1;
  params.value.size = val;
  getTableList();
};
// 编辑
const edit = item => {
  // router.push({ path: "/institution/baseEdit", query: { data: JSON.stringify(item) } });
  router.push({
    path: "/institution/baseEdit",
    query: { id: item.id }
  });
};
// 账务
const getId = item => {
  router.push({
    path: "/institution/accounting",
    query: { name: item.name, id: item.id }
  });
};
//搜索
const searchData = () => {
  return;
  params.value.page = 1;
  getTableList();
};
// 重置
const setData = () => {
  form.value = {};
  params.value.page = 1;
  value1.value = [];
};
// 选择时间
const timeChange = value => {
  if (!value || value.length !== 2) return;

  // 开始时间（默认 00:00:00）
  form.value.startTime = new Date(value[0]).getTime();

  // 结束时间（设置为 23:59:59.999）
  const endDate = new Date(value[1]);
  endDate.setHours(23, 59, 59, 999); // 关键修改
  form.value.endTime = endDate.getTime();
};

const value1 = ref([]);
// 前往创建
const goSet = () => {
  router.push({
    path: "/institution/baseAdd",
    query: { title: "jsMang", id: 1 }
  });
};
// 冻结/解冻
const getButtonText = isPub => {
  return isPub === true ? "解冻" : "冻结";
};
const Freeze = async row => {
  console.log("🍭-----row-----", row);
  const isFreezing = !row.freeze;
  const confirmText = isFreezing
    ? "你确定要冻结该机构吗?"
    : "你确定要解冻该机构吗?";
  const confirmTitle = isFreezing ? "确认冻结" : "确认解冻";
  const successMessage = isFreezing ? "已冻结" : "已解冻";

  try {
    await ElMessageBox.confirm(confirmText, confirmTitle, {
      confirmButtonText: "确认",
      cancelButtonText: "取消"
      // type: "warning"
    });

    const params = {
      id: row.id,
      freeze: isFreezing
    };
    const operateLog = {
      operateLogType: "ORGANIZATIONAL_MANAGEMENT",
      operateType: isFreezing ? "冻结了" : "解冻了",
      operatorTarget: form.value.name
    };
    const { code, msg } = await isFreeze(params, operateLog);

    if (code === 200) {
      ElMessage({
        message: successMessage,
        type: "success"
      });

      getTableList();
    } else {
      ElMessage.error(msg);
    }
  } catch (error) {
    // console.log("操作取消");
  }
};
const defaultTime = [
  new Date(2024, 4, 1, 0, 0, 0),
  new Date(2024, 4, 1, 23, 59, 59)
];
const courseTypeoptions = ref([
  {
    value: "",
    label: "全部"
  }
]);
</script>

<template>
  <div>
    <div class="common">
      <div class="con_search">
        <el-form :model="form" :inline="true">
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="value1"
              type="daterange"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              :default-time="defaultTime"
              @change="timeChange"
            />
          </el-form-item>
          <el-form-item label="机构名">
            <el-input v-model="form.ordersId" placeholder="请输入" clearable />
          </el-form-item>
          <el-form-item label="审批类型">
            <el-select
              v-model="form.orderStatus"
              style="width: 120px"
              placeholder="请选择"
              :empty-values="[null, undefined]"
              :value-on-clear="null"
            >
              <el-option
                v-for="item in courseTypeoptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="审批状态">
            <el-select
              v-model="form.orderStatus"
              style="width: 120px"
              placeholder="请选择"
              :empty-values="[null, undefined]"
              :value-on-clear="null"
            >
              <el-option
                v-for="item in courseTypeoptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label=" ">
            <div class="flex">
              <el-button type="primary" @click="searchData">搜索</el-button>
              <el-button @click="setData">重置</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <div class="containers">
      <el-scrollbar class="scrollbar">
        <div class="con_table">
          <el-table
            :data="tableData"
            max-height="calc(100vh - 280px)"
            :header-cell-style="{
              backgroundColor: '#fafafa',
              color: '#565353'
            }"
          >
            <el-table-column prop="ordersId" label="机构名" align="left">
              <template #default="scope">
                <div>
                  {{ scope.row.ordersId || "--" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column width="200px" prop="createdAt" label="申请时间">
              <template #default="scope">
                <div>
                  {{
                    formatTime(scope.row?.createdAt, "YYYY-MM-DD HH:mm:ss") ||
                    "暂无"
                  }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="courseName" label="审批类型" min-width="120">
              <template #default="scope">
                <el-text class="w-300px mb-2" truncated>
                  {{ scope.row.courseName || "--" }}
                </el-text>
              </template>
            </el-table-column>
            <el-table-column prop="orderStatus" label="审批状态" align="left">
              <template #default="scope">
                <div>
                  {{ orderStatusTypeoptions[scope.row.orderStatus] || "--" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              fixed="right"
              width="140px"
              label="操作"
              align="center"
            >
              <template #default="scope">
                <div class="box">
                  <div
                    v-if="scope.row.orderStatus"
                    class="btnse"
                    @click="getId(scope.row.id, '审核')"
                  >
                    审核
                  </div>
                  <div
                    v-else
                    class="btnse"
                    @click="getId(scope.row.id, '详情')"
                  >
                    详情
                    <div>
                      <!-- <div
                v-if="scope.row.orderStatus === 'REFUNDING'"
                class="btnse"
                @click="Freeze(scope.row)"
              >
                确认退单
              </div> -->
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-scrollbar>

      <div class="con_pagination">
        <!-- 分页 -->
        <el-pagination
          v-model:current-page="params.page"
          v-model:page-size="params.size"
          class="pagination"
          background
          :page-sizes="[15, 30, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="params.totalElements"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.scrollbar {
  // padding-top: 20px;
  // padding: 20px;
  // height: calc(100vh - 181px);
  height: calc(100vh - 329px);

  background-color: #fff;
}
:deep(.el-table .cell) {
  padding: 0;
}
.common {
  // width: 100%;
  // height: 100%;
  padding: 20px 20px 2px 20px;
  background-color: #fff;
  margin-bottom: 20px;
  .con_search {
    display: flex;
    align-items: center;
    width: 100%;
    height: fit-content;
  }
}

.containers {
  box-sizing: border-box;
  // width: calc(100% - 48px);
  height: 100%;
  padding: 20px;
  background: #fff;

  .con_top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: fit-content;
    margin-bottom: 24px;

    .titles {
      font-size: 20px;
      font-weight: bold;
      color: #606266;
    }
  }

  .con_search {
    display: flex;
    align-items: center;
    width: 100%;
    height: fit-content;
    margin-bottom: 24px;
    .el-form--inline .el-form-item {
      margin-right: 0;
    }
  }

  .con_table {
    // width: calc(100% - 25px);
    // margin-bottom: 24px;
    // margin-left: 25px;
    .button {
      display: flex;
      // justify-content: space-around;
      .btnse {
        color: #409eff;
        cursor: pointer;
        padding: 0 5px;
      }
    }
  }

  .con_pagination {
    display: flex;
    justify-content: flex-end;
    width: 100%;
    margin-top: 20px;
  }
}
</style>
