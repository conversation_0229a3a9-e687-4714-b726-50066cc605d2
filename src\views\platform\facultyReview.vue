<script setup>
import { useFacultyReview } from "./utils/facultyReviewHook";

const {
  state,
  columns,
  buttons,
  plusPageInstance,
  beforeSearchSubmit,
  getList
} = useFacultyReview();
</script>

<template>
  <div class="out-container">
    <div class="plus-table">
      <PlusPage
        ref="plusPageInstance"
        class="plus-table-page"
        :request="getList"
        :columns="columns"
        :params="state.query"
        :before-search-submit="beforeSearchSubmit"
        :default-page-info="{ page: 1, pageSize: 20 }"
        :page-info-map="{ page: 'page', pageSize: 'size' }"
        :search="{
          labelWidth: '80px',
          colProps: { span: 4 },
          hasUnfold: false
        }"
        :table="{
          actionBar: { buttons, width: 140 },
          titleBar: false,
          adaptive: true,
          maxHeight: '66vh',
          border: false
        }"
      >
        <template #table-title>
          <el-row class="button-row" />
        </template>
      </PlusPage>
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep(.plus-table-column__header) {
  color: rgb(48, 49, 51);
}
:deep(.el-card) {
  box-shadow: none;
  border: none;
  border-radius: 0;
}
:deep(.el-form-item__content .el-icon) {
  display: none;
}
</style>
