<script setup>
import { ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { updatePassword } from "@/api/institution.js";
import { decrypt, encryption } from "@/utils/SM4.js";
import { useUserStoreHook } from "@/store/modules/user";

const router = useRouter();
const route = useRoute();

// 表单
const form = ref({
  oldPassword: "",
  newPassword: "",
  confirmNewPassword: ""
});
// 提交
const formRef = ref(null);
const formData = ref([
  {
    label: "旧密码",
    type: "input",
    prop: "oldPassword",
    check: true,
    placeholder: "请输入旧密码",
    width: "400px"
  },
  {
    label: "新密码",
    type: "input",
    prop: "newPassword",
    check: true,
    placeholder: "请输入新密码",
    width: "400px"
  },
  {
    label: "确认新密码",
    type: "input",
    prop: "confirmNewPassword",
    check: true,
    placeholder: "请输入确认新密码",
    width: "400px"
  }
]);
// 自定义校验方法
const validatePassword = (rule, value, callback) => {
  const val = form.value.newPassword === value;
  const allowedCharsRegex =
    /^(?:(?=.*[a-z])(?=.*[A-Z])(?=.*\d)|(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%^&*.])|(?=.*[a-z])(?=.*\d)(?=.*[!@#$%^&*.])|(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*.]))[A-Za-z\d!@#$%^&*.]{6,12}$/;
  if (value === "") {
    callback(new Error("确认新密码不能为空"));
  } else if (!allowedCharsRegex.test(value)) {
    callback(
      new Error(
        "请设置6位及以上12位以内包含大写字母、小写字母、数字、特殊符号中至少三种组合的密码"
      )
    );
  } else if (!val) {
    callback(new Error("新密码与确认密码不一致"));
  } else {
    callback();
  }
};
const validateOldPassword = (rule, value, callback) => {
  const allowedCharsRegex = /^[a-z0-9!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]*$/i;
  if (value === "") {
    callback(new Error("旧密码不能为空"));
  } else if (!allowedCharsRegex.test(value)) {
    callback(new Error("密码只能包含数字、字母和普通符号"));
  } else {
    callback();
  }
};
const validateNewPassword = (rule, value, callback) => {
  const allowedCharsRegex =
    /^(?:(?=.*[a-z])(?=.*[A-Z])(?=.*\d)|(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%^&*.])|(?=.*[a-z])(?=.*\d)(?=.*[!@#$%^&*.])|(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*.]))[A-Za-z\d!@#$%^&*.]{6,12}$/;
  if (value === "") {
    callback(new Error("确认新密码不能为空"));
  } else if (!allowedCharsRegex.test(value)) {
    callback(
      new Error(
        "请设置6位及以上12位以内包含大写字母、小写字母、数字、特殊符号中至少三种组合的密码"
      )
    );
  } else {
    callback();
  }
};
// 校验规则
const rules = ref({
  oldPassword: [
    { required: true, validator: validateOldPassword, trigger: "blur" }
    // { min: 2, max: 10, message: "姓名长度应在2-10个字符之间", trigger: "blur" }
  ],
  newPassword: [
    { required: true, validator: validateNewPassword, trigger: "blur" }
  ],
  confirmNewPassword: [
    { required: true, validator: validatePassword, trigger: "blur" }
  ]
});
const submitForm = () => {
  formRef.value.validate(valid => {
    if (valid) {
      console.log("表单数据:", form.value);
      submit();
    } else {
      console.log("表单校验失败");
    }
  });
};

// 获取列表信息
const getListLoading = ref(false);

const submit = async data => {
  if (getListLoading.value) {
    return;
  }
  getListLoading.value = true;
  let paramsData = {
    oldPassword: encryption(form.value.oldPassword),
    newPassword: encryption(form.value.newPassword)
  };
  const operateLog = {
    operateLogType: "ACCOUNT_MANAGEMENT",
    operateType: "修改了密码"
    // operatorTarget: form.value.name,
  };
  // return;
  try {
    const { code, data, msg } = await updatePassword(paramsData, operateLog);
    if (code == 200) {
      ElMessage({
        message: "修改密码成功",
        type: "success"
      });
      /** 退出登录 */
      useUserStoreHook().logOut();
    } else {
      ElMessage({
        message: msg,
        type: "error"
      });
    }
  } catch (error) {
    console.error("修改密码失败：", error);
  }
  getListLoading.value = false;
};
// 取消
const cancelForm = () => {
  router.push({
    path: "/welcome"
  });
};
</script>

<template>
  <div class="containers">
    <div class="table_content">
      <el-form ref="formRef" :rules="rules" :model="form">
        <el-descriptions title="" :column="1" border>
          <el-descriptions-item
            v-for="(item, index) in formData"
            :key="index"
            label-align="center"
            label-class-name="my-label"
          >
            <template #label>
              <span v-if="item.check" class="star">*</span>{{ item.label }}
            </template>
            <el-form-item
              :prop="item.prop"
              :inline-message="item.check"
              style="margin-bottom: 0"
              :show-message="true"
              error-placement="right"
            >
              <!-- input输入 -->
              <template v-if="item.type === 'input'">
                <el-input
                  v-model.trim="form[item.prop]"
                  :placeholder="item.placeholder"
                  :style="{ width: item.width }"
                  type="password"
                  show-password
                />
              </template>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
        <div class="table_bottom">
          <el-button type="default" @click="cancelForm"> 取消 </el-button>
          <el-button
            type="primary"
            :loading="getListLoading"
            @click="submitForm"
          >
            保存
          </el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.containers {
  box-sizing: border-box;
  width: calc(100% - 48px);
  height: 100%;
  //   padding: 24px;
  background: #f0f2f5;

  .table_top {
    box-sizing: border-box;
    padding: 20px;
    background-color: #fff;
  }

  .table_content {
    box-sizing: border-box;
    padding: 20px;
    margin-top: 20px;
    background-color: rgb(255 255 255);

    .star {
      margin-right: 3px;
      color: red;
    }

    .el-descriptions-item {
      display: flex;
      align-items: center;
    }

    .Vacode {
      margin-left: 20px;
    }

    .isQR {
      margin-right: 240px;
    }

    .codeQR {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }

  .table_bottom {
    display: flex;
    justify-content: flex-end;
    margin-top: 30px;
  }
}

:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  background: #e1f5ff;
  //   width: 240px;
}

:deep(.my-label) {
  background: #e1f5ff !important;
}

:deep(.my-content) {
  background: var(--el-color-danger-light-9);
}
</style>
