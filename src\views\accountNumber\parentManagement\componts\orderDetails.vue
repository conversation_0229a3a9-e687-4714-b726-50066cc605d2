<script setup>
import { ref, reactive, onMounted } from "vue";
import { ordersGetOrderDetails, ordersRefund } from "@/api/parentManage.js";
import { requestTo } from "@/utils/http/tool";
import { useRouter, useRoute } from "vue-router";
import { ElMessageBox, ElMessage } from "element-plus";
import { Edit, Hide, View } from "@element-plus/icons-vue";
import { decrypt, encryption } from "@/utils/SM4.js";
import dayjs from "dayjs";
const router = useRouter();
const route = useRoute();

// import pdfOne from "@/assets/a.pdf";
// // 附带文件查看
// const currentPdf = ref(null);
// const filesAdd = val => {
//   console.log("🦄-----pdfOne-----", pdfOne);
//   currentPdf.value = pdfOne;
// };

// 退单点击
const open = (text, val) => {
  ElMessageBox.confirm("确定要退单吗？", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      isOpen(text, val);
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "取消"
      });
    });
};
const isOpen = async (text, val) => {
  console.log("🍪-----text, val-----", text, val);
  const operateLog = {
    operateLogType: "PARENT_MANAGEMENT",
    operateType: "退了订单号" + val.ordersId
    // operatorTarget: form.value.name,
  };
  const paramsArg = {
    id: val.ordersId
  };
  const { code } = await ordersRefund(paramsArg, operateLog);
  if (code === 200) {
    ElMessage({
      type: "success",
      message: "已退单"
    });
    router.back();
  } else {
    ElMessage({
      type: "error",
      message: "退单失败"
    });
  }
};
// 查看关联课程
const openDialog = () => {
  router.push({
    path: "/course/management/current/details",
    query: { id: 1, text: "course" }
  });
};

const indexList = ref({});
const startAdd = async val => {
  const paramsArg = { id: val.id };
  const [err, res] = await requestTo(ordersGetOrderDetails(paramsArg));
  if (res) {
    indexList.value = res;
    console.log("🐠-----indexList.value-----", indexList.value);
  }
  if (err) {
  }
};

// 机构客服热线
const iconserv = ref(false);
function servAdd(val) {
  iconserv.value = !iconserv.value;
}
// 管理员电话
const iconorgan = ref(false);
function organAdd(val) {
  iconorgan.value = !iconorgan.value;
}
// 购买人电话
const iconbuyer = ref(false);
function buyerAdd(val) {
  iconbuyer.value = !iconbuyer.value;
}

const dataTime = val => {
  if (val) {
    return dayjs(val).format("YYYY-MM-DD HH:mm:ss");
  } else {
    return "--";
  }
};
function idNumberAdd1(val) {
  if (val) {
    let idNumber = decrypt(val);
    return hide_tm(idNumber, "phone");
  } else {
    return "--";
  }
}
function idNumberAdd0(val) {
  if (val) {
    let idNumber = decrypt(val);
    return idNumber;
  } else {
    return "--";
  }
}

onMounted(() => {
  let teId = route.query;
  startAdd(teId);
});

const isTime = val => {
  if (val === "UNPAID" || val === "PAID") {
    // 待支付  已支付
    return "--";
  } else if (val === "REFUND") {
    // 已退款
    return dataTime(indexList.value?.chargebackTime);
  } else if (val === "CANCELLED") {
    // 已取消
    return dataTime(indexList.value?.updatedAt);
  } else {
    // 完成时间
    return dataTime(indexList.value?.finishTime);
  }
};

const isComplete = val => {
  if (val === "REFUND") {
    // 已退款
    return "退单时间";
  } else if (val === "CANCELLED") {
    // 已取消
    return "取消时间";
  } else {
    return "完成时间";
  }
};

const stateAdd = val => {
  if (val === "UNPAID") {
    return "未支付";
  } else if (val === "PAY") {
    return "支付中";
  } else if (val === "PAID") {
    return "已支付";
  } else if (val === "REFUNDING") {
    return "退款中";
  } else if (val === "REFUND") {
    return "已退款";
  } else if (val === "PARTIALLY_REFUNDED") {
    return "部分退款";
  } else if (val === "CANCELLED") {
    return "已取消";
  } else if (val === "COMPLETED") {
    return "已完成";
  } else if (val === "CLOSED") {
    return "已关闭";
  } else if (val === "ERROR") {
    return "异常";
  } else {
    return "--";
  }
};

const hide_tm = (val, key) => {
  if (val == undefined) return;
  if (key == "idNumber") {
    return (
      val.substring(0, 1) +
      "*".repeat(3) +
      val.substring(4, 12) +
      "*".repeat(5) +
      val.substring(17)
    );
  } else if (key == "phone") {
    return val.replace(/^(\d{3})(\d{4})(\d{4})$/, "$1****$3");
  } else if (key == "name") {
    const surname = val.charAt(0); // 获取第一个字符作为姓氏
    const nameLength = val.length - 1; // 剩余的部分作为名字的长度
    const maskedName = surname + "*".repeat(nameLength); // 构造脱敏后的姓名
    return maskedName;
  }
};
</script>

<template>
  <div>
    <div class="common bottom">
      <div class="title">家长管理 \ 订单管理 \ 详情</div>
      <div class="puretable">
        <el-descriptions :column="2" border label-class-name="my-label">
          <el-descriptions-item label="订单ID">
            {{ indexList?.orderNo || "--" }}
          </el-descriptions-item>
          <el-descriptions-item label="是否团单">
            {{ indexList?.groupOrder === "ORDINARY" ? "是" : "否" }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ dataTime(indexList?.createdAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="订单状态">
            {{ stateAdd(indexList?.orderStatus) || "--" }}
          </el-descriptions-item>
          <el-descriptions-item label="付款时间">
            {{
              indexList?.orderStatus === "UNPAID"
                ? "--"
                : dataTime(indexList?.payTime)
            }}
          </el-descriptions-item>
          <el-descriptions-item :label="isComplete(indexList?.orderStatus)">
            {{ isTime(indexList?.orderStatus) || "--" }}
          </el-descriptions-item>
          <el-descriptions-item label="课程名">
            {{ indexList?.courseName || "--" }}
          </el-descriptions-item>
          <el-descriptions-item label="课程期号">
            {{ indexList?.termNumber || "--" }}
          </el-descriptions-item>
          <el-descriptions-item label="机构">
            {{ indexList?.organizationName || "--" }}
          </el-descriptions-item>
          <el-descriptions-item label="机构客服热线">
            {{ indexList?.organizationServicePhone || "--" }}
            <!-- <div class="iconteyp">
              <div v-if="iconserv === true">{{ indexList?.organizationServicePhone || "--" }}</div>
              <div v-else>{{ hide_tm(indexList?.organizationServicePhone, "phone") || "--" }}</div>
              <div v-if="indexList?.organizationServicePhone">
                <el-icon v-if="iconserv === true" style="margin-left: 24px; margin-top: 4px" @click="servAdd()"><View /></el-icon>
                <el-icon v-else style="margin-left: 24px; margin-top: 4px" @click="servAdd()"><Hide /></el-icon>
              </div>
            </div> -->
          </el-descriptions-item>
          <el-descriptions-item label="机构管理员">
            {{ indexList?.organizationManager || "--" }}
          </el-descriptions-item>
          <el-descriptions-item label="管理员电话">
            <div class="iconteyp">
              <div v-if="iconorgan === true">
                {{ idNumberAdd0(indexList?.organizationManagerPhone) }}
              </div>
              <div v-else>
                {{ idNumberAdd1(indexList?.organizationManagerPhone) }}
              </div>
              <div v-if="indexList?.organizationManagerPhone">
                <el-icon
                  v-if="iconorgan === true"
                  style="margin-left: 24px; margin-top: 4px"
                  @click="organAdd()"
                >
                  <View />
                </el-icon>
                <el-icon
                  v-else
                  style="margin-left: 24px; margin-top: 4px"
                  @click="organAdd()"
                >
                  <Hide />
                </el-icon>
              </div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="购买人">
            {{ indexList?.buyer || "--" }}
          </el-descriptions-item>
          <el-descriptions-item label="购买人电话">
            <div class="iconteyp">
              <div v-if="iconbuyer === true">
                {{ idNumberAdd0(indexList?.buyerPhone) }}
              </div>
              <div v-else>{{ idNumberAdd1(indexList?.buyerPhone) }}</div>
              <div v-if="indexList?.buyerPhone">
                <el-icon
                  v-if="iconbuyer === true"
                  style="margin-left: 24px; margin-top: 4px"
                  @click="buyerAdd()"
                >
                  <View />
                </el-icon>
                <el-icon
                  v-else
                  style="margin-left: 24px; margin-top: 4px"
                  @click="buyerAdd()"
                >
                  <Hide />
                </el-icon>
              </div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="学生">
            {{ indexList?.studentName || "--" }}
          </el-descriptions-item>
          <el-descriptions-item label="购买规格">
            {{ indexList?.specification || "--" }}
          </el-descriptions-item>
          <el-descriptions-item label="购买价格" span="2">
            {{ indexList?.price || "--" }}
          </el-descriptions-item>
          <el-descriptions-item label="订单附带文件" span="2">
            <div v-if="indexList.files" class="files">
              <div
                v-for="(it, index) in indexList?.files"
                :key="index"
                v-preview="{
                  url: it?.uploadFile?.url,
                  type: it?.uploadFile?.uploadType,
                  name: it?.uploadFile?.fileName
                }"
              >
                {{ it?.uploadFile.fileName }}
              </div>
            </div>
            <div v-else>{{ "--" }}</div>
            <!-- <span v-for="(it, index) in data" :key="index" class="files" @click="filesAdd(it)" >{{ it.name }}</span> -->
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <!-- <el-dialog v-model="currentPdf" width="100%" center>
        <iframe
          :src="currentPdf"
          :style="{ width: '100%', height: '70vh', display: 'block' }"
        />
      </el-dialog> -->

      <div class="footer">
        <div class="button coler" @click="openDialog()">查看关联课程</div>
        <div
          v-if="
            indexList?.orderStatus === 'PAID' ||
            indexList?.orderStatus === 'COMPLETED'
          "
          class="button"
          @click="open('退单', indexList)"
        >
          退单
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.common {
  width: 100%;
  height: 100%;
  padding: 20px;
  background-color: #fff;
  .title {
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: bold;
    color: #606266;
  }
  .puretable {
    margin-left: 25px;
    margin-right: 25px;
  }

  .footer {
    display: flex;
    justify-content: space-between;
    margin: 290px 25px 0 25px;

    .button {
      // width: 100px;
      height: 40px;
      padding: 0 20px;
      font-size: 16px;
      line-height: 40px;
      color: rgb(255 255 255);
      text-align: center;
      cursor: pointer;
      background-color: rgb(121.3 187.1 255);
      border: solid 1px rgb(121.3 187.1 255);
      border-radius: 8px;
      // text-align: left;
    }

    .coler {
      background-color: rgb(230 162 60);
      border: solid 1px rgb(230 162 60);
    }
  }
}

.bottom {
  margin-bottom: 20px;
}
.files {
  margin-right: 10px;
  color: #409eff;
  cursor: pointer;
}

.search {
  display: flex;
  justify-content: space-between;

  .button {
    // width: 320px;
    display: flex;
    justify-content: right;

    .button_A {
      width: 80px;
      height: 30px;
      font-size: 16px;
      line-height: 30px;
      color: rgb(121.3 187.1 255);
      text-align: center;
      cursor: pointer;
      border: solid 1px rgb(121.3 187.1 255);
      border-radius: 8px;
    }
  }
}

.iconteyp {
  display: flex;
  justify-content: space-between;
  width: 140px;
}

:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  background: #e1f5ff;
}
</style>
