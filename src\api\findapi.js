import { http } from "@/utils/http";

/** 分页查询 */
export const discoverFindAll = params => {
  return http.request(
    "get",
    "/platform/discover/findAll",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 查询详情
export const discoverFindById = params => {
  return http.request(
    "get",
    "/platform/discover/findById",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 删除
export const discoverDelete = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/discover/delete",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ACCOUNT_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
// 新增
export const discoverAdd = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/discover/add",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ACCOUNT_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};

// 修改
export const discoverUpdate = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/discover/update",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ACCOUNT_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
