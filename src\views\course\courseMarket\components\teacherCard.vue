<script lang="js" setup>
import { ref, onMounted } from "vue";
import router from "@/router/index";
import { ElLoading, ElMessage, ElMessageBox } from "element-plus";
const props = defineProps({
  nameFather: {
    type: String,
    default: ""
  },
  nameChild: {
    type: String,
    default: ""
  },
  tableData: {
    type: Array,
    default: () => []
  }
  //   label: {
  //     type: String,
  //     default: ""
  //   }
});
// 本条详情
const detailList = name => {
  if (name === "机构发布") {
    router.push({
      path: "/course/market/detailInstitution",
      query: { type: "institution" }
      // query: { periodId: props.id, ordersId: result.ordersId, type: 'institution' }
    });
  } else if (name === "师资发布") {
    router.push({
      path: "/course/market/detailFaculty",
      query: { type: "faculty" }
      // query: { periodId: props.id, ordersId: result.ordersId, type: 'faculty' }
    });
  }
};
// 课程详情
const courseDetail = () => {
  router.push({
    path: "/course/market/currentDetails",
    query: { type: "currentDetails" }
    // query: { periodId: props.id, ordersId: result.ordersId }
  });
};
// 招募详情
const recruitmentDemand = () => {
  router.push({
    path: "/course/market/detailRecruit",
    query: { type: "recruit" }
  });
};
// 取消发布弹窗
// const cancelPublish = () => {
//   ElMessageBox.confirm("是否取消发布？", "提示", {
//     confirmButtonText: "确定",
//     cancelButtonText: "取消",
//     type: "warning"
//   })
//     .then(() => {
//       console.log("取消发布");
//     })
//     .catch(() => {
//       console.log("取消");
//     });
// };
// 申请合作弹窗
// const applyCooperation = () => {
//   ElMessageBox.confirm("是否申请合作？", "提示", {
//     confirmButtonText: "确定",
//     cancelButtonText: "取消"
//   })
//     .then(() => {
//       console.log("申请合作");
//     })
//     .catch(() => {
//       console.log("取消");
//     });
// };
// 取消申请弹窗
// const cancelApply = () => {
//   ElMessageBox.confirm("是否取消申请？", "提示", {
//     confirmButtonText: "确定",
//     cancelButtonText: "取消",
//     type: "warning"
//   })
//     .then(() => {
//       console.log("取消申请");
//     })
//     .catch(() => {
//       console.log("取消");
//     });
// };
</script>

<template>
  <div class="teacher-card-list">
    <div
      v-for="(item, index) in tableData"
      :key="index"
      class="teacher-card-item"
    >
      <div class="teacher-card-header">
        <div>{{ item.name }}</div>
        <!-- <div
          v-if="props.nameFather === '师资发布' && props.nameChild === '全部'"
        >
          <el-button type="primary" @click="applyCooperation">
            申请合作
          </el-button>
        </div> -->
        <!-- <div
          v-if="props.nameFather === '机构发布' && props.nameChild === '已发布'"
        >
          <el-button type="primary" @click="cancelPublish">取消发布</el-button>
        </div> -->
        <!-- <div
          v-if="props.nameFather === '师资发布' && props.nameChild === '已申请'"
        >
          <el-button type="primary" @click="cancelApply">取消申请</el-button>
        </div> -->
      </div>
      <div class="font_color">
        <span>开课时间：</span><span>{{ item.time }}</span>
      </div>
      <div class="font_color">
        <span>课程类型：</span><span>{{ item.type }}</span>
      </div>
      <div v-if="props.nameFather !== '机构发布'" class="font_color">
        <span>招募需求：</span><span>{{ item.demand }}</span>
      </div>
      <div class="tag-list">
        <el-tag
          v-for="(status, index1) in item.status"
          :key="index1"
          type="primary"
        >
          {{ status }}
        </el-tag>
      </div>
      <div class="teacher-card-footer">
        <div>
          <span>{{ item.nameProme }}</span>
          <span class="font_blue" @click="detailList(nameFather)">详情&nbsp;></span>
        </div>
        <div class="teacher-card-btn">
          <div>
            <el-button type="primary" @click="courseDetail">课程详情</el-button>
          </div>
          <div v-if="props.nameFather !== '机构发布'">
            <el-button type="primary" @click="recruitmentDemand">
              招募需求
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.teacher-card-list {
  display: flex;
  flex-wrap: wrap !important;
  gap: 16px;
}
.teacher-card-item {
  flex: 0 0 calc(50% - 10px);
  box-sizing: border-box;
  background: #fff;
  border-radius: 8px;
  padding: 5px 16px;
  margin-bottom: 20px;
  min-width: 500px;
  border: 1px solid #e4e7ed;

  div {
    margin-bottom: 5px;
    &:last-child {
      margin-bottom: 0;
    }
  }
}
.teacher-card-header {
  height: 40px;
  display: flex;
  justify-content: space-between;
  line-height: 40px;
  div:nth-of-type(1) {
    font-size: 18px;
    font-weight: 500;
  }
}
.font_color {
  font-size: 14px;
  color: #464646;
}
.tag-list {
  margin: 15px 0 !important;
  :deep(.el-tag) {
    margin-left: 15px;
    &:first-child {
      margin-left: 0;
    }
  }
}
.teacher-card-footer {
  display: flex;
  justify-content: space-between;
  height: 50px;
  border-top: 1px solid #e4e7ed;
  font-size: 16px;
  line-height: 50px;
  .teacher-card-btn {
    display: flex;
    gap: 10px;
  }
  div:nth-of-type(1) {
    .font_blue {
      color: rgb(3, 158, 219);
      cursor: pointer;
      margin-left: 10px;
    }
  }
}
</style>
