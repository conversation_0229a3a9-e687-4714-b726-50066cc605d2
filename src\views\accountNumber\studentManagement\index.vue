<script setup>
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
defineOptions({
  name: "StudentManagement"
});

const router = useRouter();

import { useRole } from "./utils/hook.jsx";
const {
  form,
  pagination,
  dataList,
  columns,
  onSearch,
  setData,
  // handleInput,
  // toAdd,
  openDialog,
  // isFreezeApi,
  handleCurrentChange,
  handleSizeChange,
  loadingTable
} = useRole();
const defaultTime = [
  new Date(2024, 4, 1, 0, 0, 0),
  new Date(2024, 4, 1, 23, 59, 59)
];
</script>

<template>
  <div>
    <div class="common bottom">
      <!-- <div class="title">学生管理</div> -->
      <div class="search">
        <el-form
          :inline="true"
          :model="form"
          class="demo-form-inline"
          label-width="auto"
        >
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="form.time"
              type="daterange"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              :default-time="defaultTime"
            />
          </el-form-item>
          <el-form-item label="学生名字">
            <el-input
              v-model.trim="form.name"
              placeholder="请输入学生名字"
              clearable
            />
          </el-form-item>
          <el-form-item label="学校名称">
            <el-input
              v-model.trim="form.school"
              placeholder="请输入学校名称"
              clearable
            />
          </el-form-item>
          <el-form-item label="">
            <div class="button">
              <el-button type="primary" @click="onSearch(form)">搜索</el-button>
              <el-button @click="setData(form)">重置</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="common">
      <div class="puretable">
        <pure-table
          ref="tableRef"
          row-key="id"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="left"
          table-layout="auto"
          showOverflowTooltip
          :data="dataList"
          :columns="columns"
          :pagination="{ ...pagination }"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #operation="{ row }">
            <el-button
              v-code="['614']"
              class="reset-margin"
              link
              type="primary"
              @click="openDialog('详情', row)"
            >
              详情
            </el-button>
          </template>
        </pure-table>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.common {
  box-sizing: border-box;
  padding: 20px 20px 2px;
  background-color: #fff;
  .title {
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: bold;
    color: #606266;
  }
  .search {
    display: flex;
    justify-content: space-between;
    // margin-bottom: 20px;
    // margin-left: 12px;
    .button {
      // width: 320px;
      display: flex;
      justify-content: right;
    }
  }
  // .puretable {
  //   margin-left: 25px;
  // }
}
.bottom {
  margin-bottom: 20px;
  padding-bottom: 2px;
}

:deep(.el-popper.is-dark) {
  max-width: 500px !important;
  word-break: break-all !important;
}
</style>
