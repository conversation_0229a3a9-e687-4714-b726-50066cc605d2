import { Edit, Check } from "@element-plus/icons-vue";
import { fileDeleteById } from "@/api/files";
import ImgBlurHash from "@/components/ImgBlurHash";
import { message } from "@/utils/message";
import dayjs from "dayjs";
import { onMounted, reactive, ref, onActivated, watch } from "vue";
import { uploadFile } from "@/utils/upload/upload.js";
import {
  courseTypeSave,
  courseTypeMove,
  courseTypeDelete,
  courseTypeFindAll,
  courseTypeUpdate,
  courseTypeFindByParentId
} from "@/api/courseClassify.js";
import { findAllCourseType } from "@/api/course.js";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox } from "element-plus";
import { ImageThumbnail } from "@/utils/imageProxy";
import log from "@/assets/home/<USER>";

export function useRole() {
  const columns = [
    {
      label: "编号",
      prop: "id",
      minWidth: 190,
      formatter: ({ id }) => {
        return id || "--";
      }
    },
    {
      label: "图片",
      prop: "imgadd",
      width: 220,
      cellRenderer: ({ row }) => (
        <el-image
          preview-teleported
          loading="lazy"
          src={row?.file?.url ? ImageThumbnail(row.file.url, "200x") : log}
          preview-src-list={row?.file?.url ? [row.file.url] : []}
          hide-on-click-modal={true}
          fit="cover"
          class="w-[100px] h-[100px]"
        />
      )
    },
    {
      label: "分类名",
      prop: "name",
      minWidth: 190,
      formatter: ({ name }) => {
        return name || "--";
      }
    },
    {
      label: "创建时间",
      minWidth: 180,
      prop: "createdAt",
      formatter: ({ createdAt }) => {
        return createdAt
          ? dayjs(createdAt).format("YYYY-MM-DD HH:mm:ss")
          : "--";
      }
    },
    {
      label: "操作",
      fixed: "right",
      width: 120,
      slot: "operation"
    }
  ];

  // 标记是否为子页面
  const isSubPage = ref(false);
  // 添加标志位，防止重复请求
  const isInitialized = ref(false);

  const rules = reactive({
    // 根据是否为子页面动态设置图片验证规则
    url: [
      {
        validator: (rule, value, callback) => {
          if (!isSubPage.value && !value) {
            callback(new Error("请上传图片"));
          } else {
            callback();
          }
        },
        trigger: "blur"
      }
    ],
    number: [{ required: true, message: "编号", trigger: "blur" }],
    textarea2: [{ required: true, message: "请输分类名", trigger: "blur" }]
  });

  const buttonLoading = ref(false); // 按钮加载状态
  const ruleFormRef = ref();
  const addressLinkDom = ref(false);
  const loadingTable = ref(false);
  const dataList = ref([]);
  const pagination = {
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true
  };
  const one = ref();
  const tow = ref();
  const dataArr = ref();
  const listData = ref([]);
  const form = reactive({
    title: "",
    textarea2: "",
    number: "",
    url: "",
    icon: ""
  });
  const objData = ref({});

  //分类管理-图片上传
  const fileUpload = async (file, row) => {
    try {
      // 检查文件大小，10MB = 10 * 1024 * 1024 字节
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (file.size > maxSize) {
        ElMessage.error("上传图片大小不能超过10MB！");
        return false; // 阻止上传
      }

      const { code, data } = await uploadFile(file, () => {}, ["image"]);
      if (code === 200) {
        form.url = data.url;
        form.icon = data?.fileIdentifier;
        if (ruleFormRef.value) {
          ruleFormRef.value.validateField("url", valid => {
            if (!valid) {
              console.log("验证失败");
            } else {
              console.log("验证成功");
            }
          });
        }
      }
    } catch (error) {
      ElMessage({
        type: "error",
        message: error.message
      });
    }
  };
  const removeEmptyValues = obj => {
    return Object.fromEntries(
      Object.entries(obj).filter(
        ([key, value]) =>
          value !== "" && !(Array.isArray(value) && value.length === 0)
      )
    );
  };

  // 第一级 分页 查看
  const startAdd = async val => {
    const paramsArg = {
      depth: 1,
      page: val.page,
      size: val.size,
      sort: "sortOrder,asc"
    };
    const [err, res] = await requestTo(courseTypeFindAll(paramsArg));
    if (res) {
      dataList.value = res?.content;
      pagination.total = res?.totalElements;

      one.value = res?.content[0]?.id;
      tow.value = res?.content[res.content.length - 1]?.id;
    }
    if (err) {
    }
  };

  // 第n级 分页 查看
  const pagingView = async val => {
    console.log("🎁-----val--第n级---", val);
    dataList.value = [];
    const paramsArg = {
      parentId: val.parentId,
      page: val.page,
      size: val.size,
      sort: "sortOrder,asc"
    };
    let aee = removeEmptyValues(paramsArg);
    const res = await courseTypeFindAll(aee);
    const { code, data } = res;
    if (code === 200) {
      dataList.value = data?.content;
      pagination.total = data?.totalElements;

      one.value = data?.content[0]?.id;
      tow.value = data?.content[data?.content?.length - 1]?.id;
    }
  };

  // 查看
  const view = async (terxt, val) => {
    dataList.value = [];
    dataArr.value = val;
    listData.value.push(val);
    isSubPage.value = true; // 进入子页面
  };

  // 移动
  const move = async (text, val) => {
    const operateLog = {
      operateLogType: "COURSE_CLASSIFY_MANAGEMENT",
      operateType: `${text}了"${val.name}"的课程分类`
    };
    const params = {
      courseTypeId: val.id,
      moveType: text === "上移" ? "UP" : "DOWN" // UP:上移，DOWN：下移
    };
    const { code } = await courseTypeMove(params, operateLog);
    if (code === 200) {
      ElMessage({
        type: "success",
        message: text === "上移" ? "上移成功" : "下移成功"
      });
      if (dataArr.value?.id) {
        const paramsArg = {
          parentId: dataArr.value?.id || "",
          page: pagination.currentPage - 1,
          size: pagination.pageSize
        };
        pagingView(paramsArg);
      } else {
        const paramsArg = {
          page: pagination.currentPage - 1,
          size: pagination.pageSize
        };
        startAdd(paramsArg);
      }
    } else {
      ElMessage({
        type: "error",
        message: text === "上移" ? "上移失败" : "下移失败"
      });
    }
  };
  // 删除
  const isFreezeApi = async (text, val) => {
    const operateLog = {
      operateLogType: "COURSE_CLASSIFY_MANAGEMENT",
      operateType: `删除了"${val.name}"的课程分类`
    };
    const params = { id: val.id };
    const { code } = await courseTypeDelete(params, operateLog);
    if (code === 200) {
      ElMessage({
        type: "success",
        message: "删除成功"
      });
      if (dataArr.value?.id) {
        const paramsArg = {
          parentId: dataArr.value?.id || "",
          page: pagination.currentPage - 1,
          size: pagination.pageSize
        };
        pagingView(paramsArg);
      } else {
        const paramsArg = {
          page: pagination.currentPage - 1,
          size: pagination.pageSize
        };
        startAdd(paramsArg);
      }
    } else {
      ElMessage({
        type: "error",
        message: "删除失败"
      });
    }
  };
  // 删除 提示
  const openDialog = (text, val) => {
    ElMessageBox.confirm("确定要删除吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消"
    })
      .then(async () => {
        const res = await findAllCourseType();
        const { code, data } = res;
        if (code === 200) {
          // 递归查找当前分类是否有子分类
          const findChildren = items => {
            for (let item of items) {
              if (item.id === val.id) {
                return item.children && item.children.length > 0;
              }
              if (item.children && item.children.length > 0) {
                const found = findChildren(item.children);
                if (found !== undefined) return found;
              }
            }
          };

          const hasChildren = findChildren(data);
          if (hasChildren) {
            ElMessage.error("该分类下还有子分类，请先删除子分类");
            return;
          } else {
            isFreezeApi(text, val);
          }
        }
      })
      .catch(() => {});
  };

  // 取消或关闭弹出窗口
  const beforeCloseDom = async () => {
    if (ruleFormRef.value) ruleFormRef.value.resetFields();
    form.textarea2 = "";
    form.number = "";
    addressLinkDom.value = false;
    buttonLoading.value = false;
  };
  // 新增 api接口
  const apiHandleAdd = async val => {
    const operateLog = {
      operateLogType: "COURSE_CLASSIFY_MANAGEMENT",
      operateType: `新增了"${val.textarea2}"的课程分类名`
    };
    const paramsArg = {
      name: val.textarea2,
      parentId: dataArr.value?.id || "",
      icon: val.icon
    };
    let aee = removeEmptyValues(paramsArg);
    const { code } = await courseTypeSave(aee, operateLog);
    if (code === 200) {
      ElMessage({
        type: "success",
        message: "新增成功"
      });
      form.title = "";
      form.textarea2 = "";
      form.number = "";
      console.log("🐠-----form---新增 api接口--", form);
      addressLinkDom.value = false;
      buttonLoading.value = false;
      console.log("🎉-----dataArr.value--新增 api接口---", dataArr.value);
      if (dataArr.value?.id) {
        const paramsArg = {
          parentId: dataArr.value?.id || "",
          page: pagination.currentPage - 1,
          size: pagination.pageSize
        };
        pagingView(paramsArg);
      } else {
        const paramsArg = {
          page: pagination.currentPage - 1,
          size: pagination.pageSize
        };
        startAdd(paramsArg);
      }
    } else {
      ElMessage({
        type: "error",
        message: "新增失败"
      });
      buttonLoading.value = false;
    }
  };
  // 修改 api接口
  const apiEditAdd = async val => {
    // 获取旧值 - 从当前数据中找到对应项的原始名称
    const currentItem = dataList.value.find(item => item.id === val.number);
    const oldName = currentItem ? currentItem.name : "未知";

    const operateLog = {
      operateLogType: "COURSE_CLASSIFY_MANAGEMENT",
      operateType: `将"${oldName}"的课程分类名修改为"${val.textarea2}"`
    };
    const paramsArg = {
      id: val.number,
      name: val.textarea2,
      icon: val.icon
    };
    const { code, msg } = await courseTypeUpdate(paramsArg, operateLog);
    if (code === 200) {
      form.title = "";
      form.textarea2 = "";
      form.number = "";
      console.log("🍪-----form--修改 api接口---", form);
      addressLinkDom.value = false;
      buttonLoading.value = false;
      console.log("🐬-----dataArr.value--修改 api接口---", dataArr.value);
      if (dataArr.value?.id) {
        const paramsArg = {
          parentId: dataArr.value?.id || "",
          page: pagination.currentPage - 1,
          size: pagination.pageSize
        };
        pagingView(paramsArg);
      } else {
        const paramsArg = {
          page: pagination.currentPage - 1,
          size: pagination.pageSize
        };
        startAdd(paramsArg);
      }
      ElMessage({
        type: "success",
        message: "分类名修改成功"
      });
    } else {
      ElMessage({
        type: "error",
        message: "分类名修改失败"
      });
      buttonLoading.value = false;
    }
  };
  // 编辑 按钮
  function editAdd(text, val) {
    form.title = "编辑";
    form.textarea2 = val.name;
    form.number = val.id;
    form.url = val.file?.url || "";
    form.icon = val.file?.fileIdentifier || "";
    addressLinkDom.value = true;
  }
  // 新增分类 按钮
  const handleAdd = async () => {
    if (ruleFormRef.value) ruleFormRef.value.resetFields();
    form.title = "新增";
    form.textarea2 = "";
    form.number = "";
    // 根据是否有父级ID判断是主页面还是子页面
    isSubPage.value = !!dataArr.value?.id;
    addressLinkDom.value = true;
  };
  // 弹框 确认按钮
  const confirmAdd = async (ruleFormRef, val) => {
    console.log("🐬-----ruleFormRef ,val-----", ruleFormRef, val);
    if (!ruleFormRef) return;
    await ruleFormRef.validate((valid, fields) => {
      if (valid) {
        buttonLoading.value = true;
        if (val.title === "新增") {
          apiHandleAdd(val);
        } else if (val.title === "编辑") {
          apiEditAdd(val);
        }
      } else {
        console.log("error submit!", fields);
      }
    });
  };

  // 每页多少条
  async function handleSizeChange(val) {
    pagination.pageSize = val;
    if (dataArr.value?.id) {
      const paramsArg = {
        parentId: dataArr.value.id || "",
        page: 0,
        size: val
      };
      pagingView(paramsArg);
    } else {
      const paramsArg = {
        page: 0,
        size: val
      };
      startAdd(paramsArg);
    }
  }
  // 前往页数
  async function handleCurrentChange(val) {
    pagination.currentPage = val;
    if (dataArr.value?.id) {
      const paramsArg = {
        parentId: dataArr.value?.id || "",
        page: val - 1,
        size: pagination.pageSize
      };
      pagingView(paramsArg);
    } else {
      const paramsArg = {
        page: val - 1,
        size: pagination.pageSize
      };
      startAdd(paramsArg);
    }
  }

  // 面包屑 跳转获取数据
  const to = val => {
    if (val) {
      dataArr.value = val;
      const index = listData.value.findIndex(element => element.id === val.id);
      listData.value = listData.value.splice(0, index + 1);
      isSubPage.value = true; // 进入子页面
    } else {
      dataArr.value = {};
      listData.value = [];
      pagination.pageSize = 10;
      pagination.currentPage = 1;
      isSubPage.value = false; // 返回主页面
    }
  };

  // 获取数据的统一方法
  const fetchData = async () => {
    if (dataArr.value?.id) {
      const paramsArg = {
        parentId: dataArr.value?.id || "",
        page: pagination.currentPage - 1,
        size: pagination.pageSize,
        sort: "sortOrder,asc" // 添加排序参数
      };
      await pagingView(paramsArg);
    } else {
      const paramsArg = {
        page: pagination.currentPage - 1,
        size: pagination.pageSize,
        depth: 1, // 添加depth参数
        sort: "sortOrder,asc" // 添加排序参数
      };
      await startAdd(paramsArg);
    }
  };

  // 监听dataArr的变化
  watch(
    () => dataArr.value?.id,
    (newVal, oldVal) => {
      if (newVal !== oldVal || oldVal === undefined) {
        fetchData();
      }
    }
  );

  onMounted(() => {
    isSubPage.value = false; // 初始化为主页面状态
    const paramsArg = {
      page: 0,
      size: 10
    };
    startAdd(paramsArg);
    // 设置标志位，表示已完成初始化
    isInitialized.value = true;
  });

  return {
    one,
    tow,
    loadingTable,
    pagination,
    dataList,
    columns,
    isSubPage,
    view,
    editAdd,
    openDialog,
    move,
    handleAdd,
    handleCurrentChange,
    handleSizeChange,
    listData,
    to,
    addressLinkDom,
    form,
    ruleFormRef,
    rules,
    confirmAdd,
    beforeCloseDom,
    buttonLoading,
    fileUpload
  };
}
