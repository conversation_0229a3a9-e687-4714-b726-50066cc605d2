<script setup lang="ts">
// import ReQrcode from "@/components/ReQrcode";
import WxQrCode from "@/components/WxQrCode/index.vue";
import { useUserStoreHook } from "@/store/modules/user";
import { useI18n } from "vue-i18n";
import Motion from "../utils/motion";
import { onMounted, onUnmounted, ref, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import {
  Loading,
  User,
  Check,
  ArrowLeft,
  Right
} from "@element-plus/icons-vue";
import { getTopMenu, initRouter } from "@/router/utils";
import { message } from "@/utils/message";

const router = useRouter();
const route = useRoute();
const { t } = useI18n();

const loading = ref(false);
const isCallback = ref(false);
const isProcessingCallback = ref(false); // 添加处理回调的开关
const callbackData = ref({
  code: "",
  state: ""
});

const wxQrCodeRef = ref(null);

// 重置到初始状态
const resetToInitialState = () => {
  isCallback.value = false;
  loading.value = false;
  isProcessingCallback.value = false; // 重置处理状态
  callbackData.value = { code: "", state: "" };

  // 清除处理记录，允许重新处理
  processedCallbacks.value.clear();

  // 清除URL参数并重新初始化微信登录
  router.replace({
    path: route.path,
    query: {}
  });
};

// 处理微信回调
const handleWxCallback = async (code: string, state: string) => {
  // 设置正在处理回调状态
  isProcessingCallback.value = true;
  loading.value = true;

  // 从 sessionStorage 获取 state 进行验证
  // const storedState = sessionStorage.getItem("wx_login_state");
  // if (state !== storedState) {
  //   message("微信登录状态不一致，请重新扫码", { type: "error" });
  //   resetToInitialState();
  //   return;
  // }

  try {
    await useUserStoreHook()
      .loginByQrCode({ code })
      .then(res => {
        return initRouter().then(() => {
          loading.value = true;
          router
            .push(getTopMenu(true).path)
            .then(() => {
              message(t("login.pureLoginSuccess"), { type: "success" });
            })
            .finally(() => (loading.value = false));
        });
      })
      .catch(err => {
        console.log(err, "err");
        // 确保在微信登录失败后，保持平台管理员角色选择
        useUserStoreHook().SET_ROLETARGET("平台管理员");
        resetToInitialState();
      })
      .finally(() => (loading.value = false));
    loading.value = false;
  } catch (error) {
    console.error("微信登录请求失败:", error);
    message("微信登录失败，请重新扫码", { type: "error" });
    resetToInitialState();
  }
};

// 用于防止重复处理同一个回调
const processedCallbacks = ref(new Set());

// 监听路由参数变化
watch(
  () => route.query,
  newQuery => {
    const { code, state } = newQuery;
    console.log(code, state, "微信回调参数");

    if (code && state) {
      // 如果正在处理回调，跳过
      if (isProcessingCallback.value) {
        console.log("正在处理回调中，跳过重复处理");
        return;
      }

      // 生成唯一标识，防止重复处理
      const callbackId = `${code}_${state}`;

      // 如果已经处理过这个回调，就不再处理
      if (processedCallbacks.value.has(callbackId)) {
        console.log("回调已处理过，跳过:", callbackId);
        return;
      }

      // 标记为已处理
      processedCallbacks.value.add(callbackId);

      isCallback.value = true;
      callbackData.value = { code: code as string, state: state as string };
      handleWxCallback(code as string, state as string);
    } else {
      // 没有回调参数时，清理处理记录和状态
      if (!isProcessingCallback.value) {
        processedCallbacks.value.clear();
        isCallback.value = false;
      }
    }
  },
  { immediate: true }
);

// 返回账号登录
const onBack = () => {
  // 确保回到账号登录页面时，保持平台管理员角色选择
  useUserStoreHook().SET_ROLETARGET("平台管理员");
  useUserStoreHook().SET_CURRENTPAGE(0);
};

// 切换到手机号登录
const switchToPhoneLogin = () => {
  useUserStoreHook().SET_CURRENTPAGE(1);
};

// 测试登录接口
const testLogin = () => {
  handleWxCallback("021Cud100KittU1mv81005C4Sg2Cud19", "123");
};
</script>

<template>
  <!-- 微信登录处理中的loading状态 -->
  <div
    v-if="isCallback && isProcessingCallback"
    class="callback-loading-container"
  >
    <div class="loading-content">
      <el-icon class="is-loading loading-icon">
        <Loading />
      </el-icon>
      <h3 class="loading-title">微信登录处理中</h3>
      <p class="loading-text">正在验证您的微信信息，请稍候...</p>
    </div>
  </div>

  <!-- 正常的二维码登录页面 -->
  <div v-else>
    <Motion class="-mt-2 -mb-2">
      <!-- <ReQrcode :text="t('login.pureTest')" /> -->
      <WxQrCode ref="wxQrCodeRef" :redirectPath="route.path" :fastLogin="1" />
    </Motion>

    <Motion :delay="100">
      <el-divider>
        <p class="text-gray-500 text-xs">{{ t("login.pureTip") }}</p>
      </el-divider>
    </Motion>

    <Motion :delay="150">
      <div class="w-full h-[20px] flex justify-between items-center">
        <el-button class="w-full mt-4" :disabled="loading" @click="onBack">
          {{ "账号登录" }}
        </el-button>
        <el-button
          class="w-full mt-4 ml-2"
          :disabled="loading"
          @click="switchToPhoneLogin"
        >
          {{ t("login.purePhoneLogin") }}
        </el-button>
      </div>
    </Motion>

    <!-- 测试按钮 -->
    <!-- <Motion :delay="200">
      <el-button
        class="w-full mt-4"
        type="warning"
        :disabled="loading"
        @click="testLogin"
      >
        测试登录接口
      </el-button>
    </Motion> -->
  </div>
</template>

<style scoped>
/* 微信登录处理中的loading样式 */
.callback-loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  min-height: 300px;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.loading-icon {
  font-size: 32px;
  color: #409eff;
  margin-bottom: 16px;
}

.loading-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.loading-text {
  font-size: 14px;
  color: #909399;
  margin: 0;
  line-height: 1.5;
}

/* 确保二维码容器有合适的样式 */
#login_container {
  width: 100%;
  min-height: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
