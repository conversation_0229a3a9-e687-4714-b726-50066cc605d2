import { SM4 } from "gm-crypto";
import { courseAdd, verifyPhone, verifyUsername } from "@/api/institution";

const key = "377a416b574e66504a70345242586459";

// sm4 加密
function encryption(text) {
  const encryptedData = SM4.encrypt(text, key, {
    inputEncoding: "utf8",
    outputEncoding: "base64"
  });
  return encryptedData;
}

/*

#任务目标
将我提供给你的信息进行结构化的转化，部分信息需你进行补充

#待转化内容（机构名称、姓名、手机号、机构简介）
中山市第一职业技术学校（劳动教育基地） 惠琴  *********** 学校拥有有企业经历的“双师”型专业教师团队，形成劳动实践教育培养体系。通过联合中小学校开展劳动教育活动，对改变人们的职业认知、专业选择和培养孩子适合的职业兴趣、教育志趣与升学选择具有重要的决定性作用。
中山市沙溪理工学校（劳动教育基地） 彭利荣 0760-******** 学校构建“培技育德”双元驱动劳动教育课程，实施劳动教育“四链四育”人才培养模式，开展“六个一”送教服务，探索“做中学、学中做、做中悟”育人途径，全面发展学生劳动素养，荣获国家级荣誉1项，省级荣誉9项。

#返回格式
以数组的形式返回，数组内的每个元素为json，格式如下：
  {
      "name": "[待转化内容中的机构名称]",
      "alias": "[为该机构设定一个不重复的英文别名，要求为小写英文字母，不能出现中文或者数字，连续无空格，字符数不超过10个字符]",
      "introduction": "<p>[待转化内容中的机构简介]</p>",
      "username": "[待转化内容中的姓名]",
      "account": "[该字段由两个部分组成，第一部分为待转化内容中姓名的拼音小写字母缩写，第二部分为该机构的英文别名，即alias，两部分以@进行连接]",
      "phone": "[待转化内容中的手机号]",
      "customerServiceHotline": "[待转化内容中的手机号]"
  }

 */

// 创建机构
export function createIntroduction() {
  let tmpEnData = [
    {
      name: "中山市第一职业技术学校（劳动教育基地）",
      alias: "dyzyjsxx",
      introduction:
        "<p>学校拥有有企业经历的“双师”型专业教师团队，形成劳动实践教育培养体系。通过联合中小学校开展劳动教育活动，对改变人们的职业认知、专业选择和培养孩子适合的职业兴趣、教育志趣与升学选择具有重要的决定性作用。</p>",
      username: "惠琴",
      account: "huiqin@dyzyjsxx",
      phone: "***********",
      customerServiceHotline: "***********"
    },
    {
      name: "中山市沙溪理工学校（劳动教育基地）",
      alias: "shaxi",
      introduction:
        "<p>学校构建“培技育德”双元驱动劳动教育课程，实施劳动教育“四链四育”人才培养模式，开展“六个一”送教服务，探索“做中学、学中做、做中悟”育人途径，全面发展学生劳动素养，荣获国家级荣誉1项，省级荣誉9项。</p>",
      username: "彭利荣",
      account: "penglirong@shaxi",
      phone: "0760-********",
      customerServiceHotline: "0760-********"
    },
    {
      name: "中山市建斌职业技术学校（劳动教育基地）",
      alias: "zhongshanjianbin",
      introduction:
        "<p>中山市建斌职业技术学校学校是一所市属公办学校、学校办学业绩突出，国家级重点中等职业学校、广东省示范性中等职业学校，广东省高水平职业学校建设学校，篮球、足球特色学校，全国成人教育先进单位。</p>",
      username: "张贤文",
      account: "zhangxianwen@zhongshanjianbin",
      phone: "***********",
      customerServiceHotline: "***********"
    },
    {
      name: "中山市现代职业技术学校（劳动教育基地）",
      alias: "xiandaizhiye",
      introduction:
        "<p>在中山市农业农村局、三乡镇农业农村局的指导与支持下，中山食出公司产业园，实现农文旅产业融合发展。</p>",
      username: "李晓荣",
      account: "lixiaorong@xiandaizhiye",
      phone: "***********",
      customerServiceHotline: "***********"
    },
    {
      name: "中山市火炬科学技术学校（劳动教育基地）",
      alias: "huojukexue",
      introduction:
        "<p>我校劳动教育基地以科技创新为主，非遗项目为辅，手工制作为切入点，着重培养学生科技创新精神。开设了科技创新、3D打印、无人机飞控体验、钳工体验、掐丝珐琅、版画拓印、建筑沙盘、秀珠等劳动教育课程。</p>",
      username: "韩冬",
      account: "handong@huojukexue",
      phone: "***********",
      customerServiceHotline: "***********"
    },
    {
      name: "广东华农农业股份有限公司华农水培蔬菜基地",
      alias: "huanong",
      introduction:
        "<p>广东华农农业股份有限公司，专注无土栽培技术。基地全部覆盖塑料大棚、防虫网、自动播种机、喷灌设备、种植槽等现代农业设施，放养酵素走地鸡，中山麻鸭，研发农业机器人，是广东省菜篮子基地、粤港澳大湾区菜篮子。</p>",
      username: "亮亮",
      account: "liangliang@huanong",
      phone: "***********",
      customerServiceHotline: "***********"
    }
  ];

  // for (var i = 0; i < tmpEnData.length; i++) {
  //   console.log("创建：" + tmpEnData[i].name);
  //   let tt = encryption(tmpEnData[i].phone)
  //   console.log(tt);
  //   tmpEnData[i].phone = tt;
  //   const operateLog = {
  //     operateLogType: "COMPLEX_MANAGEMENT",
  //     operateType: "创建了",
  //     operatorTarget: tmpEnData[i].name
  //   };
  //   courseAdd(tmpEnData[i], operateLog);
  // }
  console.log(tmpEnData);
}
