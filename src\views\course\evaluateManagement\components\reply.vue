<script setup>
import {
  onMounted,
  ref,
  onActivated,
  nextTick,
  onUnmounted,
  onDeactivated
} from "vue";
import { formatTime } from "@/utils/index";
import {
  courseFindAll,
  courseIsFreeze,
  findAllCourseType
} from "@/api/course.js";
import {
  findByReplies,
  batchSetAuditRepliesState
} from "@/api/evaluateManagement.js";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import { to, isEmpty } from "@iceywu/utils";
import ExamineDialog from "@/components/Base/evaluateDialog.vue";
import { Warning } from "@element-plus/icons-vue";

const props = defineProps({
  replyObj: {
    type: Object,
    default: () => ({
      todoId: 0,
      state: "PENDING_REVIEW"
    })
  }
});
const tableRef = ref(null);
onMounted(async () => {
  await nextTick();
  getTableList(props.replyObj.state);
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);
});

onUnmounted(() => {
  window.removeEventListener("resize", calculateTableHeight);
});
const router = useRouter();

// 计算表格高度
const searchFormHeight = ref(0);
const tableHeight = ref("88vh"); // 初始默认值，会被 calculateTableHeight 覆盖

const calculateTableHeight = async () => {
  await nextTick();
  const searchForm = document.querySelector(".search .con_search");
  //   if (searchForm) {
  //     searchFormHeight.value = searchForm.offsetHeight;
  //     if (isSelection.value) {
  //       tableHeight.value = `calc(88vh - 140px - ${searchFormHeight.value}px - 125px)`;
  //     } else {
  //       tableHeight.value = `calc(88vh - 140px - ${searchFormHeight.value}px - 100px)`;
  //     }
  //   }
  if (isSelection.value) {
    tableHeight.value = `calc(88vh - 140px - ${searchFormHeight.value}px - 152px)`;
  } else {
    if (props.replyObj.state === "PENDING_REVIEW") {
      tableHeight.value = `calc(88vh - 140px - ${searchFormHeight.value}px - 130px)`;
    } else {
      tableHeight.value = `calc(88vh - 140px - ${searchFormHeight.value}px - 90px)`;
    }
  }
};

// 搜索表单
const form = ref({
  content: ""
});
// 表格数据
const tableData = ref([]);
const params = ref({
  page: 1,
  size: 15,
  sort: "createdAt,desc",
  totalElements: 0
});
// 获取列表信息
const getListLoading = ref(false);
const getTableList = async data => {
  if (getListLoading.value) {
    return;
  }
  getListLoading.value = true;
  let paramsData = {
    page: params.value.page - 1,
    size: params.value.size,
    sort: params.value.sort,
    auditState: data || "PENDING_REVIEW"
    // auditState:'APPROVED'
  };
  for (const paramsDataKey in form.value) {
    let isArray = Array.isArray(form.value[paramsDataKey]);
    if (isArray) {
      if (form.value[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    } else {
      if (form.value[paramsDataKey]) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    }
  }
  const [err, result] = await requestTo(findByReplies(paramsData));
  // console.log("🎁-----result--222---", result);
  if (result) {
    tableData.value = result?.content;

    params.value.totalElements = result.totalElements;
    await nextTick();
    calculateTableHeight();
  } else {
    ElMessage.error(err);
  }
  getListLoading.value = false;
};
//分页
const handleCurrentChange = e => {
  params.value.page = e;
  getTableList(props.replyObj.state);
};
const handleSizeChange = val => {
  params.value.page = 1;
  params.value.size = val;
  getTableList(props.replyObj.state);
};
//搜索
const searchData = () => {
  params.value.page = 1;
  getTableList(props.replyObj.state);
};
// 重置
const setData = () => {
  form.value.content = "";
  params.value.page = 1;
  getTableList(props.replyObj.state);
};
const isSelection = ref(false);
const handleBatchAudit = () => {
  isSelection.value = true;
  if (isSelection.value) {
    tableHeight.value = `calc(88vh - 140px - ${searchFormHeight.value}px - 152px)`;
  } else {
    if (props.replyObj.state === "PENDING_REVIEW") {
      tableHeight.value = `calc(88vh - 140px - ${searchFormHeight.value}px - 130px)`;
    } else {
      tableHeight.value = `calc(88vh - 140px - ${searchFormHeight.value}px - 90px)`;
    }
  }
};
const getRowKeys = row => {
  return row.id;
};
const batchDeleteArr = ref([]);
const handleSelectionChange = val => {
  batchDeleteArr.value = [];
  ids.value = [];
  if (!val.length) return;
  if (val.length > 0) {
    val.forEach(item => {
      batchDeleteArr.value.push(item);
    });
  } else {
    batchDeleteArr.value = val[0];
  }
};
const ids = ref([]); //审核id
const idsName = ref(""); //被审核名
// 单个通过
const passEvt = (row, bool) => {
  if (isEmpty(row)) {
    ElMessage.error("请选择需要审核的回复");
    return;
  }
  if (row && row.length > 0) {
    ids.value = row.map(it => it.id);
    idsName.value = row.map(it => it.userName).join("、");
  } else {
    ids.value = [row.id];
    idsName.value = row.userName;
  }
  // console.log("🌳 idsName.value------------------------------>", idsName.value);
  ElMessageBox.confirm(`确认审核通过么？`, "审核通过", {
    confirmButtonText: "确认",
    cancelButtonText: "取消"
  })
    .then(() => {
      setAuditApi(row, bool);
    })
    .catch(() => {});
};
const rejectEvt = (row, bool) => {
  if (isEmpty(row)) {
    ElMessage.error("请选择需要审核的回复");
    return;
  }
  if (row && row.length > 0) {
    ids.value = row.map(it => it.id);
    idsName.value = row.map(it => it.userName).join("、");
  } else {
    ids.value = [row.id];
    idsName.value = row.userName;
  }
  dialogFormVisible.value = true;
};
const operateLog = ref({});
const setAuditApi = async (row, bool) => {
  const params = {
    ids: ids.value,
    auditState: bool
  };
  if (bool === "APPROVED") {
    operateLog.value = {
      operateLogType: "COMMENTS",
      operateType: `通过了${idsName.value}的回复`
    };
  } else {
    params.reason = reason.value;
    operateLog.value = {
      operateLogType: "COMMENTS",
      operateType: `驳回了${idsName.value}的回复`
    };
  }
  // console.log("🍭params------------------------------>", params);
  const [err, res] = await to(
    batchSetAuditRepliesState(params, operateLog.value)
  );
  if (res.code === 200) {
    ElMessage({
      type: "success",
      message: "审核成功"
    });
    getTableList();
    ids.value = [];
    batchDeleteArr.value = [];
  } else {
    ElMessage({
      type: "error",
      message: "审核失败"
    });
  }
  if (err) {
    ElMessage({
      type: "error",
      message: err.msg
    });
  }
};
// 取消
const cancelEvt = () => {
  isSelection.value = false;
  batchDeleteArr.value = [];
  ids.value = [];
  idsName.value = "";
  // 清除表格中的选择状态
  if (tableRef.value) {
    tableRef.value.clearSelection();
  }
  // 重新计算表格高度
  calculateTableHeight();
};
const updateData = () => {
  ids.value = [];
  batchDeleteArr.value = [];
  getTableList();
};
const dialogFormVisible = ref(false);
defineExpose({ getTableList });
</script>

<template>
  <div>
    <div class="con_search">
      <el-form :model="form" :inline="true">
        <el-form-item label="内容">
          <el-input
            v-model.trim="form.content"
            placeholder="请输入"
            style="width: 180px"
            clearable
            @clear="clearEvt('content')"
          />
        </el-form-item>
        <el-form-item label=" ">
          <div class="flex">
            <el-button type="primary" @click="searchData">搜索</el-button>
            <el-button @click="setData">重置</el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <div>
      <el-button
        v-if="replyObj.todoId === 0"
        type="primary"
        style="margin-bottom: 10px"
        @click="handleBatchAudit"
      >
        批量审核
      </el-button>
    </div>
    <el-scrollbar class="scrollbar" :style="{ height: tableHeight }">
      <el-table
        ref="tableRef"
        :data="tableData"
        :header-cell-style="{
          backgroundColor: '#fafafa',
          color: '#565353'
        }"
        table-layout="fixed"
        :max-height="tableHeight"
        :row-key="getRowKeys"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          v-if="isSelection === true && replyObj.todoId === 0"
          :selectable="selectable"
          :reserve-selection="true"
          type="selection"
          width="55"
        />
        <el-table-column
          prop="userName"
          label="回复人"
          width="100"
          align="left"
          show-overflow-tooltip
          fixed
        >
          <template #default="scope">
            {{ scope.row.userName || "--" }}
          </template>
        </el-table-column>
        <el-table-column prop="content" label="回复内容" align="left">
          <template #default="scope">
            <div>
              {{ scope.row.content || "--" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="coursePeriodName"
          label="课期名称"
          align="left"
          width="250"
          show-overflow-tooltip
        >
          <template #default="scope">
            <div
              class="name-style"
              @click="
                router.push({
                  path: '/course/current/details/evaluate',
                  query: { text: 'course', periodId: scope.row.coursePeriodId }
                })
              "
            >
              {{ scope.row.coursePeriodName || "--" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          width="250"
          prop="updatedAt"
          label="回复时间"
          align="left"
        >
          <template #default="scope">
            <div>
              {{
                formatTime(scope.row?.updatedAt, "YYYY-MM-DD HH:mm:ss") || "--"
              }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="replyObj.todoId !== 0"
          prop="auditState"
          label="回复状态"
          align="left"
          fixed="right"
          width="250px"
        >
          <template #default="scope">
            <div v-if="replyObj.state === 'APPROVED'" style="color: #409eff">
              已通过
            </div>
            <div v-else style="color: red">
              <el-tooltip
                class="box-item"
                title=""
                :content="scope.row.reason"
                placement="bottom"
                effect="light"
                :popper-style="{ maxWidth: '400px' }"
              >
                <div class="state-reject">
                  已驳回<el-icon style="color: red; margin-left: 3px">
                    <Warning />
                  </el-icon>
                </div>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="isSelection !== true && replyObj.todoId === 0"
          fixed="right"
          label="审核操作"
          align="left"
          width="150px"
        >
          <template #default="{ row }">
            <el-button type="primary" link @click="passEvt(row, 'APPROVED')">
              通过
            </el-button>
            <el-button type="danger" link @click="rejectEvt(row, 'REJECTED')">
              驳回
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-scrollbar>
    <div
      v-if="isSelection === true && replyObj.todoId === 0"
      class="examine-btn"
    >
      <el-button type="primary" @click="passEvt(batchDeleteArr, 'APPROVED')">
        通过
      </el-button>
      <el-button type="danger" @click="rejectEvt(batchDeleteArr, 'REJECTED')">
        驳回
      </el-button>
      <el-button type="default" @click="cancelEvt">取消</el-button>
    </div>
    <div class="con_pagination">
      <!-- 分页 -->
      <el-pagination
        v-model:current-page="params.page"
        v-model:page-size="params.size"
        :class="isSelection === true ? 'con_pagination-big' : 'con_pagination'"
        background
        :page-sizes="[15, 30, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="params.totalElements"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <ExamineDialog
      :id="ids"
      v-model:dialogFormVisible="dialogFormVisible"
      :api="batchSetAuditRepliesState"
      :operateLogType="'COMMENTS'"
      :operateType="`驳回了${idsName}的回复`"
      :logOut="false"
      :showContent="'examine'"
      :textRightBtn="'确认'"
      :textLeftBtn="'取消'"
      :title="'审核驳回'"
      @reset="dialogFormVisible = false"
      @update-data="updateData"
    />
  </div>
</template>

<style lang="scss" scoped>
.con_pagination {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  margin-top: 10px;
}
.con_pagination-big {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  // margin-top: 15px;
}
.state-reject {
  width: 40px;
  display: flex;
  // justify-content: center;
  align-items: center;
  white-space: nowrap;
}
.name-style {
  cursor: pointer;
  width: 100%;
  height: 100%;
  &:hover {
    color: #409eff;
  }
}
</style>
