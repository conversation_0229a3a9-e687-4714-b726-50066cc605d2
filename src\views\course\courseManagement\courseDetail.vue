<script setup>
import { ref, onMounted, onActivated, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import {
  courseFindById,
  coursePeriodFindAll,
  findLeaderLecturerByCourseId,
  coursePeriodOnline,
  coursePeriodOffline,
  platformCloseGroupOrder
} from "@/api/course.js";

import { ElMessage, ElMessageBox } from "element-plus";
import { requestTo } from "@/utils/http/tool";
import { formatTime } from "@/utils/index";
import { Warning } from "@element-plus/icons-vue";
import { to } from "@iceywu/utils";
import { AUDIT_ENUM } from "@/utils/enum";
import { ImageThumbnail } from "@/utils/imageProxy";
defineOptions({
  name: "CourseManagementDetail"
});
onActivated(() => {
  getTablePeriodList();
});
const router = useRouter();
const route = useRoute();
const copyShow = ref(false);

// 课程信息配置项
const courseInfoConfig = ref([
  {
    id: "courseName",
    label: "课程名",
    value: "",
    key: "name"
  },
  {
    id: "organization",
    label: "机构",
    value: "",
    key: "organization.name"
  },
  {
    id: "courseId",
    label: "课程ID",
    value: "",
    key: "id"
  },
  {
    id: "courseType",
    label: "课程类型",
    value: "",
    key: "courseType.name"
  },
  {
    id: "courseStatus",
    label: "课程状态",
    value: "",
    key: "freeze",
    formatter: data => (data.freeze === true ? "冻结" : "正常"),
    style: data => ({
      color: data.freeze === true ? "#f56c6c" : "#67c23a"
    })
  },
  {
    id: "createdAt",
    label: "创建时间",
    value: "",
    key: "createdAt",
    formatter: data => formatTime(data.createdAt, "YYYY-MM-DD HH:mm:ss") || "--"
  },
  {
    id: "ageRange",
    label: "课程年龄段",
    value: "",
    key: "ageRange",
    formatter: data => {
      if (data.minAge && data.maxAge) {
        return `${data.minAge}-${data.maxAge}岁`;
      } else if (data.minAge) {
        return `${data.minAge}岁以上`;
      } else if (data.maxAge) {
        return `${data.maxAge}岁以下`;
      }
      return "--";
    }
  }
]);

// 保持原有的tableHeader用于兼容性
const tableHeader = ref([
  {
    id: "1",
    label: "机构",
    value: "",
    width: "107px"
  },
  {
    id: "2",
    label: "课程ID",
    value: "",
    width: "107px"
  },
  {
    id: "3",
    label: "创建时间",
    value: "",
    width: "107px"
  },
  {
    id: "4",
    label: "课程类型",
    value: "",
    width: "107px"
  },
  {
    id: "5",
    label: "课程亮点标签",
    value: "",
    width: "107px"
  },
  {
    id: "6",
    label: "课程状态",
    value: "",
    width: "107px"
  },
  {
    id: "7",
    label: "课程简介",
    value: "",
    width: "107px"
  }
]);
// 表格
const tableData = ref([]);
// 搜索表单
const form = ref({
  startTime: "",
  endTime: "",
  coursePeriodState: "all",
  leadersId: 0,
  lecturersId: 0,
  buyType: "all",
  reviewState: "all"
});
const url = ref();

const srcList = ref([]);
// 购买类型
const typeOptions = [
  {
    value: "all",
    label: "全部"
  },
  {
    value: "ORDINARY",
    label: "普通单"
  },
  {
    value: "PRIVATE_DOMAIN_GROUP_ORDER",
    label: "团购单"
  }
];
// 课程状态
const stateOptions = [
  {
    value: "all",
    label: "全部"
  },
  // {
  //   value: "NOT_LISTED",
  //   label: "未上架"
  // },
  {
    value: "ONLINE",
    label: "上架"
  },
  {
    value: "ONLINE_UNDER_REVIEW",
    label: "上架审核"
    // label: "审核中"
  },
  {
    value: "OFFLINE",
    label: "下架"
  },
  {
    value: "OFFLINE_UNDER_REVIEW",
    label: "下架审核"
    // label: "审核中"
  },
  {
    value: "COMPLETED",
    label: "已完成"
  }
];
// 审核状态
const auditOptions = [
  {
    value: "all",
    label: "全部"
  },
  {
    value: "ONLINE_UNDER_REVIEW",
    label: "上架审核中"
  },
  {
    value: "ONLINE_PASS",
    label: "上架通过"
  },
  {
    value: "ONLINE_REJECT",
    label: "上架驳回"
    // label: "审核中"
  },
  {
    value: "OFFLINE_UNDER_REVIEW",
    label: "下架审核中"
  },
  {
    value: "OFFLINE_PASS",
    label: "下架通过"
    // label: "审核中"
  },
  {
    value: "OFFLINE_REJECT",
    label: "下架驳回"
    // label: "审核中"
  },
  {
    value: "NONE",
    label: "无"
  }
];
// 获取课程状态
const getSatte = val => {
  let res = "";
  stateOptions?.map(item => {
    if (item.value === val) {
      res = item.label;
    }
  });
  return res;
};

// 讲师
const teacherOptions = ref([
  {
    label: "全部",
    value: 0
  }
]);
// 领队
const leaderOptions = ref([
  {
    label: "全部",
    value: 0
  }
]);
// 领队讲师查询
const leaderFindApi = async type => {
  const params = {
    type: type,
    courseId: route.query.id
  };
  let [err, res] = await requestTo(findLeaderLecturerByCourseId(params));
  if (res) {
    if (type === "LECTURER") {
      let res1 = res.map(it => {
        return {
          ...it,
          label: it.name,
          value: it.id
        };
      });
      teacherOptions.value = teacherOptions.value.concat(res1);
    } else {
      let res2 = res.map(it => {
        return {
          ...it,
          label: it.name,
          value: it.id
        };
      });
      leaderOptions.value = leaderOptions.value.concat(res2);
    }
  } else {
    console.log("🌵-----err-----", err);
  }
};

// 重置
const setData = () => {
  params.value.page = 1;
  form.value = {
    startTime: "",
    endTime: "",
    coursePeriodState: "all",
    leadersId: 0,
    lecturersId: 0
  };
  pickTime.value = "";
  getTablePeriodList();
};
//搜索
const searchData = () => {
  params.value.page = 1;
  getTablePeriodList();
};
// 清除数据
const clearEvt = val => {
  if (val === "time") {
    form.value.startTime = "";
    form.value.endTime = "";
  }
  // params.value.page = 1;
  getTablePeriodList();
};
const pickTime = ref("");
// 选择时间
const timeChange = value => {
  // console.log("🐬-----value-----", value);
  if (value) {
    // form.value.startTime = value[0];
    // form.value.endTime = value[1];
    form.value.startTime = new Date(value[0])?.getTime();
    const endDate = new Date(value[1]);
    endDate.setHours(23, 59, 59, 999); // 关键修改
    form.value.endTime = endDate.getTime();
  }
};

const params = ref({
  page: 1,
  size: 15,
  sort: "createdAt,desc",
  totalElements: 0
});

const courseName = ref();
// 获取列表信息
const getListLoading = ref(false);
const getTableList = async data => {
  if (getListLoading.value) {
    return;
  }
  getListLoading.value = true;
  let paramsData = {
    id: route.query.id
  };
  const [err, result] = await requestTo(courseFindById(paramsData));
  if (result) {
    courseName.value = result.name;
    // 更新新的课程信息配置项
    courseInfoConfig.value.forEach(config => {
      if (config.formatter) {
        config.value = config.formatter(result);
      } else {
        // 支持嵌套属性访问，如 organization.name
        const keys = config.key.split(".");
        let value = result;
        for (const key of keys) {
          value = value?.[key];
        }
        config.value = value || "--";
      }
    });
    tableHeader.value[6].value = result.introduction || "--";
    tableHeader.value[0].value = result.organization?.name || "--";
    tableHeader.value[1].value = result.id || "--";
    tableHeader.value[2].value =
      formatTime(result.createdAt, "YYYY-MM-DD HH:mm:ss") || "--";
    tableHeader.value[3].value = result.courseType?.name || "--";
    // if (result.minPeopleNumber && result.maxPeopleNumber) {
    //   tableHeader.value[4].value =
    //     result.minPeopleNumber + "-" + result.maxPeopleNumber;
    // } else if (result.minPeopleNumber) {
    //   tableHeader.value[4].value = result.minPeopleNumber;
    // } else if (result.maxPeopleNumber) {
    //   tableHeader.value[4].value = result.maxPeopleNumber;
    // } else {
    //   tableHeader.value[4].value = "--";
    // }
    if (result.tags && result.tags.length) {
      tableHeader.value[4].value = result.tags?.join("、");
    } else {
      tableHeader.value[4].value = "--";
    }
    if (result.files?.length) {
      result.files.map(item => {
        srcList.value.push(item?.uploadFile?.url);
      });
      url.value = result.files[0]?.uploadFile?.url;
    }
    tableHeader.value[5].value = result.freeze || "--";
  } else {
    ElMessage.error(err);
  }
  getListLoading.value = false;
};
// 获取课期列表信息
const gettLoading = ref(false);
const getTablePeriodList = async data => {
  if (gettLoading.value) {
    return;
  }
  gettLoading.value = true;
  let paramsData = {
    page: params.value.page - 1,
    size: params.value.size,
    sort: params.value.sort,
    courseId: route.query.id
  };
  for (const paramsDataKey in form.value) {
    let isArray = Array.isArray(form.value[paramsDataKey]);
    if (isArray) {
      if (form.value[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    } else {
      if (form.value[paramsDataKey]) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    }
  }
  if (form.value.coursePeriodState === "all") {
    delete paramsData.coursePeriodState;
  }
  if (form.value.buyType === "all") {
    delete paramsData.buyType;
  }
  if (form.value.reviewState === "all") {
    delete paramsData.reviewState;
  }
  const [err, result] = await requestTo(coursePeriodFindAll(paramsData));
  if (result) {
    tableData.value = result?.content;
    params.value.totalElements = result.totalElements;
  } else {
    ElMessage.error(err);
  }
  gettLoading.value = false;
};
//分页
const handleCurrentChange = e => {
  params.value.page = e;
  getTablePeriodList();
};
const handleSizeChange = val => {
  params.value.page = 1;
  params.value.size = val;
  getTablePeriodList();
};
//详细资料
const editinfo = () => {
  router.push({
    path: "/course/management/information",
    query: {
      id: route.query.id
    }
  });
};
// 详情
const detailEvt = info => {
  console.log("💗getInfoid---------->", info);
  router.push({
    path: "/course/management/current/details",
    query: { courseId: route.query.id, periodId: info, text: "course" }
  });
};
// 上下架
// 是否上下架
const removeEvt = (row, bool) => {
  let freezeText =
    bool === true ? "确定要下架该课期吗？" : "确定要上架该课期吗？";
  let title = bool === true ? "下架" : "上架";
  ElMessageBox.confirm(`${freezeText}`, `确定${title}`, {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(() => {
      // console.log("🐬-----row-----", row);
      isFreezeApi(row, bool);
    })
    .catch(() => {});
};
const coursePeriodStr = ref();
const isFreezeApi = async (row, bool) => {
  const params = {
    id: row?.id
  };
  let api;
  if (bool) {
    api = coursePeriodOffline;
    coursePeriodStr.value = {
      operateLogType: "COURSE_MANAGEMENT",
      operateType: `下架了“${row.name}”课期`
    };
  } else {
    api = coursePeriodOnline;
    coursePeriodStr.value = {
      operateLogType: "COURSE_MANAGEMENT",
      operateType: `上架了“${row.name}”课期`
    };
  }
  const { code } = await api(params, coursePeriodStr.value);
  if (code === 200) {
    ElMessage({
      type: "success",
      message: bool === true ? "下架成功" : "上架成功"
    });
    getTablePeriodList();
  } else {
    ElMessage({
      type: "error",
      message: bool === true ? "下架失败" : "上架失败"
    });
  }
};
// 关闭团购
const closeGroup = val => {
  let coursePeriodStr = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType: `取消了“${val.name}”课期的定制`
  };
  ElMessageBox.confirm(`确定要取消定制该课程吗？`, "取消定制", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(async () => {
      const [err, res] = await to(
        platformCloseGroupOrder({ id: val.id }, coursePeriodStr)
      );
      if (res.code === 200) {
        ElMessage({
          type: "success",
          message: "取消定制成功"
        });
        getTablePeriodList();
      } else {
        ElMessage({
          type: "error",
          message: `取消定制失败${res.msg}`
        });
      }
      // console.log("🐬-----row-----", row);
    })
    .catch(() => {});
};
// 获取领队讲师姓名
const getName = val => {
  let res = [];
  if (!val?.length) return;
  val.map(item => {
    res.push(item.name);
  });
  return res.join("、");
};
// 计算课程信息展示数据
const courseMetaRows = computed(() => {
  // 获取所有有效的配置项（排除课程名，且有数据的）
  const validConfigs = courseInfoConfig.value
    .filter(
      item => item.id !== "courseName" && item.value && item.value !== "--"
    )
    .map(item => ({ ...item }));

  // 按每行3列分组
  const rows = [];
  for (let i = 0; i < validConfigs.length; i += 3) {
    rows.push(validConfigs.slice(i, i + 3));
  }
  return rows;
});

onMounted(async () => {
  getTableList();
  getTablePeriodList();
  await leaderFindApi("LECTURER");
  leaderFindApi("LEADER");
});

// 课期状态颜色
const getCoursePeriodStateColor = state => {
  const colorMap = {
    NOT_LISTED: "#9A9A9A", // 无/未上架 - 灰色
    ONLINE: "#4095E5", // 上架 - 蓝色
    ONLINE_UNDER_REVIEW: "#FF8000", // 审核中 - 橙色
    OFFLINE: "#FF6161", // 下架 - 红色
    OFFLINE_UNDER_REVIEW: "#FF6161", // 下架审核 - 红色
    COMPLETED: "#4095E5" // 已完成 - 蓝色
  };
  return colorMap[state] || "#9A9A9A";
};

// 审核状态颜色
const getAuditStateColor = state => {
  const colorMap = {
    NONE: "#9A9A9A", // 无 - 灰色
    ONLINE_UNDER_REVIEW: "#FF8000", // 审核中 - 橙色
    OFFLINE_UNDER_REVIEW: "#FF8000", // 审核中 - 橙色
    ONLINE_PASS: "#4095E5", // 审核通过/已完成 - 蓝色
    OFFLINE_PASS: "#4095E5", // 审核通过/已完成 - 蓝色
    ONLINE_REJECT: "#FF6161", // 审核驳回 - 红色
    OFFLINE_REJECT: "#FF6161" // 审核驳回 - 红色
  };
  return colorMap[state] || "#9A9A9A";
};
</script>

<template>
  <div class="containers">
    <!-- 顶部课程信息卡片 -->
    <div class="content_top">
      <el-card class="course-card" shadow="never">
        <div class="course-header">
          <!-- 封面 -->
          <div class="course-cover">
            <el-image
              :src="ImageThumbnail(url, '280x')"
              :zoom-rate="1.2"
              :max-scale="7"
              :min-scale="0.2"
              :preview-src-list="srcList"
              :hide-on-click-modal="true"
              show-progress
              :initial-index="4"
              fit="cover"
              class="cover-image"
            />
          </div>
          <!-- 课程信息 -->
          <div class="course-info">
            <div class="course-title">
              <h2>
                {{
                  courseInfoConfig.find(item => item.id === "courseName")
                    ?.value ||
                  courseName ||
                  "--"
                }}
              </h2>
            </div>

            <!-- 课程亮点标签 -->
            <div class="course-tags">
              <template
                v-if="tableHeader[4].value && tableHeader[4].value !== '--'"
              >
                <el-tag
                  v-for="tag in tableHeader[4].value.split('、')"
                  :key="tag"
                  type="primary"
                  effect="light"
                  class="tag-item"
                  size="small"
                >
                  {{ tag }}
                </el-tag>
              </template>
            </div>

            <!-- 课程简介 -->
            <div class="course-intro">
              <div
                v-if="tableHeader[6].value && tableHeader[6].value !== '--'"
                class="intro-label"
              >
                简介：
              </div>
              <div class="intro-content">
                <p v-if="tableHeader[6].value && tableHeader[6].value !== '--'">
                  {{ tableHeader[6].value }}
                </p>
              </div>
            </div>

            <div class="course-meta">
              <div
                v-for="(row, rowIndex) in courseMetaRows"
                :key="rowIndex"
                class="meta-row"
              >
                <div v-for="config in row" :key="config.id" class="meta-item">
                  <span class="meta-label">{{ config.label }}：</span>
                  <span
                    class="meta-value"
                    :style="
                      config.style
                        ? config.style({ freeze: tableHeader[5].value })
                        : {}
                    "
                  >
                    {{ config.value }}
                  </span>
                </div>
              </div>
            </div>
          </div>
          <!-- 操作按钮 -->
          <div class="course-actions-right">
            <el-button
              @click="
                router.push({
                  path: '/course/management/allEvaluate',
                  query: { courseId: route.query.id }
                })
              "
            >
              查看全部评价
            </el-button>
            <!-- 其他操作按钮可按需添加 -->
          </div>
        </div>
      </el-card>
    </div>
    <!-- 课期管理区域 -->
    <div class="content_bottom">
      <div class="content-top-actions">
        <div class="actions-title">
          <p>课期列表</p>
        </div>
        <div class="actions-buttons">
          <!-- 可按需添加“新建课期”等按钮 -->
        </div>
      </div>
      <el-divider style="margin: 14px 0 20px 0" />
      <!-- 搜索表单 -->
      <div class="con_search">
        <el-form :model="form" :inline="true">
          <el-form-item label="开课时间">
            <el-date-picker
              v-model="pickTime"
              type="daterange"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              value-format="x"
              @change="timeChange"
              @clear="clearEvt('time')"
            />
          </el-form-item>
          <el-form-item label="购买类型">
            <el-select
              v-model="form.buyType"
              style="width: 120px"
              placeholder="请选择"
              value-key="id"
            >
              <el-option
                v-for="item in typeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="领队">
            <el-select
              v-model="form.leadersId"
              style="width: 120px"
              placeholder="请选择领队"
              value-key="id"
            >
              <el-option
                v-for="item in leaderOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="讲师">
            <el-select
              v-model="form.lecturersId"
              style="width: 120px"
              placeholder="请选择讲师"
              value-key="id"
            >
              <el-option
                v-for="item in teacherOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="课期状态">
            <el-select
              v-model="form.coursePeriodState"
              style="width: 120px"
              placeholder="请选择"
              :empty-values="[null, undefined]"
              :value-on-clear="null"
            >
              <el-option
                v-for="item in stateOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="审核状态">
            <el-select
              v-model="form.reviewState"
              style="width: 120px"
              placeholder="请选择"
              :empty-values="[null, undefined]"
              :value-on-clear="null"
            >
              <el-option
                v-for="item in auditOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label=" ">
            <div class="flex">
              <el-button
                type="primary"
                style="margin-right: 10px"
                @click="searchData"
              >
                搜索
              </el-button>
              <el-button @click="setData">重置</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <!-- 表格区域 -->
      <div class="con_table">
        <el-table
          :data="tableData"
          table-layout="fixed"
          :header-cell-style="{ backgroundColor: '#fafafa', color: '#565353' }"
          highlight-current-row
          height="100%"
        >
          <el-table-column
            prop="termNumber"
            label="期号"
            min-width="100"
            align="left"
            fixed
          >
            <template #default="scope">
              {{ scope.row.termNumber || "0" }}
            </template>
          </el-table-column>
          <el-table-column
            prop="name"
            label="课期名"
            width="320"
            align="left"
            show-overflow-tooltip
            fixed
          >
            <template #default="scope">
              {{ scope.row.name || "--" }}
            </template>
          </el-table-column>
          <el-table-column
            width="120px"
            prop="coursePeriodState"
            label="课期状态"
          >
            <template #default="scope">
              <div
                v-if="
                  scope.row.offlineType === 'PLATFORM_OFFLINE' ||
                  scope.row.offlineType === 'PLATFORM_CLOSE_GROUP'
                "
              >
                <el-tooltip
                  class="box-item"
                  title=""
                  :content="
                    scope.row.offlineType === 'PLATFORM_OFFLINE'
                      ? '课期已被强制下架，请联系平台客服了解情况'
                      : '课期已被强制取消定制，请联系平台客服了解情况'
                  "
                  placement="bottom"
                  effect="light"
                >
                  <div
                    class="state-reject"
                    :style="{
                      color: getCoursePeriodStateColor(
                        scope.row.coursePeriodState
                      )
                    }"
                  >
                    {{ getSatte(scope.row.coursePeriodState) || "--" }}
                    <el-icon style="color: red; margin-left: 3px">
                      <Warning />
                    </el-icon>
                  </div>
                </el-tooltip>
              </div>
              <div
                v-else
                class="state-reject"
                :style="{
                  color: getCoursePeriodStateColor(scope.row.coursePeriodState)
                }"
              >
                {{ getSatte(scope.row.coursePeriodState) || "--" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            width="120px"
            prop="auditState"
            label="审核状态"
            align="left"
          >
            <template #default="scope">
              <div
                v-if="
                  scope.row.reviewState === 'OFFLINE_REJECT' ||
                  scope.row.reviewState === 'ONLINE_REJECT'
                "
              >
                <el-tooltip
                  class="box-item"
                  title=""
                  :content="scope.row.opinion ? scope.row.opinion : '无'"
                  placement="bottom"
                  effect="light"
                >
                  <div
                    class="state-reject"
                    :style="{
                      color: getAuditStateColor(scope.row.reviewState)
                    }"
                  >
                    {{ AUDIT_ENUM[scope.row.reviewState]?.label || "无" }}

                    <el-icon style="color: red; margin-left: 3px">
                      <Warning />
                    </el-icon>
                  </div>
                </el-tooltip>
              </div>
              <div
                v-else
                class="state-reject"
                :style="{ color: getAuditStateColor(scope.row.reviewState) }"
              >
                {{ AUDIT_ENUM[scope.row.reviewState]?.label || "无" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="leaders"
            label="领队"
            align="left"
            width="120"
            show-overflow-tooltip
          >
            <template #default="scope">
              {{ getName(scope.row.leaders) || "--" }}
            </template>
          </el-table-column>
          <el-table-column
            prop="lecturers"
            label="讲师"
            align="left"
            width="120"
            show-overflow-tooltip
          >
            <template #default="scope">
              <div>
                {{ getName(scope.row.lecturers) || "--" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="openTime"
            label="开课时间"
            align="left"
            min-width="180"
          >
            <template #default="scope">
              <div>
                {{
                  formatTime(scope.row.openTime, "YYYY-MM-DD HH:mm:ss") || "--"
                }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="createdAt"
            label="创建时间"
            align="left"
            min-width="180"
          >
            <template #default="scope">
              <div>
                {{
                  formatTime(scope.row.createdAt, "YYYY-MM-DD HH:mm:ss") || "--"
                }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="buyType" label="购买类型" align="left">
            <template #default="scope">
              <div>
                {{
                  scope.row.buyType === "ORDINARY" ? "普通单" : "团购单" || "--"
                }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="address"
            fixed="right"
            label="操作"
            align="left"
            width="200px"
          >
            <template #default="{ row }">
              <div class="option">
                <div class="btnse" @click="detailEvt(row.id)">详情</div>

                <div
                  v-if="
                    row.coursePeriodState === 'ONLINE' &&
                    row.buyType === 'ORDINARY'
                  "
                  class="btnse"
                  @click="removeEvt(row, true)"
                >
                  下架
                </div>
                <div
                  v-else-if="
                    row.coursePeriodState === 'ONLINE' &&
                    row.buyType !== 'ORDINARY'
                  "
                  class="btnse"
                  @click="closeGroup(row)"
                >
                  取消定制
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 分页 -->
      <div class="con_pagination">
        <!-- 分页 -->
        <el-pagination
          v-model:current-page="params.page"
          v-model:page-size="params.size"
          class="pagination"
          background
          :page-sizes="[15, 30, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="params.totalElements"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.containers {
  box-sizing: border-box;
  width: calc(100% - 48px);
  height: 88vh;
  // padding: 24px;
  background: #f0f2f5;
  display: flex;
  flex-direction: column;

  .content_top {
    box-sizing: border-box;
    padding: 20px;
    background-color: #fff;
    flex-shrink: 0;
    .btns {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      margin-bottom: 20px;
    }

    .con_top {
      position: relative;
      display: flex;
      align-items: center;
      //   justify-content: flex-end;
      width: 100%;
      height: fit-content;
      // margin-bottom: 24px;

      .copy-select {
        position: absolute;
        top: 33px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 90px;
        height: 70px;
        background-color: #fff;
        border: 1px solid #a8b7bd;
        border-radius: 5px;

        p {
          margin-bottom: 5px;
          font-size: 14px;
          cursor: pointer;
        }

        p:hover {
          color: #409eff;
        }
      }
    }

    .tabHeastyle {
      display: flex;
      // align-items: flex-end;
      // justify-content: space-between;
      .courseName {
        min-width: 145px;
        max-width: 146px;
        // font-weight: 600;
      }

      .img {
        width: 145px;
        // height: 100px;
        height: 85px;
        margin: 0 17px;

        .img-pic {
          width: 145px;
          height: 85px;
          // object-fit: cover;
        }
      }
    }

    .tabtn {
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      height: 80px;
      margin-right: 100px;
    }
  }

  .content_bottom {
    box-sizing: border-box;
    padding: 20px;
    margin-top: 20px;
    background-color: #fff;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  :deep(.el-button + .el-button) {
    margin: 0;
  }

  // :deep(.el-button--primary) {
  //   width: 100px;
  // }
}

.con_search {
  display: flex;
  align-items: center;
  width: 100%;
  height: fit-content;
  // margin-top: 40px;

  .btn_search {
    display: flex;
    justify-content: space-between;
    width: 140px;
  }
}
.state-reject {
  width: 40px;
  display: flex;
  // justify-content: center;
  align-items: center;
  white-space: nowrap;
}
:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  background: #e1f5ff;
}

.con_table {
  // width: calc(100% - 25px);
  // min-height: 500px;
  margin-bottom: 24px;
  flex: 1;
  overflow: hidden;

  // margin-left: 25px;
  .option {
    display: flex;
    // justify-content: center;

    .btnse {
      display: flex;
      margin-right: 16px;
      color: #409eff;
      cursor: pointer;
    }
    .nofreeze {
      color: #f56c6c;
      cursor: pointer;
      // margin-right: 16px;
    }
    .btnse1 {
      // display: flex;
      // margin-right: 16px;
      color: #f56c6c;
      cursor: pointer;
    }
  }
}

.con_pagination {
  display: flex;
  justify-content: flex-end;
  width: 100%;
}
:deep(.el-form--inline .el-form-item) {
  margin-right: 32px !important;
}
.course-card {
  border-radius: 0;
  border: none;
  :deep(.el-card__body) {
    padding: 0;
  }
  .course-header {
    display: flex;
    gap: 36px;

    .course-cover {
      display: flex;
      flex-shrink: 0;
      .cover-image {
        width: 280px;
        height: 180px;
        border-radius: 8px;
        overflow: hidden;
        position: relative;

        :deep(.el-image__inner) {
          width: 100% !important;
          height: auto !important;
          position: absolute;
          top: 50%;
          left: 0;
          transform: translateY(-50%);
          object-fit: cover;
          border-radius: 8px !important;
        }
      }
    }

    .course-info {
      flex: 1;
      .course-title {
        margin-bottom: 12px;
        h2 {
          font-size: 24px;
          font-weight: 600;
          color: #1a1a1a;
          margin: 0;
          line-height: 1.3;
        }
      }
      .course-intro {
        margin-bottom: 24px;
        min-height: 23px; // 确保固定高度，即使没有简介内容
        display: flex;
        align-items: flex-start;

        .intro-label {
          width: 50px;
          color: #666;
          font-size: 14px;
          flex-shrink: 0;
          line-height: 1.5;
          margin-top: 2px;
        }

        .intro-content {
          flex: 1;

          p {
            margin: 0;
            color: #333;
            font-size: 14px;
            line-height: 1.6;
            word-break: break-all;
            text-align: justify; // 两端对齐
          }
        }
      }
      .course-tags {
        margin-bottom: 4px;
        min-height: 24px; // 确保固定高度，即使没有标签

        .tag-item {
          margin-right: 8px;
          margin-bottom: 8px;
        }
      }
      .course-meta {
        display: flex;
        flex-direction: column;
        gap: 1rem;

        .meta-row {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 16px 32px;
          align-items: center;
          margin-bottom: 0;

          .meta-item {
            display: flex;
            align-items: flex-start;

            .meta-label {
              width: 90px;
              color: #666;
              font-size: 14px;
              flex-shrink: 0;
              text-align: left;
              line-height: 1.5;
            }
            .meta-value {
              color: #333;
              font-size: 14px;
              font-weight: 500;
              flex: 1;
              min-width: 80px;
              word-break: break-all;
              line-height: 1.5;
            }
          }
        }
      }
    }
    .course-actions-right {
      display: flex;
      align-self: flex-end;
      flex-wrap: wrap;
    }
  }
}

.content-top-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .actions-title {
    p {
      margin: 0;
      font-size: 16px;
      font-weight: 700;
      color: #303133;
    }
  }
  .actions-buttons {
    display: flex;
  }
}
</style>
