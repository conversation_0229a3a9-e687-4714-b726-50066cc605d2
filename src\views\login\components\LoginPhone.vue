<script setup lang="ts">
import type { FormInstance } from "element-plus";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { $t, transformI18n } from "@/plugins/i18n";
import { useUserStoreHook } from "@/store/modules/user";
import { message } from "@/utils/message";
import Iphone from "@iconify-icons/ep/iphone";
import { reactive, ref, toRaw } from "vue";
import { useI18n } from "vue-i18n";
import Motion from "../utils/motion";
import { phoneRules } from "../utils/rule";
import { useVerifyCode } from "../utils/verifyCode";
import { getLoginPhone } from "@/api/user";
import { encryption } from "@/utils/SM4.js";
import { getTopMenu, initRouter } from "@/router/utils";
import { useRouter } from "vue-router";

const { t } = useI18n();
const loading = ref(false);
const ruleForm = reactive({
  phone: "",
  codeType: "LOGIN",
  code: ""
});
const ruleFormRef = ref<FormInstance>();
const { isDisabled, text } = useVerifyCode();
const router = useRouter();

const onLogin = async (formEl: FormInstance | undefined) => {
  loading.value = true;
  if (!formEl) return;
  await formEl.validate(async valid => {
    if (valid) {
      const params = {
        phone: encryption(ruleForm.phone),
        code: ruleForm.code
      };

      try {
        await useUserStoreHook()
          .loginByCode(params)
          .then(res => {
            return initRouter().then(() => {
              loading.value = true;
              router
                .push(getTopMenu(true).path)
                .then(() => {
                  message(t("login.pureLoginSuccess"), { type: "success" });
                })
                .finally(() => (loading.value = false));
            });
          })
          .catch(err => {
            if (err[0] === 10015) {
              message(t("login.pureLoglock"), { type: "error" });
            } else {
              message(t("login.pureLoginagain"), { type: "error" });
            }
          })
          .finally(() => (loading.value = false));
        loading.value = false;
      } catch (err) {
        console.log(err);
        message("登录失败，请重试", { type: "error" });
        loading.value = false;
      }
    } else {
      loading.value = false;
    }
  });
};

function onBack() {
  useVerifyCode().end();
  useUserStoreHook().SET_CURRENTPAGE(0);
}
</script>

<template>
  <el-form ref="ruleFormRef" :model="ruleForm" :rules="phoneRules" size="large">
    <Motion>
      <el-form-item prop="phone">
        <el-input
          v-model="ruleForm.phone"
          clearable
          :placeholder="t('login.purePhone')"
          :prefix-icon="useRenderIcon(Iphone)"
        />
      </el-form-item>
    </Motion>

    <Motion :delay="100">
      <el-form-item prop="code">
        <div class="w-full flex justify-between">
          <el-input
            v-model="ruleForm.code"
            clearable
            :placeholder="t('login.pureSmsVerifyCode')"
            :prefix-icon="useRenderIcon('ri:shield-keyhole-line')"
          />
          <el-button
            :disabled="isDisabled"
            class="ml-2"
            @click="
              useVerifyCode().start(ruleFormRef, 'phone', toRaw(ruleForm))
            "
          >
            {{
              text.length > 0
                ? text + t("login.pureInfo")
                : t("login.pureGetVerifyCode")
            }}
          </el-button>
        </div>
      </el-form-item>
    </Motion>

    <Motion :delay="150">
      <el-form-item>
        <el-button
          class="w-full"
          size="default"
          type="primary"
          :loading="loading"
          @click="onLogin(ruleFormRef)"
        >
          {{ t("login.pureLogin") }}
        </el-button>
      </el-form-item>
    </Motion>

    <Motion :delay="200">
      <el-form-item>
        <div class="w-full h-[20px] flex justify-between items-center">
          <el-button class="w-full mt-4" size="default" @click="onBack">
            <!-- {{ t("login.pureBack") }} -->
            {{ "账号登录" }}
          </el-button>
          <el-button
            class="w-full mt-4 ml-2"
            size="default"
            @click="useUserStoreHook().SET_CURRENTPAGE(2)"
          >
            {{ t("login.pureQRCodeLogin") }}
          </el-button>
        </div>
      </el-form-item>
    </Motion>
  </el-form>
</template>
