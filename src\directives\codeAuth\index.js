/**
 * 权限码校验指令
 * 用于按钮级别的权限控制
 * 使用方式：v-code="['101', '102']"
 */

// 检查当前用户是否拥有权限码
const hasCode = codeList => {
  const authData = localStorage.getItem("authorityDTOS");
  if (!authData) return false;

  try {
    const { authorityDTOS } = JSON.parse(authData);
    if (!authorityDTOS || !authorityDTOS.length) return true;

    const findCode = (list, code) => {
      for (const item of list) {
        if (item.idCode === code) return true;

        if (item.children && item.children.length) {
          if (findCode(item.children, code)) return true;
        }
      }
      return false;
    };

    if (Array.isArray(codeList)) {
      return codeList.some(code =>
        authorityDTOS.some(item => findCode([item], code))
      );
    } else if (typeof codeList === "string") {
      return authorityDTOS.some(item => findCode([item], codeList));
    }

    return false;
  } catch (error) {
    console.error("权限码校验异常:", error);
    return false;
  }
};

export const code = {
  mounted(el, binding) {
    const { value } = binding;
    if (value) {
      !hasCode(value) && el.parentNode?.removeChild(el);
    } else {
      throw new Error(
        "[Directive: code]: 需要指定权限码! 例如: v-code=\"['101','102']\""
      );
    }
  }
};
