import { http } from "@/utils/http";

/*  专家库  */
// 分页查询
export const teacherDatabaseFindAll = params => {
  return http.request(
    "get",
    "/platform/teacherDatabase/findAll",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};

// 删除
export const teacherDatabaseDelete = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/teacherDatabase/delete",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ACCOUNT_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};

// Excel导入师资库信息
export const teacherDatabaseImportExcel = data => {
  return http.request(
    "post",
    "/platform/teacherDatabase/importExcel",
    { data },
    { isNeedToken: true, isNeedEncrypt: false }
  );
};
