.wave {
  position: fixed;
  /* max-width: 229px; */
  max-height: 36px;
  left: 48px;
  top: 24px;
  z-index: -1;
}

.login-container {
  width: 100vw;
  height: 100vh;
  max-width: 100%;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  /* grid-gap: 18rem; */
  padding: 0 2rem;
}

.img {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-left: 14rem;
}

.img img {
  width: 817px;
  height: 717px;
  /* min-width: 417px;
  min-height: 317px; */
  object-fit: contain;
}

.login-box {
  display: flex;
  /* align-items: center; */
  text-align: center;
  overflow: hidden;
  justify-content: space-around;
  margin-top: 24vh;
}

.login-form {
  width: 360px;
}

.avatar {
  width: 350px;
  height: 80px;
}

.login-form h2 {
  text-transform: uppercase;
  text-align: left;
  margin: 45px 0;
  color: #999;
  font:
    bold 200% Consolas,
    Monaco,
    monospace;
}

@media screen and (max-width: 1500px) {
  .login-container {
    grid-gap: 9rem;
  }

  .login-form {
    width: 290px;
  }

  .login-form h2 {
    font-size: 2.4rem;
    margin: 8px 0;
  }

  .img img {
    width: 360px;
    /* display: none; */
  }

  .avatar {
    width: 280px;
    height: 80px;
  }
}

@media screen and (max-width: 1200px) {
  .wave {
    display: none;
  }

  .img {
    display: none;
  }

  .login-container {
    grid-template-columns: 1fr;
  }

  .login-box {
    justify-content: center;
    margin-top: 0;

    .register {
      position: relative;
      right: auto;
    }
  }
}
