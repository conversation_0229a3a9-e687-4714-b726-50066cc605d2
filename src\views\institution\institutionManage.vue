<script setup>
import { onMounted, ref, onActivated, nextTick } from "vue";
import { formatTime } from "@/utils/index";
import {
  courseFindId,
  courseFindAll,
  isFreeze,
  updateFeeRatio
} from "@/api/institution";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
defineOptions({
  name: "InstitutionManage"
});

onActivated(() => {
  getTableList();
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);
});
onMounted(() => {
  // courseTypeFindApi();
  getTableList();
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);
});

// 计算表格高度
const searchFormHeight = ref(0);
const tableHeight = ref("calc(100vh - 280px)");

const calculateTableHeight = async () => {
  await nextTick();
  const searchForm = document.querySelector(".con_search");
  if (searchForm) {
    searchFormHeight.value = searchForm.offsetHeight;
    // 动态计算表格高度，减去表单高度、页面其他元素高度和边距
    tableHeight.value = `calc(100vh - 290px - ${searchFormHeight.value}px)`;
  }
};

const router = useRouter();
// 搜索表单
const form = ref({
  startTime: "",
  endTime: "",
  name: "",
  organizationName: "",
  courseTypeName: "",
  freeze: "all"
});
// 表格数据
const tableData = ref([]);
const params = ref({
  page: 1,
  size: 15,
  sort: "createdAt,desc",
  totalElements: 0
});
const courseTypeoptions = ref([
  {
    value: 0,
    label: "全部"
  }
]);
const freezeList = ref([
  { label: "全部", value: "all" },
  { label: "正常", value: "false" },
  { label: "冻结", value: "true" }
]);
const courseTypeFindApi = async () => {
  const params = {
    depth: 1
  };
  // let [err, res] = await requestTo(courseTypeFind(params));
  // console.log("🐠res------------------------------>", res);
  // let res1 = res.map(it => {
  //   return {
  //     ...it,
  //     label: it.name,
  //     value: it.id
  //   };
  // });
  // courseTypeoptions.value = courseTypeoptions.value.concat(res1);
  // console.log(
  //   "🌵courseTypeoptions.value------------------------------>",
  //   courseTypeoptions.value
  // );
};
// 获取列表信息
const getListLoading = ref(false);
const getTableList = async item => {
  if (getListLoading.value) return;
  // return;
  getListLoading.value = true;
  let paramsData = {
    page: params.value.page - 1,
    size: params.value.size,
    sort: params.value.sort
  };
  for (const paramsDataKey in form.value) {
    let isArray = Array.isArray(form.value[paramsDataKey]);
    if (isArray) {
      if (form.value[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    } else {
      if (paramsDataKey === "freeze") {
        if (form.value[paramsDataKey] !== "all") {
          paramsData[paramsDataKey] = form.value[paramsDataKey];
        }
      } else if (form.value[paramsDataKey]) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    }
  }
  console.log("🍧-----paramsData---搜索条件--", paramsData);
  const { code, data, msg } = await courseFindAll(paramsData);
  console.log("🎁-----result-----", data);
  if (data) {
    tableData.value = data?.content;

    params.value.totalElements = data.totalElements;
    await nextTick();
    calculateTableHeight();
  }
  getListLoading.value = false;
};
//分页
const handleCurrentChange = e => {
  params.value.page = e;
  getTableList();
};
const handleSizeChange = val => {
  params.value.page = 1;
  params.value.size = val;
  getTableList();
};
// 编辑
const edit = item => {
  // router.push({ path: "/institution/baseEdit", query: { data: JSON.stringify(item) } });
  router.push({
    path: "/institution/baseEdit",
    query: { id: item.id }
  });
};
// 账务
const getId = item => {
  router.push({
    path: "/institution/accounting",
    query: { name: item.name, id: item.id }
  });
};
//搜索
const searchData = () => {
  params.value.page = 1;
  getTableList();
};
// 重置
const setData = () => {
  form.value = {};
  params.value.page = 1;
  value1.value = [];
  form.value.freeze = "all";
  getTableList();
};
// 选择时间
const timeChange = async value => {
  if (!value || value.length !== 2) return;

  // 开始时间（默认 00:00:00）
  form.value.startTime = new Date(value[0]).getTime();

  // 结束时间（设置为 23:59:59.999）
  const endDate = new Date(value[1]);
  endDate.setHours(23, 59, 59, 999); // 关键修改
  form.value.endTime = endDate.getTime();
  await nextTick();
  calculateTableHeight();
};

const value1 = ref([]);
// 前往创建
const goSet = () => {
  router.push({
    path: "/institution/baseAdd",
    query: { title: "jsMang", id: 1 }
  });
};
// 冻结/解冻
const getButtonText = isPub => {
  return isPub === true ? "解冻" : "冻结";
};
const Freeze = async row => {
  console.log("🍭-----row-----", row);
  const isFreezing = !row.freeze;
  const confirmText = isFreezing
    ? "你确定要冻结该机构吗?"
    : "你确定要解冻该机构吗?";
  const confirmTitle = isFreezing ? "确定冻结" : "确定解冻";
  const successMessage = isFreezing ? "已冻结" : "已解冻";

  try {
    await ElMessageBox.confirm(confirmText, confirmTitle, {
      confirmButtonText: "确定",
      cancelButtonText: "取消"
      // type: "warning"
    });

    const params = {
      id: row.id,
      freeze: isFreezing
    };
    const operateLog = {
      operateLogType: "ORGANIZATIONAL_MANAGEMENT",
      operateType: isFreezing ? "冻结了" : "解冻了",
      operatorTarget: `“${row.name}”的机构状态`
    };
    const { code, msg } = await isFreeze(params, operateLog);

    if (code === 200) {
      ElMessage({
        message: successMessage,
        type: "success"
      });

      getTableList();
    } else {
      ElMessage.error(msg);
    }
  } catch (error) {
    // console.log("操作取消");
  }
};
const defaultTime = [
  new Date(2024, 4, 1, 0, 0, 0),
  new Date(2024, 4, 1, 23, 59, 59)
];

// 抽成比例相关
const proportionDialogVisible = ref(false);
const currentInstitution = ref(null);
const proportionValue = ref("");
const confirmProportionLoading = ref(false);

// 在输入时限制比例到 0-100，且保留 1 位小数
const handleProportionInput = value => {
  if (value === "" || value === null || value === undefined) {
    proportionValue.value = "";
    return;
  }
  let num = Number(value);
  if (Number.isNaN(num)) {
    proportionValue.value = "";
    return;
  }
  if (num < 0) num = 0;
  if (num > 100) num = 100;
  num = Math.round(num * 10) / 10; // 保留 1 位小数
  proportionValue.value = String(num);
};

// 设置抽成比例（入口已从操作列移除，如需开启请在新入口调用）
const setProportion = row => {
  currentInstitution.value = row;
  proportionValue.value = row.serviceFeeRatio
    ? (row.serviceFeeRatio * 100).toFixed(1)
    : "";
  proportionDialogVisible.value = true;
};

// 确认设置比例（移除范围校验拦截，输入阶段已限制）
const confirmProportion = async () => {
  if (confirmProportionLoading.value) return;
  if (
    proportionValue.value === "" ||
    proportionValue.value === null ||
    proportionValue.value === undefined
  ) {
    ElMessage.error("请输入数值");
    return;
  }

  const value = Number(proportionValue.value);

  try {
    confirmProportionLoading.value = true;
    // 调用后端接口更新服务费比例
    const params = {
      organizationId: currentInstitution.value.id,
      serviceFeeRatio: value / 100 // 将百分比转换为小数
    };

    const operateLog = {
      operateLogType: "ORGANIZATIONAL_MANAGEMENT",
      operateType: "修改了",
      operatorTarget: `"${currentInstitution.value.name}"的服务费抽成比例为${value}%`
    };

    const { code, msg } = await updateFeeRatio(params, operateLog);

    if (code === 200) {
      ElMessage.success("抽成比例设置成功");
      proportionDialogVisible.value = false;
      proportionValue.value = "";
      currentInstitution.value = null;
      // 更新列表数据：通过接口重新查询，确保显示为后端数据
      getTableList();
    } else {
      ElMessage.error(msg || "设置失败");
    }
  } catch (error) {
    console.error("设置抽成比例失败:", error);
    ElMessage.error("设置失败，请重试");
  } finally {
    confirmProportionLoading.value = false;
  }
};

// 取消设置比例
const cancelProportion = () => {
  proportionDialogVisible.value = false;
  proportionValue.value = "";
  currentInstitution.value = null;
};

// 获取抽成比例显示文案（数据列使用）
const getProportionText = row => {
  return row.serviceFeeRatio !== undefined && row.serviceFeeRatio !== null
    ? `${(row.serviceFeeRatio * 100).toFixed(1)}%`
    : "--";
};
</script>

<template>
  <div class="page-container">
    <div class="common">
      <div class="con_search">
        <el-form :model="form" label-width="70px" :inline="true">
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="value1"
              type="daterange"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              :default-time="defaultTime"
              @change="timeChange"
            />
          </el-form-item>
          <el-form-item label="机构" class="flex_item">
            <el-input v-model.trim="form.name" placeholder="请输入" clearable />
          </el-form-item>
          <el-form-item label="机构状态">
            <el-select
              v-model="form.freeze"
              placeholder="请选择"
              style="width: 100px"
              clearable
            >
              <el-option
                v-for="item in freezeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="机构">
            <el-input
              v-model="form.organizationName"
              placeholder="请输入"
              clearable
            />
          </el-form-item> -->
          <!-- <el-form-item label="课程类型">
            <el-select
              v-model="form.courseTypeName"
              style="width: 120px"
              placeholder="请选择"
              :empty-values="[null, undefined]"
              :value-on-clear="null"
            >
              <el-option
                v-for="item in courseTypeoptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item> -->

          <el-form-item label=" ">
            <div class="flex">
              <el-button type="primary" @click="searchData">搜索</el-button>
              <el-button @click="setData">重置</el-button>
              <el-button type="primary" @click="goSet">新建机构</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <div class="containers">
      <el-scrollbar :style="{ height: tableHeight }">
        <div class="con_table">
          <el-table
            :data="tableData"
            :max-height="tableHeight"
            :header-cell-style="{
              background: 'var(--el-fill-color-light)',
              color: 'var(--el-text-color-primary)'
            }"
          >
            <el-table-column prop="name" label="机构名" min-width="120">
              <template #default="scope">
                <el-text class="" truncated>
                  {{ scope.row.name || "--" }}
                </el-text>
              </template>
            </el-table-column>
            <el-table-column width="200px" prop="createdAt" label="创建时间">
              <template #default="scope">
                <div>
                  {{
                    formatTime(scope.row?.createdAt, "YYYY-MM-DD HH:mm") || "--"
                  }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="organizationName"
              label="机构别名"
              align="left"
            >
              <template #default="scope">
                <div>
                  {{ scope.row.alias || "--" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="termNumber" label="管理员" align="left">
              <template #default="scope">
                <div>
                  {{ scope.row.organizationAdmin.name || "--" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="freeze" label="机构状态" align="left">
              <template #default="scope">
                <div
                  :style="{
                    color: scope.row.freeze === true ? 'red' : ''
                  }"
                >
                  {{ scope.row.freeze === true ? "冻结" : "正常" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="serviceFeeRatio"
              label="抽成比例"
              align="left"
              min-width="120"
            >
              <template #default="scope">
                <div>
                  {{ getProportionText(scope.row) }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              fixed="right"
              width="260"
              label="操作"
              align="left"
            >
              <template #default="scope">
                <div class="button">
                  <div v-code="['616']" class="btnse" @click="edit(scope.row)">
                    编辑
                  </div>
                  <div class="btnse" @click="getId(scope.row)">账务</div>
                  <el-button
                    type="primary"
                    link
                    :style="{
                      color: scope.row.freeze === true ? 'red' : '#409EFF'
                    }"
                    @click="Freeze(scope.row)"
                  >
                    {{ getButtonText(scope.row.freeze) }}
                  </el-button>
                  <div class="btnse" @click="setProportion(scope.row)">
                    设置抽成比例
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-scrollbar>

      <div class="con_pagination">
        <!-- 分页 -->
        <el-pagination
          v-model:current-page="params.page"
          v-model:page-size="params.size"
          class="pagination"
          background
          :page-sizes="[15, 30, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="params.totalElements"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>

    <!-- 设置抽成比例弹窗 -->
    <el-dialog
      v-model="proportionDialogVisible"
      title="设置机构抽成比例"
      width="400px"
      :before-close="cancelProportion"
    >
      <div class="proportion-dialog">
        <div class="dialog-content">
          <p class="dialog-tip">请输入抽成比例：</p>
          <el-input
            v-model="proportionValue"
            type="number"
            placeholder="请输入0-100之间的数值"
            :min="0"
            :max="100"
            style="width: 100%"
            @input="handleProportionInput"
          >
            <template #append>%</template>
          </el-input>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelProportion">取消</el-button>
          <el-button
            type="primary"
            :loading="confirmProportionLoading"
            @click="confirmProportion"
            >
确定
</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.page-container {
  display: flex;
  flex-direction: column;
  height: 86vh;
  overflow: hidden;
}

.scrollbar {
  background-color: #fff;
}

:deep(.el-table .cell) {
  padding: 0;
}

.common {
  // width: 100%;
  // height: 100%;
  padding: 20px 20px 2px 20px;
  background-color: #fff;
  margin-bottom: 20px;
  .con_search {
    display: flex;
    align-items: center;
    width: 100%;
    height: fit-content;
  }
}

.containers {
  box-sizing: border-box;
  // width: calc(100% - 48px);
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 20px;
  background: #fff;
  overflow: hidden;

  .con_top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: fit-content;
    margin-bottom: 24px;

    .titles {
      font-size: 20px;
      font-weight: bold;
      color: #606266;
    }
  }

  .con_search {
    display: flex;
    align-items: center;
    width: 100%;
    height: fit-content;
    margin-bottom: 24px;
    .el-form--inline .el-form-item {
      margin-right: 0;
    }
  }

  .con_table {
    // width: calc(100% - 25px);
    // margin-bottom: 24px;
    // margin-left: 25px;
    .button {
      display: flex;
      // justify-content: space-around;
      .btnse {
        color: #409eff;
        cursor: pointer;
        padding: 0 5px;
      }
    }
  }

  .con_pagination {
    display: flex;
    justify-content: flex-end;
    width: 100%;
    margin-top: 20px;
    padding: 10px 20px;
    background-color: #fff;
  }
}
.flex_item {
  margin-left: -25px !important;
}

// 抽成比例弹窗样式
.proportion-dialog {
  .dialog-content {
    padding: 20px 0;

    .dialog-tip {
      margin-bottom: 15px;
      font-size: 14px;
      color: #606266;
      font-weight: 500;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
