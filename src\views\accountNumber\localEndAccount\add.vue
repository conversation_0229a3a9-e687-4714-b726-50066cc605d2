<script setup>
import { ref, reactive, onMounted, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import {
  localEndAdd,
  localEndFindById,
  localEndUpdate
} from "@/api/localEnd.js";
import { decrypt, encryption } from "@/utils/SM4.js";
import { requestTo } from "@/utils/http/tool";
import { to, compareObjects } from "@iceywu/utils";
import { ElMessage } from "element-plus";
import { formatTime, generatePassword } from "@/utils/index.js";
import { Hide, View } from "@element-plus/icons-vue";
const router = useRouter();
const route = useRoute();
const ruleFormRef = ref();
const phoneOther = ref("");
const ruleForm = reactive({
  name: "",
  account: "",
  phone: ""
});

// 手机号校验
const phoneTest = (rule, value, callback) => {
  // console.log('🍭value------------------------------>',value);
  const phoneRegex = /^1[3-9]\d{9}$/;
  if (route.query.type === "create") {
    if (value === "") {
      callback(new Error("手机号不能为空"));
    } else if (!phoneRegex.test(value)) {
      callback(new Error("手机号格式不正确"));
    } else {
      callback();
    }
  } else {
    if (isView.value === true) {
      if (value === "") {
        callback(new Error("手机号不能为空"));
      } else if (!phoneRegex.test(value)) {
        callback(new Error("手机号格式不正确"));
      } else {
        callback();
      }
    } else {
      callback();
    }
  }
};
// 名字校验
const nameTest = (rule, value, callback) => {
  const namePattern = /^[\u4E00-\u9FA5]+$/;
  if (!value) {
    callback(new Error("姓名不能为空"));
  } else if (!namePattern.test(value)) {
    callback(new Error("姓名必须为汉字"));
  } else {
    callback();
  }
};
// 账号校验
const accountTest = (rule, value, callback) => {
  const accountPattern = /^(adm[in]{1,2}|root|sysadm|[\w-]*(admin)[\w-]*)$/;
  if (!value) {
    callback(new Error("账号不能为空"));
  } else if (accountPattern.test(value)) {
    callback(new Error("该用户名不可用，请重新输入"));
  } else {
    callback();
  }
};
const rules = reactive({
  name: [
    { validator: nameTest, trigger: "blur" }
    // { min: 3, max: 5, message: "Length should be 3 to 5", trigger: "blur" }
  ],
  account: [
    // { required: true, message: "请输入账号", trigger: "blur" }
    { validator: accountTest, trigger: "blur" }
    // { min: 3, max: 5, message: "Length should be 3 to 5", trigger: "blur" }
  ],
  phone: [
    { validator: phoneTest, trigger: "blur" }
    // { min: 3, max: 5, message: "Length should be 3 to 5", trigger: "blur" }
  ]
});
const fixData = ref([
  {
    id: "1",
    label: "账号ID",
    value: "--",
    width: "400px"
  },
  {
    id: "2",
    label: "创建时间",
    value: "--",
    width: "400px"
  }
]);
const formData = ref([
  {
    label: "姓名",
    type: "input",
    prop: "name",
    check: true,
    placeholder: "请输入姓名",
    width: "400px",
    eye: false,
    maxLength: 10
  },
  {
    label: "账号",
    type: "input",
    prop: "account",
    check: true,
    placeholder: "请输入账号",
    width: "400px",
    eye: false,
    maxLength: 20
  },
  {
    label: "手机号",
    type: "input",
    prop: "phone",
    check: true,
    placeholder: "请输入手机号",
    width: "400px",
    eye: true,
    maxLength: 11
  }
]);
const oldParams = ref({});
const oldParamsOther = ref({});
const paramsArg = ref({});
// 实时更新初始密码
const newPassword = ref("");
watch(
  () => [ruleForm.name, ruleForm.phone],
  ([name, phone]) => {
    // 前置校验
    if (name && name.length >= 2 && phone && /^1[3-9]\d{9}$/.test(phone)) {
      newPassword.value = generatePassword(name, phone);
    } else {
      newPassword.value = "";
    }
  }
);
const submitLoading = ref(false);
// 新增编辑
const submitForm = async formEl => {
  if (!formEl) return;
  if (submitLoading.value) return;
  submitLoading.value = true;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      const newParams = {
        name: ruleForm.name,
        account: ruleForm.account,
        phone: encryption(ruleForm.phone)
      };
      if (route.query.type === "edite") {
        if (ruleForm.phone === phoneOther.value) {
          let editeParams = {
            name: ruleForm.name,
            account: ruleForm.account
          };
          const resParams = compareObjects(oldParamsOther.value, editeParams);
          paramsArg.value = resParams;
        } else {
          const resParams = compareObjects(oldParams.value, newParams);
          paramsArg.value = resParams;
        }
        paramsArg.value.id = Number(route.query.id);
      }
      const operateLog = {
        operateLogType: "ACCOUNT_MANAGEMENT",
        operateType:
          route.query.type === "create"
            ? `创建了${ruleForm.name}的局端账号`
            : `编辑了${ruleForm.name}的局端账号`
        // operatorTarget: form.value.name,
      };
      let resApi =
        route.query.type === "create"
          ? localEndAdd(newParams, operateLog)
          : localEndUpdate(paramsArg.value, operateLog);

      let [err, result] = await to(resApi);
      if (result.code === 200) {
        route.query.type === "create"
          ? ElMessage.success("创建成功")
          : ElMessage.success("修改成功");
        router.push("/account/localend/account");
      } else {
        if (result.code === 70008) {
          ElMessage.error("该手机号已被使用");
          return;
        } else if (result.code === 10016) {
          ElMessage.error("该账号已被使用");
          return;
        }
        route.query.type === "create"
          ? ElMessage.error("创建失败")
          : ElMessage.error("修改失败");
      }
      // console.log("result------------------------------>", result);
      // console.log("submit!");
    } else {
      console.log("error submit!", fields);
    }
    submitLoading.value = false;
  });
};
const isView = ref(false);
const isViewFn = () => {
  isView.value = !isView.value;
  // console.log('🎉 isView.value------------------------------>', isView.value);
  if (isView.value === false) {
    ruleForm.phone = phoneOther.value;
  } else {
    ruleForm.phone = decrypt(oldParams.value.phone);
  }
};
// 输入内容中间去空和数字不能输文字
const inputChange = (value, prop) => {
  const numberWhite = ["phone", "idNumber"];
  if (numberWhite.includes(prop)) {
    const filteredValue = value.replace(/[^\dX]/gi, "");
    ruleForm[prop] = filteredValue.toUpperCase();
  } else {
    ruleForm[prop] = value.replace(/\s/g, "");
  }
};
const editePassword = ref("");
// 查询
const getlocalEndFindById = async () => {
  const params = {
    id: route.query.id
  };
  const [err, res] = await requestTo(localEndFindById(params));
  // console.log("🐬res--------22---------------------->", res);
  if (res) {
    fixData.value[0].value = res?.id || 0;
    fixData.value[1].value = res?.createdAt ? formatTime(res?.createdAt) : "--";
    ruleForm.name = res?.name || "--";
    ruleForm.account = res?.account || "--";
    // ruleForm.phone = decrypt(res?.phoneCt) || "--";
    ruleForm.phone = res?.phone || "--";
    oldParams.value = {
      name: res?.name || "--",
      account: res?.account || "--",
      phone: res?.phoneCt || "--"
    };
    oldParamsOther.value = {
      name: res?.name || "--",
      account: res?.account || "--"
    };
    phoneOther.value = res?.phone;
    if (res?.name && res?.phoneCt) {
      editePassword.value = generatePassword(res?.name, decrypt(res?.phoneCt));
    }

    // ruleForm.phone = decrypt(res?.phoneCt) || "暂无数据";
    // console.log('🌳 tableData.value------------------------------>',tableData.value);
  }
  if (err) {
    console.log("err------------------------------>", err);
  }
};
onMounted(() => {
  if (route.query.id) {
    getlocalEndFindById();
  }
});
</script>

<template>
  <div class="localendAccount-add">
    <!-- <div class="title">
      <span class="title-text" @click="router.go(-1)">局端账号</span>
      <span class="line">\</span>
      <span class="create-title">创建账号</span>
    </div> -->
    <div class="localendAccount-container">
      <el-descriptions
        v-if="route.query.id"
        class="descrip-info"
        title=""
        :column="2"
        border
        :label-width="'200px'"
      >
        <template v-for="(item, index) in fixData" :key="index">
          <el-descriptions-item label-align="center">
            <template #label>
              <div class="cell-item">{{ item.label }}</div>
            </template>
            {{ item.value }}
          </el-descriptions-item>
        </template>
      </el-descriptions>
      <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules">
        <el-descriptions title="" :column="1" border :label-width="'200px'">
          <el-descriptions-item
            v-for="(item, index) in formData"
            :key="index"
            label-align="center"
          >
            <template #label>
              <span v-if="item.check" class="star">*</span>{{ item.label }}
            </template>
            <el-form-item
              :prop="item.prop"
              :inline-message="item.check"
              style="margin-bottom: 0"
              :show-message="true"
              error-placement="right"
            >
              <!-- input输入 -->
              <template v-if="item.type === 'input'">
                <el-input
                  v-if="item.eye === true && route.query.type === 'edite'"
                  v-model.trim="ruleForm[item.prop]"
                  :placeholder="item.placeholder"
                  :style="{ width: item.width }"
                  :disabled="isView === true ? false : true"
                  :maxlength="item.maxLength"
                  :show-word-limit="item.maxLength"
                  @input="inputChange($event, item.prop)"
                >
                  <template #suffix>
                    <el-icon
                      v-if="isView === true"
                      style="cursor: pointer"
                      @click="isViewFn"
                    >
                      <View />
                    </el-icon>
                    <el-icon v-else style="cursor: pointer" @click="isViewFn">
                      <Hide />
                    </el-icon>
                  </template>
                </el-input>
                <el-input
                  v-else
                  v-model.trim="ruleForm[item.prop]"
                  :placeholder="item.placeholder"
                  :style="{ width: item.width }"
                  :maxlength="item.maxLength"
                  :show-word-limit="item.maxLength"
                  @input="inputChange($event, item.prop)"
                />
              </template>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
        <el-form-item>
          <div v-if="route.query.type === 'create'" class="password-info">
            <span v-if="newPassword || editePassword" class="password">初始密码为： {{ newPassword || editePassword }}</span>
            密码生成规则：姓名首字母+手机号后6位+@
          </div>
        </el-form-item>
      </el-form>
      <div class="buttons">
        <el-button @click="router.go(-1)">取消</el-button>
        <el-button
          type="primary"
          class="create"
          :loading="submitLoading"
          @click="submitForm(ruleFormRef)"
        >
          {{ route.query.type === "create" ? "创建" : "保存" }}
        </el-button>
        <!-- <div class="cancel" @click="router.go(-1)">取消</div> -->
        <!-- <div class="create" @click="submitForm(ruleFormRef)">
          {{ route.query.type === "create" ? "确认创建" : "保存" }}
        </div> -->
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.localendAccount-add {
  box-sizing: border-box;
  // padding: 22px 20px;
  font-size: 14px;
  //   font-family: PingFangSC-regular;
  color: #101010;

  .title {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .title-text {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 76px;
      height: 28px;
      margin-right: 10px;
      cursor: pointer;
      background-color: #f6e5d4;
      border: 1px solid #ff8c19;
    }

    .line {
      margin-right: 20px;
    }
  }

  .localendAccount-container {
    box-sizing: border-box;
    width: 100%;
    height: calc(100vh - 114px);
    padding: 20px 20px;
    background-color: #fff;
    position: relative;
    .descrip-info {
      margin-bottom: 20px;
    }
    .password-info {
      margin-top: 20px;
    }
    .buttons {
      display: flex;
      // justify-content: space-between;
      justify-content: flex-end;
      width: 100%;
      // margin: 0 auto;
      // margin-top: 28vh;
      position: absolute;
      bottom: 30px;
      .create {
        margin-right: 80px;
      }
    }

    .star {
      margin-right: 3px;
      color: red;
    }
    .password {
      margin-right: 20px;
    }
  }
}

:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  width: 240px;
  background: #e1f5ff;
}
</style>
