<script setup>
import {
  onMounted,
  ref,
  onActivated,
  nextTick,
  onUnmounted,
  onDeactivated
} from "vue";
import { formatTime } from "@/utils/index";
import {
  courseFindAll,
  courseIsFreeze,
  findAllCourseType
} from "@/api/course.js";
// import { courseTypeFindAllNotPage } from "@/api/courseClassify.js";
import Evaluate from "./components/evaluate.vue";
import Reply from "./components/reply.vue";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
import { useI18n } from "vue-i18n";

defineOptions({
  name: "CourseManagementIndex"
});
const { t } = useI18n();
console.log(t("evaluateManagement.evaluationReview"));

const evaluateRef = ref(null);
const replyRef = ref(null);
// 课程状态选项
const stateOptions = ref([
  {
    name: "全部",
    id: 0
  },
  {
    name: "正常",
    id: 1
  },
  {
    name: "冻结",
    id: 2
  }
]);

const activeName = ref(0);
const tabTitle = ref([
  { id: 0, name: "评价审核" },
  { id: 1, name: "回复审核" }
]);
// 切换tab
const handleClick = (item, index) => {
  activeName.value = item.props.name;
  // console.log(
  //   "🌵 activeName.value------11------------------------>",
  //   activeName.value
  // );
  // console.log(
  //   "🍧activeOption.value------------------------------>",
  //   activeOption.value
  // );
  if (activeName.value === 0) {
    if (activeOption.value === 0) {
      evaluateRef.value?.getTableList("PENDING_REVIEW");
      evaluateObj.value.todoId = 0;
      evaluateObj.value.state = "PENDING_REVIEW";
    }
    if (activeOption.value === 1) {
      evaluateRef.value?.getTableList("APPROVED");
      evaluateObj.value.todoId = 1;
      evaluateObj.value.state = "APPROVED";
    }
    if (activeOption.value === 2) {
      evaluateRef.value?.getTableList("REJECTED");
      evaluateObj.value.todoId = 2;
      evaluateObj.value.state = "REJECTED";
    }
  }
  if (activeName.value === 1) {
    if (activeOption.value === 0) {
      replyRef.value?.getTableList("PENDING_REVIEW");
      replyObj.value.todoId = 0;
      replyObj.value.state = "PENDING_REVIEW";
    }
    if (activeOption.value === 1) {
      replyObj.value.todoId = 1;
      replyObj.value.state = "APPROVED";
      replyRef.value?.getTableList("APPROVED");
    }
    if (activeOption.value === 2) {
      replyObj.value.todoId = 2;
      replyObj.value.state = "REJECTED";
      replyRef.value?.getTableList("REJECTED");
    }
  }
  // activeOption.value = 0;
};
const activeOption = ref(0);
const tabTitleOption = ref([
  { id: 0, name: "待处理", value: "PENDING_REVIEW" },
  { id: 1, name: "已通过", value: "APPROVED" },
  { id: 2, name: "已驳回", value: "REJECTED" }
]);
const evaluateObj = ref({ todoId: 0, state: "PENDING_REVIEW" });
const replyObj = ref({ todoId: 0, state: "PENDING_REVIEW" });
// 切换tab
const handleClickOption = (item, index) => {
  // console.log("🌵item------------222------------------>", item);
  activeOption.value = item.props.name;
  // console.log(
  //   "🌵 activeOption.value------------------------------>",
  //   activeOption.value
  // );
  if (item.props.name === 0) {
    evaluateObj.value.todoId = 0;
    evaluateObj.value.state = "PENDING_REVIEW";
    replyObj.value.todoId = 0;
    replyObj.value.state = "PENDING_REVIEW";
    if (activeName.value === 0) {
      evaluateRef.value.getTableList("PENDING_REVIEW");
    } else {
      replyRef.value.getTableList("PENDING_REVIEW");
    }
  } else if (item.props.name === 1) {
    evaluateObj.value.todoId = 1;
    evaluateObj.value.state = "APPROVED";
    replyObj.value.todoId = 1;
    replyObj.value.state = "APPROVED";
    if (activeName.value === 0) {
      evaluateRef.value.getTableList("APPROVED");
    } else {
      replyRef.value.getTableList("APPROVED");
    }
  } else {
    evaluateObj.value.todoId = 2;
    evaluateObj.value.state = "REJECTED";
    replyObj.value.todoId = 2;
    replyObj.value.state = "REJECTED";
    if (activeName.value === 0) {
      evaluateRef.value.getTableList("REJECTED");
    } else {
      replyRef.value.getTableList("REJECTED");
    }
  }
};
</script>

<template>
  <div class="containers">
    <div class="main">
      <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
        <el-tab-pane
          v-for="(item, index) in tabTitle"
          :key="index"
          :label="item.name"
          :name="index"
        />
      </el-tabs>
      <div class="tab-line">
        <el-tabs
          v-model="activeOption"
          class="demo-tabs"
          @tab-click="handleClickOption"
        >
          <el-tab-pane
            v-for="(item, index) in tabTitleOption"
            :key="index"
            :label="item.name"
            :name="index"
          />
        </el-tabs>
      </div>
      <Evaluate
        v-if="activeName === 0 && evaluateObj"
        ref="evaluateRef"
        :evaluateObj="evaluateObj"
      />
      <Reply
        v-if="activeName === 1 && replyObj"
        ref="replyRef"
        :replyObj="replyObj"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.scrollbar {
  // padding-top: 20px;
  // padding: 20px;
  // height: calc(100vh - 181px);
  // height: calc(100vh - 329px);

  background-color: #fff;
}
// :deep(.el-table .cell) {
//   padding: 0;
// }
.containers {
  display: flex;
  flex-direction: column;
  height: 88vh; /* Or adjust based on your global layout, e.g., calc(100vh - GlobalHeaderHeight) */
  overflow: hidden;
  box-sizing: border-box;
  .search {
    box-sizing: border-box;
    padding: 20px 20px 2px 20px;
    background-color: #fff;
    margin-bottom: 20px;
    .con_search {
      display: flex;
      align-items: center;
      width: 100%;
      height: fit-content;
      // .input_width {
      //   width: 200px;
      // }
    }
  }

  .main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden; /* Prevents .main from expanding due to its own content */
    background: #fff;
    padding: 20px 20px 0 20px;
    box-sizing: border-box;
    // height: 100%; /* Removed as height is now controlled by flex:1 */
  }
}
.tab-line {
  :deep(.el-tabs__active-bar) {
    background-color: transparent;
  }
}
:deep(.el-tabs__nav-wrap:after) {
  background-color: transparent;
}
:deep(.el-tabs) {
  --el-tabs-header-height: 30px;
}
</style>
