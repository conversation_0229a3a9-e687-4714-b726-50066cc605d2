<script setup>
import { ref, onMounted } from "vue";
import { ArrowRight, Plus } from "@element-plus/icons-vue";
import { useRole } from "./hook.jsx";

defineOptions({
  name: "ClassificationManagement"
});

const {
  one,
  tow,
  pagination,
  dataList,
  columns,
  editAdd,
  openDialog,
  move,
  handleAdd,
  handleCurrentChange,
  handleSizeChange,
  loadingTable,
  view,
  listData,
  to,
  addressLinkDom,
  handleCloseAddressLinkDom,
  ruleFormRef,
  form,
  rules,
  confirmAdd,
  beforeCloseDom,
  buttonLoading,
  isSubPage,
  fileUpload
} = useRole();
</script>

<template>
  <div>
    <div class="common" style="padding-bottom: 0">
      <!-- <div class="title">分类管理</div> -->
      <div class="search">
        <el-breadcrumb :separator-icon="ArrowRight">
          <el-breadcrumb-item>
            <el-button link @click="to()"> 分类管理 </el-button>
          </el-breadcrumb-item>
          <el-breadcrumb-item v-for="(it, index) in listData" :key="index">
            <el-button link @click="to(it)">{{ it.name }}</el-button>
          </el-breadcrumb-item>
        </el-breadcrumb>
        <el-button type="primary" @click="handleAdd()">新增分类</el-button>
      </div>
    </div>

    <div class="common">
      <div class="puretable">
        <pure-table
          ref="tableRef"
          row-key="id"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="left"
          table-layout="auto"
          :loading="loadingTable"
          :data="dataList"
          :columns="columns"
          :pagination="{ ...pagination }"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #operation="{ row }">
            <div class="botlist">
              <div class="u">
                <el-button
                  class="reset-margin"
                  link
                  type="primary"
                  @click="view('查看', row)"
                >
                  查看
                </el-button>
              </div>
              <div class="u">
                <el-button
                  class="reset-margin"
                  link
                  type="primary"
                  @click="editAdd('编辑', row)"
                >
                  编辑
                </el-button>
              </div>
              <div class="u">
                <el-button
                  class="reset-margin"
                  link
                  type="danger"
                  @click="openDialog('删除', row)"
                >
                  删除
                </el-button>
              </div>
              <div class="u">
                <el-button
                  v-if="row.id !== one"
                  class="reset-margin"
                  link
                  type="primary"
                  @click="move('上移', row)"
                >
                  上移
                </el-button>
              </div>
              <div class="u">
                <el-button
                  v-if="row.id !== tow"
                  class="reset-margin"
                  link
                  type="primary"
                  @click="move('下移', row)"
                >
                  下移
                </el-button>
              </div>
            </div>
          </template>
        </pure-table>
      </div>
      <el-dialog
        v-model="addressLinkDom"
        width="30%"
        :close-on-click-modal="false"
        :title="`${form.title}分类名`"
        @closed="beforeCloseDom"
      >
        <el-form
          ref="ruleFormRef"
          :model="form"
          label-width="110px"
          label-position="right"
          :rules="rules"
        >
          <el-form-item
            label="图片"
            prop="url"
            show-word-limit
            style="display: flex; align-items: center"
            :required="!isSubPage"
          >
            <div class="upload-container">
              <el-upload
                v-model="form.url"
                class="avatar-uploader"
                accept="image/*"
                :show-file-list="false"
                :http-request="() => {}"
                :before-upload="uploadFile => fileUpload(uploadFile, form)"
              >
                <el-image
                  v-if="form.url"
                  :src="form.url"
                  fit="scale-down"
                  class="avatar"
                />
                <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
              </el-upload>
              <div class="upload-tip">
                支持上传png、jpg、jpeg图片格式，最多上传1张，最佳尺寸：200*200px，单张大小不超过10MB
              </div>
            </div>
          </el-form-item>
          <el-form-item
            v-if="form.title === '编辑'"
            label="编号"
            prop="number"
            show-word-limit
          >
            <el-input v-model="form.number" placeholder="请输入编号" disabled />
          </el-form-item>
          <el-form-item label="分类名" prop="textarea2" show-word-limit>
            <el-input
              v-model.trim="form.textarea2"
              placeholder="请输入分类名"
            />
          </el-form-item>
        </el-form>
        <div class="buttom">
          <el-button @click="beforeCloseDom()">取消</el-button>
          <el-button
            :loading="buttonLoading"
            type="primary"
            @click="confirmAdd(ruleFormRef, form)"
          >
            确定
          </el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.title {
  margin-bottom: 20px;
  font-size: 20px;
  font-weight: bold;
  color: #606266;
}

.common {
  width: 100%;
  height: 100%;
  padding: 20px 20px 2px;
  background-color: #fff;

  .search {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.botlist {
  // min-width: 120px;
  display: flex;
  .u {
    width: 50px;
  }
}

.buttom {
  display: flex;
  justify-content: end;
}
.botton {
  margin-bottom: 20px;
}

:deep(.avatar-uploader .avatar) {
  width: 120px;
  height: 120px;
  display: block;
}
:deep(.avatar-uploader .el-upload) {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}
:deep(.avatar-uploader .el-upload:hover) {
  border-color: var(--el-color-primary);
}
:deep(.el-icon.avatar-uploader-icon) {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 120px;
  text-align: center;
}

.upload-container {
  display: flex;
  flex-direction: column;
}

.upload-tip {
  margin-top: 8px;
  color: #909399;
  font-size: 12px;
  line-height: 1.2;
}
</style>
