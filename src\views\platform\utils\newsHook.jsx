import { ref, reactive, onMounted, onActivated } from "vue";
import dayjs from "dayjs";
import { requestTo } from "@/utils/http/tool";
import { uploadFile, validateFileType } from "@/utils/upload/upload.js";
import { ElMessage, ElMessageBox, genFileId, ElUpload } from "element-plus";
import { useRouter, useRoute } from "vue-router";
// import { discoverFindAll, discoverDelete, discoverAdd } from "@/api/findapi.js";
import { newsFindAll, newsDelete } from "@/api/newsapi.js";
import { ImageThumbnail } from "@/utils/imageProxy";

export function useRole() {
  const router = useRouter();
  const tableRef = ref();
  const defaultTime = [
    new Date(2024, 4, 1, 0, 0, 0),
    new Date(2024, 4, 1, 23, 59, 59)
  ];
  const dataList = ref([]);
  const from = reactive({
    name: "",
    time: [],
    file: {}
  });
  const pagination = {
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true
  };

  // 图片处理
  function arrimg(val) {
    let arrimgList = [];
    if (val.files.length > 0) {
      const filesArray = Array.isArray(val.files);
      if (filesArray) {
        val.files.map(item => {
          arrimgList.push(item?.uploadFile.url);
        });
        return arrimgList;
      }
    }
  }
  function convertTableToSpans(html) {
    // 替换表格行和单元格为span
    let converted = html
      .replace(/<table[^>]*>/g, "<span>") // 替换 <table> 为 <span>
      .replace(/<\/table>/g, "</span>") // 替换 </table> 为 </span>
      .replace(/<tbody[^>]*>/g, "") // 移除 <tbody>
      .replace(/<\/tbody>/g, "") // 移除 </tbody>
      .replace(/<tr[^>]*>/g, "") // 移除 <tr>
      .replace(/<\/tr>/g, "") // 移除 </tr>
      .replace(/<t[dh]([^>]*)>/g, "<span$1>") // 替换 <td> 或 <th> 为 <span>，保留属性
      .replace(/<\/t[dh]>/g, "</span>"); // 替换 </td> 或 </th> 为 </span>
    return converted;
  }

  const customerType = {
    PLATFORM_ADMIN: "平台管理员",
    ORGANIZATION_ADMIN: "组织管理员",
    PARENT: "家长",
    STUDENT: "学生",
    EDUCATION_BUREAU: "教育局"
  };

  const columns = [
    {
      label: "编号",
      prop: "id",
      width: 80,
      formatter: ({ id }) => {
        return id || "--";
      }
    },
    // 根据新接口，暂时注释掉图片列
    // {
    //   label: "图片",
    //   prop: "files",
    //   width: 120,
    //   cellRenderer: ({ row }) =>
    //     // <div  style="width: 120px; height: 100px; display: flex; justify-content: center;  align-items: center;">
    //     row.files
    // ? (
    //       <el-image
    //         preview-teleported
    //         loading="lazy"
    //         src={ImageThumbnail(row.files[0]?.uploadFile?.url, "200x")}
    //         preview-src-list={arrimg(row)}
    //         hide-on-click-modal={true}
    //         fit="cover"
    //         class="w-[100px] h-[100px]"
    //       />
    //     )
    // : (
    //       <el-image
    //         src=""
    //         preview-src-list={[]}
    //         show-progress
    //         fit="cover"
    //         class="img-pic"
    //       />
    //     )
    //   // </div>
    // },
    // 根据新接口，移除发布者姓名和用户类型列
    // {
    //   label: "发布者姓名",
    //   prop: "promoterName",
    //   width: 150,
    //   formatter: ({ promoterName }) => {
    //     return promoterName || "--";
    //   }
    // },
    // {
    //   label: "用户类型",
    //   prop: "userType",
    //   width: 150,
    //   formatter: ({ userType }) => {
    //     return customerType[userType] || "--";
    //   }
    //   // cellRenderer: ({ row }) => (
    //   //   <el-text line-clamp="1" >
    //   //     {customerType[userType]}
    //   //   </el-text>
    //   // )
    // },
    {
      label: "标题",
      prop: "title",
      width: 200,
      cellRenderer: ({ row }) => (
        <el-text line-clamp="1">{row.title || "--"}</el-text>
      )
    },
    {
      label: "创建时间",
      width: 180,
      prop: "createdAt",
      formatter: ({ createdAt }) => {
        return createdAt
          ? dayjs(createdAt).format("YYYY-MM-DD HH:mm:ss")
          : "--";
      }
    },
    {
      label: "内容",
      prop: "content",
      minWidth: 500,
      cellRenderer: ({ row }) => (
        <el-text line-clamp="4" style={{ maxHeight: "120px" }}>
          <span v-html={convertTableToSpans(row.content)} />
        </el-text>
      )
    },

    {
      label: "操作",
      fixed: "right",
      width: 180,
      slot: "operation"
    }
  ];

  const removeEmptyValues = obj => {
    return Object.fromEntries(
      Object.entries(obj).filter(
        ([key, value]) =>
          value !== "" && !(Array.isArray(value) && value.length === 0)
      )
    );
  };

  // 列表 api
  const onSearch = async val => {
    try {
      // 根据新接口规范构建参数
      const paramsArg = {
        page: val.page,
        size: val.size,
        sort: "createdAt,desc",
        title: val.title || "" // 新接口使用title字段进行搜索
      };

      let aee = removeEmptyValues(paramsArg);
      console.log("📡 API调用参数:", aee); // 调试信息

      // 调用新闻分页查询API
      const [err, res] = await requestTo(newsFindAll(aee));

      if (res) {
        dataList.value = res.content;
        pagination.total = res.totalElements;
      } else if (err) {
        console.error("获取新闻列表失败:", err);
        ElMessage({
          type: "error",
          message: "获取新闻列表失败，请重试"
        });
      }
    } catch (error) {
      console.error("获取新闻列表失败:", error);
      ElMessage({
        type: "error",
        message: "获取新闻列表失败，请重试"
      });
    }
  };

  // 搜索
  const searchAdd = () => {
    // 搜索时重置分页到第一页
    pagination.currentPage = 1;

    // 暂时跳过创建时间筛选（后端接口尚未完成）
    // let time1 = from.time !== null ? new Date(from.time[0]).getTime() : "";
    // let time2 = from.time !== null ? new Date(from.time[1]).getTime() : "";
    const paramsArg = {
      title: from.name || "", // 使用title字段进行搜索
      // startTime: time1 || "", // 暂时注释掉时间筛选
      // endTime: time2 || "",   // 暂时注释掉时间筛选
      page: 0, // 搜索时重置到第一页
      size: pagination.pageSize || 10
    };
    console.log("🔍 搜索参数:", paramsArg); // 调试信息
    onSearch(paramsArg);
  };

  // 重置
  const setData = () => {
    from.name = "";
    from.time = [];
    // 重置后重新加载数据
    pagination.currentPage = 1;
    const paramsArg = {
      page: 0,
      size: pagination.pageSize || 10
    };
    onSearch(paramsArg);
  };

  // 每页多少条
  async function handleSizeChange(val) {
    pagination.pageSize = val;
    // 暂时跳过创建时间筛选（后端接口尚未完成）
    // let time1 = from.time !== null ? new Date(from.time[0]).getTime() : "";
    // let time2 = from.time !== null ? new Date(from.time[1]).getTime() : "";
    const paramsArg = {
      title: from.name || "", // 使用title字段进行搜索
      // startTime: time1 || "", // 暂时注释掉时间筛选
      // endTime: time2 || "",   // 暂时注释掉时间筛选
      page: 0,
      size: val
    };
    onSearch(paramsArg);
  }
  // 前往页数
  async function handleCurrentChange(val) {
    pagination.currentPage = val;
    // 暂时跳过创建时间筛选（后端接口尚未完成）
    // let time1 = from.time !== null ? new Date(from.time[0]).getTime() : "";
    // let time2 = from.time !== null ? new Date(from.time[1]).getTime() : "";
    const paramsArg = {
      title: from.name || "", // 使用title字段进行搜索
      // startTime: time1 || "", // 暂时注释掉时间筛选
      // endTime: time2 || "",   // 暂时注释掉时间筛选
      page: val - 1,
      size: pagination.pageSize
    };
    onSearch(paramsArg);
  }

  // 删除
  const isDeleteApi = (text, val) => {
    console.log("🐠-----val-----", val);
    let freezeText = `确定要删除该条内容吗？`; //  `确定要删除标题为"${val.name}"`
    ElMessageBox.confirm(`${freezeText}`, `确定删除`, {
      confirmButtonText: "确定",
      cancelButtonText: "取消"
    })
      .then(async () => {
        try {
          // 根据新接口规范构建参数
          const paramsArg = {
            id: val.id
          };

          // 操作日志
          const operateLog = {
            operateLogType: "PLATFORM_SETTINGS",
            operateType: "删除了标题为" + val.title + "的新闻管理"
          };

          // 调用新闻删除API
          const { code } = await newsDelete(paramsArg, operateLog);

          if (code === 200) {
            // 根据实际响应，成功时code为200
            // 删除成功后重新加载列表
            const paramsArgapi = {
              page: 0,
              size: 10
            };
            onSearch(paramsArgapi);
            ElMessage({
              type: "success",
              message: "删除成功"
            });
          } else {
            ElMessage({
              type: "error",
              message: "删除失败"
            });
          }
        } catch (error) {
          console.error("删除新闻失败:", error);
          ElMessage({
            type: "error",
            message: "删除失败，请重试"
          });
        }
      })
      .catch(() => {});
  };

  // 新增/详情/编辑 按钮
  const batchImportDom = ref(false);
  const batchImport = (text, val) => {
    console.log("🌈-----text,val-----", text, val);
    if (text === "newly") {
      // 新增
      router.push({
        path: "/platform/components/newsNewly",
        query: { id: "1" }
      });
    } else if (text === "details") {
      // 详情
      router.push({
        path: "/platform/components/newsDetails",
        query: { id: val.id }
      });
    } else if (text === "edit") {
      // 编辑
      router.push({
        path: "/platform/components/newsEdit",
        query: { id: val.id }
      });
    }
  };

  onMounted(async () => {
    const paramsArg = {
      page: 0,
      size: 10
    };
    onSearch(paramsArg);
  });
  onActivated(async () => {
    const paramsArg = {
      page: 0,
      size: 10
    };
    onSearch(paramsArg);
  });

  return {
    tableRef,
    defaultTime,
    dataList,
    from,
    pagination,
    columns,

    searchAdd,
    setData,
    handleSizeChange,
    handleCurrentChange,
    isDeleteApi,
    batchImport
  };
}
