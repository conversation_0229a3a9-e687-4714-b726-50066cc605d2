<script setup>
import {
  ref,
  onMounted,
  watch,
  reactive,
  onBeforeMount,
  onBeforeUnmount
} from "vue";
import { useRouter, useRoute } from "vue-router";
import RichEditor from "@/components/Base/RichEditor.vue";
import {
  courseAdd,
  verifyPhone,
  verifyUsername,
  institutionFindById
} from "@/api/institution";
import { getPhonecode } from "@/api/leaderLecturer";
import { ElMessage } from "element-plus";
import uploadImg from "@/assets/login/upload1.png";
import {
  uploadFile,
  validateFileType,
  isOverSizeLimit
} from "@/utils/upload/upload";
import { requestTo } from "@/utils/http/tool";
import { decrypt, encryption } from "@/utils/SM4.js";
import { createIntroduction } from "@/utils/createTestData.js";
import FileItem from "@/components/PreviewV2/FileItem.vue";
import { pinyin } from "pinyin-pro";
import { Plus } from "@element-plus/icons-vue";
import Map from "./components/Map.vue";
import ImgPos from "@/assets/pos.png";

// createIntroduction();

const router = useRouter();
const route = useRoute();
const form = ref({
  time: "",
  institutionID: "",
  institutionName: "",
  alias: "",
  files: [],
  // institutionLicense: [],
  organizationCode: "",
  // qualificationDocuments: [],
  customerHotline: "",
  organizationsOn: "",
  organizationAdmin: {
    name: "",
    account: "",
    phone: ""
  },
  introduction: "",
  username: "",
  account: "",
  phone: "",
  verificationCode: "", // 添加验证码字段
  legalPerson: "",
  principalContact: "",
  establishmentTime: "",
  administrativeDivision: "",
  operatingAddress: "",
  operatingAddressLongitude: "",
  operatingAddressLatitude: "",
  unifiedSocialCreditCode: "",
  schoolPermitNumber: "",
  registeredAddress: "",
  registeredAddressLongitude: "",
  registeredAddressLatitude: "",
  organizationCategory: [],
  trainingCategory: [],
  serviceScope: "",
  oneSentenceIntroduction: "",
  introduction: "",
  customerServiceHotline: "",
  logo: ""
});
const formFile = ref({
  institutionLicense: [],
  qualificationDocuments: [],
  logo: [],
  video: [],
  environment: []
});
// NONE("无"),
// COVER("封面"),
// BUSINESS_LICENSE("营业执照"),
// PHOTO("照片"),
// QUALIFICATION_DOCUMENT("资质文件"),
// ILLUSTRATION("配图");

const formRef = ref(null);
const formRef1 = ref(null);
const richFlag = ref(false);
onMounted(() => {
  // if (route.query.data) {
  //   const rawData = JSON.parse(route.query.data);
  //   form.value = rawData;
  // }
  richFlag.value = true;
});
// 基本信息
const formData = ref([
  {
    label: "机构名称",
    type: "input",
    prop: "name",
    check: true,
    maxlength: 50,
    placeholder: "请输入机构名称"
    // width: "500px"
  },
  // {
  //   label: "机构ID",
  //   type: "input",
  //   prop: "id",
  //   check: true,
  //   placeholder: "请输入机构ID"
  //   // width: "500px"
  // },
  {
    label: "机构别名",
    type: "input",
    check: true,
    prop: "alias",
    maxlength: 30,
    placeholder: "请输入机构别名"
    // width: "500px"
  },
  {
    label: "机构编号",
    type: "input",
    check: false,
    prop: "code",
    maxlength: 30,
    placeholder: "请输入机构编号"
  },
  {
    label: "客服热线",
    type: "input",
    check: true,
    maxlength: 11,
    prop: "customerServiceHotline",
    placeholder: "请输入客服热线"
    // width: "500px"
  },
  {
    label: "组织机构代码",
    type: "input",
    check: false,
    prop: "organizationCode",
    maxlength: 20,
    placeholder: "请输入组织机构代码"
    // width: "500px"
  },
  {
    label: "法定代表人",
    type: "input",
    check: true,
    maxlength: 50,
    prop: "legalPerson",
    placeholder: "请输入法定代表人"
    // width: "500px"
  },
  {
    label: "负责人联系方式",
    type: "input",
    check: true,
    maxlength: 11,
    prop: "principalContact",
    placeholder: "请输入联系方式"
    // width: "500px"
  },
  {
    label: "成立时间",
    type: "date",
    check: true,
    prop: "establishmentTime",
    placeholder: "请输入成立时间"
    // width: "500px"
  },
  {
    label: "所属行政区域",
    type: "select",
    check: true,
    prop: "administrativeDivision",
    placeholder: "请输入行政区域",
    opt: null,
    parentId: 381
    // width: "500px"
  },
  {
    label: "经营地址",
    type: "map",
    check: true,
    prop: "operatingAddress",
    placeholder: "请选择经营地址"
    // width: "500px",
  },
  {
    label: "统一社会信用代码",
    type: "input",
    check: true,
    maxlength: 20,
    prop: "unifiedSocialCreditCode",
    placeholder: "请输入统一社会信用代码"
    // width: "500px"
  },
  {
    label: "办学许可证",
    type: "input",
    check: false,
    maxlength: 30,
    prop: "schoolPermitNumber",
    placeholder: "请输入办学许可证号"
    // width: "500px"
  },
  {
    label: "注册地址",
    type: "map",
    check: true,
    prop: "registeredAddress",
    placeholder: "请选择注册地址",
    registeredAddressLongitude: "",
    registeredAddressLatitude: ""
    // width: "500px"
  },
  {
    label: "机构类别",
    type: "select2",
    check: false,
    prop: "organizationCategory",
    placeholder: "请选择机构类别",
    // width: "500px",
    opt: [],
    parentId: 369
  },
  {
    label: "培训类别",
    type: "select2",
    check: false,
    prop: "trainingCategory",
    placeholder: "请选择培训类别",
    // width: "500px",
    opt: [],
    parentId: 370
  },
  {},
  {
    label: "服务范围",
    type: "textarea",
    check: true,
    prop: "serviceScope",
    placeholder: "请输入服务范围"
    // width: "500px"
  }
]);

// 机构介绍
const institutionIntroduction = ref([
  {
    label: "机构logo",
    type: "upload",
    check: true,
    prop: "logo",
    placeholder: "请上传机构logo",
    width: "200px",
    limit: 1,
    text: "支持上传png、jpg、jpeg图片格式，最多上传1张，最佳尺寸:200*200px，单张图片大小不超过10MB"
  },
  {
    label: "机构简介",
    type: "textarea",
    check: true,
    prop: "oneSentenceIntroduction",
    placeholder: "请用一句话对机构进行简介",
    span: 12,
    maxLength: 200
    // width: "500px"
  },
  {
    label: "机构介绍",
    type: "editor",
    check: true,
    prop: "introduction",
    placeholder: "请输入机构介绍",
    width: "200px"
  },
  {
    label: "宣传视频",
    type: "upload",
    type2: "video",
    check: false,
    prop: "video",
    prop2: "PROMOTIONAL_VIDEO",
    width: "200px",
    limit: 3,
    text: "支持上传vido等视频格式，最多上传3个，单个视频大小不超过800MB "
  },
  {
    label: "机构环境",
    type: "upload",
    prop: "environment",
    prop2: "ENVIRONMENT",
    width: "200px",
    limit: 9,
    text: "支持上传png、jpg、jpeg图片格式，最多上传9张，最佳尺寸:200*200px，单张图片大小不超过10MB"
  }
]);
// 资质信息
const qualifications = ref([
  {
    label: "营业执照",
    type: "upload",
    check: true,
    prop: "institutionLicense",
    prop2: "BUSINESS_LICENSE",
    text: "支持上传图片及pdf文件，最多上传9个，图片最佳尺寸：750*1334px，单张图片大小不超过10MB，文件单个大小不超过30MB"
  },
  {
    label: "资质文件",
    type: "upload",
    check: true,
    prop: "qualificationDocuments",
    prop2: "QUALIFICATION_DOCUMENT",
    text: "支持上传图片及pdf、doc、docx、ppt、pptx、xls、xlsx文件，最多上传9个，图片最佳尺寸：750*1334px，单张图片大小不超过10MB，文件单个大小不超过30MB"
  }
]);

// 账户信息
const formfootData = ref([
  {
    label: "姓名",
    type: "input",
    check: true,
    prop: "username",
    placeholder: "请输姓名",
    width: "200px"
  },
  {
    label: "账号",
    type: "input",
    check: true,
    prop: "account",
    placeholder: "请输账号",
    width: "200px"
  }
]);
const formfootDataTow = ref([
  {
    label: "手机号",
    type: "input",
    prop: "phone",
    check: true,
    show: true,
    placeholder: "请输入手机号",
    width: "200px"
  },
  {
    label: "验证码",
    type: "input",
    prop: "verificationCode",
    placeholder: "请输入验证码",
    width: "200px",
    check: true,
    show: false,
    hasButton: false,
    buttonText: "获取验证码"
  }
]);

//返回上一页
const reset = () => {
  router.go(-1);
};
// 自定义富文本校验方法
const validateIntroduction = (rule, value, callback) => {
  // 处理空值
  if (!value) {
    return callback(new Error("机构简介不能为空"));
  }

  // 转换HTML实体为普通字符
  let cleanValue = value
    .replace(/&nbsp;/g, " ")
    .replace(/&emsp;/g, " ")
    .replace(/&lt;/g, "<")
    .replace(/&gt;/g, ">")
    .replace(/&amp;/g, "&");

  // 移除HTML标签
  cleanValue = cleanValue.replace(/<[^>]*>/g, "").trim();

  // 处理仅包含空格或换行符的情况
  if (!cleanValue || /^\s*$/.test(cleanValue)) {
    return callback(new Error("机构简介不能为空"));
  }

  callback();
};
// 自定义客服热线校验方法
const validateServiceHotline = (rule, value, callback) => {
  if (!value) {
    callback(new Error("客服热线不能为空"));
    return;
  }

  const phoneRegex =
    /^((0\d{2,3}-?\d{7,8})|(1[3-9]\d{9})|(400-?\d{3}-?\d{4})|(400\d{7,8}))$/;
  if (!phoneRegex.test(value)) {
    callback(new Error("请输入正确的手机号、座机号或400热线"));
    return;
  }

  callback();
};
// 自定义中文校验方法
const validateChineseName = (rule, value, callback) => {
  const chineseRegex = /^[\u4E00-\u9FA5]+$/; // 匹配纯中文字符
  if (!value || !chineseRegex.test(value)) {
    callback(new Error("姓名必须是中文"));
  } else {
    callback();
  }
};
// 自定义电话号码校验方法
const validatePhoneNumber = async (rule, value, callback) => {
  const phoneRegex = /^1[3-9]\d{9}$/; // 匹配中国大陆手机号码
  if (!value) {
    callback(new Error("手机号不能为空"));
    formfootDataTow.value[1].show = false;
    formfootDataTow.value[1].hasButton = false;
  } else if (!phoneRegex.test(value)) {
    callback(new Error("请输入有效的手机号码"));
    formfootDataTow.value[1].show = false;
    formfootDataTow.value[1].hasButton = false;
  } else {
    formfootDataTow.value[1].show = true;
    formfootDataTow.value[1].hasButton = true;
    callback();
  }
};

// 自定义验证码校验方法
const validateVerificationCode = (rule, value, callback) => {
  if (!value) {
    callback(new Error("验证码不能为空"));
  } else if (!/^\d{6}$/.test(value)) {
    callback(new Error("请输入6位数字验证码"));
  } else {
    callback();
  }
};
// 自定义机构别名校验方法
const validateAlphabet = (rule, value, callback) => {
  const alphabetRegex = /^[a-z]+$/i; // 匹配纯字母字符
  if (!value) {
    callback(new Error("机构别名不能为空"));
  } else if (!alphabetRegex.test(value)) {
    callback(new Error("机构别名只能包含字母"));
  } else {
    callback();
  }
};
// 自定义账号校验方法
const validateAccount = async (rule, value, callback) => {
  if (!value) {
    callback(new Error("账号不能为空"));
  } else {
    try {
      const response = await verifyUsername({ username: value });
      console.log("🌈-----response-----", response);
      if (response.code === 10016) {
        callback(new Error("账号已存在"));
      } else {
        callback();
      }
    } catch (error) {
      console.log("🌈-----error-----", error);
    }
    // callback();
  }
};
// 自定义组织机构校验方法
const validatelegalPerson = (rule, value, callback) => {
  const legalPerson = /^[\u4E00-\u9FA5]+$/;
  if (!value) {
    callback(new Error("法定代表人不能为空"));
  } else if (!legalPerson.test(value)) {
    callback(new Error("法定代表人必须是中文"));
  } else {
    callback();
  }
};
//自定义负责人联系方式校验方法
const validateprincipalContact = (rule, value, callback) => {
  if (!value) {
    callback(new Error("负责人联系方式不能为空"));
  }
  const phoneRegex =
    /^(?:0\d{2,3}-[1-9]\d{6,7}(?:-\d{1,6})?|0\d{2,3}[1-9]\d{6,7}(?:-\d{1,6})?|400-\d{4}-\d{4}|400\d{8}|1[3-9]\d{9})$/;
  if (!phoneRegex.test(value)) {
    callback(new Error("请输入正确的联系方式"));
    return;
  }

  callback();
};
// 自定义统一社会信用代码校验方法
const validateUnifiedSocialCreditCode = (rule, value, callback) => {
  const pattern = /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/;
  if (!value) {
    callback(new Error("统一社会信用代码不能为空"));
  } else if (!pattern.test(value)) {
    callback(new Error("请输入正确的统一社会信用代码"));
  } else {
    callback();
  }
};
// 自定义一句话简介校验方法
const oneSentenceIntroductions = (rule, value, callback) => {
  const cleanValue = value.replace(/\s/g, "");
  if (!cleanValue) {
    callback(new Error("简介不能为空"));
  } else if (value.length > 200) {
    callback(new Error("简介不能超过200个字"));
  } else {
    callback();
  }
};
// 服务范围校验
const serviceScopepop = (rule, value, callback) => {
  const cleanValue = value.replace(/\s/g, "");
  if (!cleanValue) {
    callback(new Error("服务范围不能为空"));
  } else if (value.length > 1000) {
    callback(new Error("服务范围不能超过1000个字"));
  } else {
    callback();
  }
};
// 机构营业执照文件校验
const institutionLicenseFile = (rule, value, callback) => {
  if (formFile.value.institutionLicense.length === 0) {
    callback(new Error("请上传营业执照"));
  } else {
    callback();
  }
};
// 资质文件校验
const qualificationDocumentsFile = (rule, value, callback) => {
  if (formFile.value.qualificationDocuments.length === 0) {
    callback(new Error("请上传资质文件"));
  } else {
    callback();
  }
};
// logo文件校验
const logoFile = (rule, value, callback) => {
  if (formFile.value.logo.length <= 0) {
    callback(new Error("请上传logo"));
  } else {
    callback();
  }
};
// 校验规则
const rules = ref({
  name: [{ required: true, message: "机构名称不能为空", trigger: "blur" }],
  alias: [{ required: true, validator: validateAlphabet, trigger: "blur" }],
  customerServiceHotline: [
    { required: true, validator: validateServiceHotline, trigger: "blur" }
  ],
  introduction: [
    { required: true, validator: validateIntroduction, trigger: "blur" }
  ],
  username: [
    { required: true, validator: validateChineseName, trigger: "blur" }
  ],
  account: [{ required: true, validator: validateAccount, trigger: "blur" }],
  phone: [{ required: true, validator: validatePhoneNumber, trigger: "blur" }],
  verificationCode: [
    { required: true, validator: validateVerificationCode, trigger: "blur" }
  ],
  legalPerson: [
    { required: true, validator: validatelegalPerson, trigger: "blur" }
  ],
  principalContact: [
    { required: true, validator: validateprincipalContact, trigger: "blur" }
  ],
  establishmentTime: [
    { required: true, message: "请选择成立时间", trigger: "blur" }
  ],
  unifiedSocialCreditCode: [
    {
      required: true,
      validator: validateUnifiedSocialCreditCode,
      trigger: "blur"
    }
  ],
  oneSentenceIntroduction: [
    {
      required: true,
      validator: oneSentenceIntroductions,
      trigger: "blur"
    }
  ],
  administrativeDivision: [
    { required: true, message: "请选择行政区划", trigger: "blur" }
  ],
  serviceScope: [
    { required: true, validator: serviceScopepop, trigger: "blur" }
  ],
  operatingAddress: [
    { required: true, message: "请选择经营地址", trigger: "blur" }
  ],
  registeredAddress: [
    { required: true, message: "请选择注册地址", trigger: "blur" }
  ],
  institutionLicense: [
    { required: true, validator: institutionLicenseFile, trigger: "blur" }
  ],
  qualificationDocuments: [
    { required: true, validator: qualificationDocumentsFile, trigger: "blur" }
  ],
  logo: [{ required: true, validator: logoFile, trigger: "blur" }]
});
// 提交表单
const submitForm = () => {
  form.value.establishmentTime = new Date(
    form.value.establishmentTime
  ).getTime();

  formRef.value.validate(valid => {
    if (valid) {
      console.log("表单数据:", form.value);
      onSubmit();
    } else {
      console.log("表单校验失败", valid);
    }
  });
};
const getListLoading = ref(false);
const onSubmit = async () => {
  console.log("submit!");
  form.value.files = []; // 清空文件集合
  fileData(); //文件集合处理
  let paramsData = {};
  for (const paramsDataKey in form.value) {
    let isArray = Array.isArray(form.value[paramsDataKey]);
    if (isArray) {
      if (form.value[paramsDataKey].length > 0) {
        // console.log("🌈-----paramsDataKey-----", paramsDataKey);
        if (paramsDataKey == "phone") {
          paramsData[paramsDataKey] = encryption(form.value[paramsDataKey]);
        } else {
          paramsData[paramsDataKey] = form.value[paramsDataKey];
        }
      }
    } else {
      if (form.value[paramsDataKey]) {
        // console.log("🌈-----paramsDataKey2-----", paramsDataKey);
        if (paramsDataKey == "phone") {
          paramsData[paramsDataKey] = encryption(form.value[paramsDataKey]);
        } else {
          paramsData[paramsDataKey] = form.value[paramsDataKey];
        }
      }
    }
  }
  if (form.value.alias && form.value.account) {
    paramsData.account = form.value.account + "@" + form.value.alias;
  }
  const operateLog = {
    operateLogType: "COMPLEX_MANAGEMENT",
    operateType: "创建了",
    operatorTarget: `一个名为“${form.value.name}”的机构`
  };
  try {
    const { code, data, msg } = await courseAdd(paramsData, operateLog);
    console.log("🎁-----data-----", code, data, msg);
    if (code == 200) {
      console.log("🐠-----code-----", code);
      ElMessage({
        message: "创建成功",
        type: "success"
      });
      router.go(-1);
    } else {
      ElMessage({
        message: msg,
        type: "warning"
      });
    }
  } catch (error) {
    console.log("catch-----error-----", error);
  }
  // const [code, data, msg] = await requestTo(courseAdd(paramsData));
  // console.log("🎁-----data-----", code, data, msg);
  // if (code == 200) {
  //   console.log('🐠-----code-----', code);
  //   router.go(-1);
  // }
  getListLoading.value = false;
};
// 实时更新初始密码
const newPassword = ref("");

const generatePassword = (name, phone) => {
  try {
    const initials = pinyin(name || "", {
      pattern: "first",
      toneType: "none",
      type: "array"
    })
      .join("")
      .toLowerCase();

    const phonePart = phone?.slice(-6) || "";
    newPassword.value = `${initials}${phonePart}@`;
  } catch (error) {
    console.error("生成密码失败：", error);
    newPassword.value = "";
  }
};
watch(
  () => [form.value.username, form.value.phone],
  ([name, phone]) => {
    // 前置校验
    if (name && name.length >= 2 && phone && /^1[3-9]\d{9}$/.test(phone)) {
      generatePassword(name, phone);
    } else {
      newPassword.value = "";
    }
  }
);
//文件处理
const fileData = () => {
  //机构营业执照
  let institutionLicense = formFile.value.institutionLicense;
  //资质文件
  let qualificationDocuments = formFile.value.qualificationDocuments;
  // logo
  let logo = formFile.value.logo;
  // 视频
  let video = formFile.value.video;
  // 环境照片
  let environment = formFile.value.environment;
  if (institutionLicense.length > 0) {
    setFilesFn(institutionLicense, "BUSINESS_LICENSE");
  }
  if (qualificationDocuments.length > 0) {
    setFilesFn(qualificationDocuments, "QUALIFICATION_DOCUMENT");
  }
  if (logo.length > 0) {
    form.value.logo = formFile.value.logo[0]?.fileIdentifier || "";
  }
  if (video.length > 0) {
    setFilesFn(qualificationDocuments, "PROMOTIONAL_VIDEO");
  }
  if (environment.length > 0) {
    setFilesFn(qualificationDocuments, "ENVIRONMENT");
  }
};
// 🌈thanks for AnNan!! 🎇🎇🎇🎇🎇🎇
const setFilesFn = (val, type) => {
  for (let index = 0; index < val.length; index++) {
    const element = val[index].fileIdentifier;
    console.log("🌵-----element-----", element);
    form.value.files.push({
      fileType: type,
      fileIdentifier: element,
      sortOrder: index + 1
    });
  }
};
// 验证码倒计时
const countdown = ref(0);
const buttonText = ref("获取验证码");
const isCountingDown = ref(false);

// 获取验证码
const getCaptcha = async item => {
  // 如果正在倒计时，则不执行操作
  if (isCountingDown.value) return;

  try {
    // 调用获取验证码接口
    const response = await getPhonecode({
      phone: encryption(form.value.phone),
      codeType: "VERIFICATION_CODE"
    });

    if (response.code === 200) {
      ElMessage({
        message: "验证码已发送",
        type: "success"
      });

      // 开始倒计时
      isCountingDown.value = true;
      countdown.value = 60;
      buttonText.value = `${countdown.value}s后重新获取`;

      const timer = setInterval(() => {
        countdown.value--;
        buttonText.value = `${countdown.value}s后重新获取`;

        if (countdown.value <= 0) {
          clearInterval(timer);
          buttonText.value = "获取验证码";
          isCountingDown.value = false;
        }
      }, 1000);
    } else {
      ElMessage({
        message: response.msg || "验证码发送失败",
        type: "error"
      });
    }
  } catch (error) {
    console.error("获取验证码失败:", error);
    ElMessage({
      message: "获取验证码失败，请稍后重试",
      type: "error"
    });
  }
};

// 文件上传
const beforeUpload = async (file, item) => {
  // let fileName = file.name.split(".");
  // let fileStyle = ["ppt", "pptx"];
  // if (!fileStyle.includes(fileName[1])) {
  //   ElMessage.error("请上传ppt文件");
  //   return;
  // }
  // console.log("💗beforeUpload---------->33333", file);

  // const isLt2M = file.size / 1024 / 1024 < 10;
  // const allowedTypes = ["image/gif", "image/jpeg", "image/jpg", "image/png"];
  // const fileType = file.type;
  // // console.log(file.size / 1024 / 1024, "00000000000000000000");

  // if (!allowedTypes.includes(fileType))
  //   return ElMessage.error("请上传PNG、JPEG、JPG、GIF格式的图片");

  // if (!isLt2M) return ElMessage.error("文件大小不能超过10M");

  // let { code, data } = await uploadFile(file);
  // if (code === 200) {
  //   // console.log("🌳-----data-----", data);
  //   formFile.value[item].push(data);
  //   console.log("🎁----- formFile.fileData-----", formFile.value[item]);
  // }

  // 判断是否重复
  const isDuplicate = formFile.value[item].some(
    f => f.name === file.name && f.totalSize === file.size
  );
  if (isDuplicate) {
    ElMessage.error("不能上传重复的文件");
    return false;
  }

  let fileType = [];
  switch (item) {
    case "video":
      fileType = ["video"];
      break;
    case "institutionLicense":
      fileType = ["image", "pdf"];
      break;
    case "qualificationDocuments":
      fileType = ["image", "pdf", "word", "excel", "text"];
      break;
    default:
      break;
  }
  let strData = validateFileType(file, fileType);
  let typeImage = validateFileType(file, ["image"]);
  let isSize = isOverSizeLimit(
    file,
    item === "video" ? 800 : typeImage.valid === true ? 10 : 30
  );

  if (item === "logo" || item === "environment") {
    const allowedTypes = ["image/jpeg", "image/jpg", "image/png"];
    const fileTypes = file.type;
    if (!allowedTypes.includes(fileTypes)) {
      strData.valid = false;
      ElMessage.error("请上传jpg、jpeg、png格式的图片");
      return false;
    }
  }

  if (strData.valid === true && isSize.valid === true) {
    try {
      let { code, data } = await uploadFile(file, () => {}, fileType);
      if (code === 200) {
        formFile.value[item].push({
          ...data,
          name: data.name || data.fileName || file.name,
          url: data.url || data.fileUrl || "",
          uid: data.uploadId || Date.now() + Math.random(),
          status: "success" // 关键
        });
      }
    } catch (error) {
      ElMessage({
        message: error.message,
        type: "error"
      });
    }
  } else {
    if (strData.message) {
      ElMessage.error(strData.message);
      return false;
    }
    if (isSize.message) {
      ElMessage.error(isSize.message);
      return false;
    }
  }
};
const dialogVisible = ref(false);
const dialogImageUrl = ref("");
const handlePreview = (file, prop) => {
  dialogImageUrl.value = file.url;
  dialogVisible.value = true;
};
const handleExceed = (files, item) => {
  switch (item) {
    case "logo":
      return ElMessage({
        message: "当前限制上传一张图片",
        type: "error"
      });
    case "video":
      return ElMessage({
        message: "当前限制上传三个视频",
        type: "error"
      });
    case "environment":
      return ElMessage({
        message: "当前限制上传九张图片",
        type: "error"
      });
    case "institutionLicense":
    case "qualificationDocuments":
      return ElMessage({
        message: "当前限制上传九个文件",
        type: "error"
      });
    default:
      break;
  }
};
const imageAndDocs = val => {
  const qualification = [
    // 图片类型
    "image/jpeg",
    "image/png",
    "image/gif",
    "image/bmp",
    "image/webp",
    // PDF
    "application/pdf",
    ".pdf",
    // Word文档
    "application/msword",
    ".doc",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ".docx",
    // PowerPoint文档
    "application/vnd.ms-powerpoint",
    ".ppt",
    "application/vnd.openxmlformats-officedocument.presentationml.presentation",
    ".pptx",
    // Excel文档
    "application/vnd.ms-excel",
    ".xls",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    ".xlsx"
  ].join(",");
  const institution = [
    // 图片类型（扩展名+MIME类型）
    "image/jpeg",
    "image/png",
    "image/gif",
    "image/bmp",
    "image/webp",
    // PDF
    "application/pdf",
    ".pdf"
  ].join(",");
  if (val === "institutionLicense") {
    return institution;
  } else {
    return qualification;
  }
};
//删除文件
const handleClickDetele = (item, index) => {
  console.log("🌈-----item, index-----", item, index);
  // return
  formFile.value[item].splice(index, 1);
  // maxUpload.value.uploadFile = 0;
};
const suffix = ref("");
// 机构别名与账号同步显示
const onInput = (item, val) => {
  // console.log("🦄-----item, val-----", item, val);
  if (val === "alias") {
    suffix.value = "@" + item;
  }
};
//删除文件
const uploadKey = ref(Date.now());
const getDeleted = (item, index) => {
  formFile.value[item].splice(index, 1);
  uploadKey.value = Date.now(); // 关键：强制刷新
};
// 地图
const dialogTableVisible = ref(false);
const locationPostion = ref([116.3912757, 39.906217]);
const selectedLocationMap = ref({}); // 存储不同地址类型的选择位置
const teacherTimeInfo = {
  location_range: 1000
};
const handleMapConfirm = data => {
  console.log("🎉-----handleMapConfirm-----", data);
  if (addressStr.value === "经营地址") {
    form.value.operatingAddress = data.address;
    form.value.operatingAddressLatitude = data.point.lat;
    form.value.operatingAddressLongitude = data.point.lng;
    // 保存经营地址的选择位置
    selectedLocationMap.value.operatingAddress = data;
  } else if (addressStr.value === "注册地址") {
    form.value.registeredAddress = data.address;
    form.value.registeredAddressLatitude = data.point.lat;
    form.value.registeredAddressLongitude = data.point.lng;
    // 保存注册地址的选择位置
    selectedLocationMap.value.registeredAddress = data;
  }
};
// 打开地图
let addressStr = ref("");
const mapOpen = val => {
  val === "operatingAddress"
    ? (addressStr.value = "经营地址")
    : (addressStr.value = "注册地址");
  dialogTableVisible.value = true;
};
// 页面下拉列表查询
const selectOptions = async val => {
  try {
    let { code, data } = await institutionFindById({ parentId: val.parentId });
    if (code === 200) {
      formData.value.map(item => {
        if (item.prop === val.prop) {
          item.opt = data;
          if (
            val.prop === "organizationCategory" ||
            val.prop === "trainingCategory"
          ) {
            item.opt = (Array.isArray(data) ? data : []).map(opt => ({
              value: opt.name,
              label: opt.name
            }));
          }
        }
      });
    }
  } catch (error) {
    ElMessage({
      message: "获取失败",
      type: "error"
    });
  }
};
// 多选下拉列表
const selectOptions2 = (val, prop, item) => {
  form.value[prop] = val;
};
// 下拉选择器
const onInputSelect = async (value, prop, options) => {
  const selectedOption = options.find(item => item.name === value);
  form.value[prop] = selectedOption?.name || value; // 保证是 name
};
const handleRemove = (file, item) => {
  console.log("🌳-----file-----", file);
  const idx = formFile.value[item].findIndex(
    i => i.url === file.url && i.uid === file.uid
  );
  if (idx !== -1) {
    formFile.value[item].splice(idx, 1);
  } else {
    formFile.value[item].splice(0, 1);
  }
};
const disabledAfterToday = date => {
  return date.getTime() > Date.now();
};
</script>

<template>
  <div class="main">
    <el-scrollbar class="scrollbar">
      <!-- <div class="header">
                      <el-input
        <p>基本信息</p>
        <p>{{ form.time }}</p>
      </div>
      <div class="title_rigth">
        <p />
        <p>{{ form.institutionID }}</p>
      </div>
    </div> -->

      <el-form ref="formRef" :model="form" :rules="rules" label-width="">
        <div>
          <!-- <el-form ref="formRef" :model="form" :rules="rules"> -->
          <div class="title-text">基本信息</div>
          <el-descriptions title="" :column="2" border :label-width="'200px'">
            <el-descriptions-item
              v-for="(item, index) in formData"
              :key="index"
              label-align="right"
              label-class-name="my-label"
              :span="item.span || 1"
            >
              <template #label>
                <span v-if="item.check" class="star">*</span>{{ item.label }}
              </template>
              <template #default>
                <div style="min-width: 335px">
                  <el-form-item
                    :prop="item.prop"
                    :inline-message="item.check"
                    style="margin-bottom: 0"
                    :show-message="true"
                    error-placement="right"
                  >
                    <!-- input输入 -->
                    <template v-if="item.type === 'input'">
                      <el-input
                        v-model.trim="form[item.prop]"
                        :placeholder="item.placeholder"
                        :style="{ width: item.width }"
                        :maxlength="item.maxlength || 50"
                        @input="value => onInput(value, item.prop)"
                      />
                      <!-- 获取验证码 -->
                      <!-- <div class="Vacode">
                        <el-button
                          v-if="item.hasButton"
                          @click="getCaptcha(item)"
                        >
                          {{ item.buttonText }}
                        </el-button>
                      </div> -->
                    </template>

                    <!-- text -->
                    <template v-if="item.type === 'text'">
                      <div style="color: #a8a8a8">{{ form[item.prop] }}</div>
                    </template>
                    <!-- textarea输入 -->
                    <template v-if="item.type === 'textarea'">
                      <el-input
                        v-model="form[item.prop]"
                        :rows="5"
                        type="textarea"
                        :maxlength="1000"
                        show-word-limit
                        :placeholder="item.placeholder"
                      />
                    </template>
                    <!-- 下拉框（单选） -->
                    <template v-if="item.type === 'select'">
                      <el-select
                        v-model="form[item.prop]"
                        :placeholder="item.placeholder"
                        @focus="selectOptions(item)"
                        @change="
                          value => onInputSelect(value, item.prop, item.opt)
                        "
                      >
                        <el-option
                          v-for="items in item.opt"
                          :key="items.id"
                          :label="items.name"
                          :value="items.name"
                        />
                      </el-select>
                    </template>
                    <!-- 下拉框（多选） -->
                    <template v-if="item.type === 'select2'">
                      <el-select-v2
                        v-model="form[item.prop]"
                        :options="item.opt"
                        :placeholder="item.placeholder"
                        multiple
                        @focus="selectOptions(item)"
                        @change="
                          value =>
                            selectOptions2(value, item.prop, form[item.prop])
                        "
                      />
                    </template>
                    <!-- 日期选择 -->
                    <template v-if="item.type === 'date'">
                      <el-date-picker
                        v-model="form[item.prop]"
                        type="date"
                        placeholder="请选择成立时间"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                        style="width: 100%"
                        :disabled-date="disabledAfterToday"
                      />
                    </template>
                    <!-- 地图 -->
                    <template v-if="item.type === 'map'">
                      <div class="selsect-pos">
                        <div class="cover" @click="mapOpen(item.prop)" />
                        <el-input
                          v-model="form[item.prop]"
                          class="input-part"
                          placeholder="请选择地址"
                          readonly
                        >
                          <template #suffix>
                            <div class="pos-icon">
                              <img :src="ImgPos" class="wfull h-full" alt="">
                            </div>
                          </template>
                        </el-input>
                      </div>
                    </template>
                  </el-form-item>
                </div>
              </template>
            </el-descriptions-item>
          </el-descriptions>
          <!-- </el-form> -->

          <div class="title-text">机构介绍</div>
          <el-descriptions :column="1" border :label-width="'200px'">
            <el-descriptions-item
              v-for="(item, index) in institutionIntroduction"
              :key="index"
              label-align="right"
              label-class-name="my-label"
              :span="item.span || 1"
            >
              <template #label>
                <span v-if="item.check" class="star">*</span>{{ item.label }}
              </template>
              <template #default>
                <el-form-item
                  :prop="item.prop"
                  :inline-message="item.check"
                  style="margin-bottom: 0"
                  :show-message="true"
                  error-placement="right"
                >
                  <!-- textarea输入 -->
                  <template v-if="item.type === 'textarea'">
                    <el-input
                      v-model="form[item.prop]"
                      :rows="3"
                      type="textarea"
                      :maxlength="200"
                      show-word-limit
                      :placeholder="item.placeholder"
                    />
                  </template>

                  <!-- 富文本 -->
                  <template v-else-if="item.type === 'editor'">
                    <div style="width: 100%; border: 1px solid #e2e2e2">
                      <RichEditor v-model="form[item.prop]" height="350px" />
                    </div>
                  </template>

                  <!-- 示例：上传组件 -->
                  <template v-else-if="item.type === 'upload'">
                    <el-upload
                      :key="
                        item.prop === 'video'
                          ? uploadKey + item.prop
                          : item.prop
                      "
                      action="#"
                      :class="{
                        hideUploadBtn: formFile[item.prop].length >= item.limit
                      }"
                      :show-file-list="item.prop === 'video' ? false : true"
                      :file-list="formFile[item.prop]"
                      :http-request="() => {}"
                      :limit="item.limit"
                      :on-exceed="file => handleExceed(file, item.prop)"
                      :accept="item.prop === 'video' ? 'video/*' : 'image/*'"
                      :list-type="item.type2 ? '' : 'picture-card'"
                      :before-upload="file => beforeUpload(file, item.prop)"
                      :before-remove="file => handleRemove(file, item.prop)"
                      :on-preview="file => handlePreview(file, item.prop)"
                    >
                      <template v-if="item.type2 === 'video'">
                        <img :src="uploadImg" alt="">
                      </template>
                      <template v-else>
                        <el-icon><Plus /></el-icon>
                      </template>
                    </el-upload>

                    <template v-if="item.type2">
                      <template
                        v-for="(item2, index2) in formFile[item.prop]"
                        :key="index2"
                      >
                        <FileItem
                          isNeedDelte
                          :data="item2"
                          :index="index2"
                          style="width: 50%; min-width: 130px"
                          @delete="getDeleted(item.prop, index2)"
                        />
                      </template>
                    </template>
                    <div class="upload_text">{{ item.text }}</div>
                  </template>
                </el-form-item>
              </template>
            </el-descriptions-item>
          </el-descriptions>

          <div class="title-text">资质信息</div>
          <el-descriptions :column="1" border :label-width="'200px'">
            <el-descriptions-item
              v-for="(item, index) in qualifications"
              :key="index"
              label-align="right"
              label-class-name="my-label"
            >
              <template #label>
                <span v-if="item.check" class="star">*</span>{{ item.label }}
              </template>
              <template #default>
                <el-form-item
                  :prop="item.prop"
                  :inline-message="item.check"
                  style="margin-bottom: 0"
                  :show-message="true"
                  error-placement="right"
                >
                  <template v-if="item.type === 'upload'">
                    <el-upload
                      :key="uploadKey + item.prop"
                      action="#"
                      :show-file-list="false"
                      class="upload-demo"
                      :limit="9"
                      :on-exceed="file => handleExceed(file, item.prop)"
                      :http-request="() => {}"
                      :accept="imageAndDocs(item.prop)"
                      :before-upload="file => beforeUpload(file, item.prop)"
                    >
                      <img :src="uploadImg" alt="">
                    </el-upload>

                    <template
                      v-for="(item2, index2) in formFile[item.prop]"
                      :key="index2"
                    >
                      <FileItem
                        isNeedDelte
                        :data="item2"
                        :index="index2"
                        style="width: 50%; min-width: 130px"
                        @delete="getDeleted(item.prop, index2)"
                      />
                    </template>
                    <div class="upload_text">{{ item.text }}</div>
                  </template>
                </el-form-item>
              </template>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="account_management">
          <div>管理员账号</div>
          <div class="ChangePwd">
            <div v-if="newPassword">
              初始密码为：<span>{{ newPassword }}</span>
            </div>
            <span>密码生成规则：姓名首字母+手机号后6位+@</span>
          </div>
        </div>
        <el-descriptions title="" :column="1" border :label-width="'200px'">
          <el-descriptions-item
            v-for="(item, index) in formfootData"
            :key="index"
            label-align="right"
            label-class-name="my-label"
          >
            <template #label>
              <span v-if="item.check" class="star">*</span>{{ item.label }}
            </template>
            <template #default>
              <el-form-item
                :prop="item.prop"
                :inline-message="item.check"
                style="margin-bottom: 0"
                :show-message="true"
                error-placement="right"
              >
                <!-- text -->
                <template v-if="item.type === 'text'">
                  <div style="color: #a8a8a8">{{ form[item.prop] }}</div>
                </template>
                <!-- input输入 -->
                <template v-if="item.type === 'input'">
                  <el-input
                    v-model.trim="form[item.prop]"
                    :placeholder="item.placeholder"
                    :style="{ width: item.width }"
                  />
                </template>
              </el-form-item>
            </template>
          </el-descriptions-item>
        </el-descriptions>
        <!-- <el-form ref="formRef2" :model="form" :rules="rules"> -->
        <el-descriptions
          title=""
          :column="2"
          border
          :label-width="'200px'"
          class="formfoot"
        >
          <el-descriptions-item
            v-for="(item, index) in formfootDataTow"
            :key="index"
            label-align="right"
            label-class-name="my-label"
          >
            <template #label>
              <template v-if="item.show">
                <span v-if="item.check" class="star">*</span>{{ item.label }}
              </template>
            </template>
            <el-form-item
              :prop="item.prop"
              :inline-message="item.check"
              style="margin-bottom: 0"
              :show-message="true"
              error-placement="right"
            >
              <!-- input输入 -->
              <div
                v-if="item.type === 'input' && item.show === true"
                class="input_box"
              >
                <el-input
                  v-model.trim="form[item.prop]"
                  :placeholder="item.placeholder"
                  :style="{ width: item.width }"
                />
                <!-- 获取验证码 -->
                <div v-if="item.hasButton === true" class="Vacode">
                  <el-button
                    type="primary"
                    :disabled="isCountingDown"
                    @click="getCaptcha(item)"
                  >
                    {{ buttonText }}
                  </el-button>
                </div>
              </div>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
      </el-form>
    </el-scrollbar>
    <!-- </el-form> -->
    <div class="footer">
      <el-button @click="reset">取消</el-button>
      <el-button type="primary" @click="submitForm">确定新建</el-button>
    </div>
    <Map
      v-if="dialogTableVisible"
      v-model="dialogTableVisible"
      :center="locationPostion"
      :selected-location="
        addressStr === '经营地址'
          ? selectedLocationMap.operatingAddress
          : selectedLocationMap.registeredAddress
      "
      :checkInResult="teacherTimeInfo"
      :addressStr="addressStr"
      @confirm="handleMapConfirm"
    />
    <el-dialog v-model="dialogVisible">
      <img class="w-full" :src="dialogImageUrl" alt="Preview Image">
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.main {
  padding: 20px;
  background: #fff;
}
.scrollbar {
  // padding-top: 20px;
  // padding: 20px;
  height: calc(100vh - 190px);
  background-color: #fff;
}
.Vacode {
  margin-left: 10px;
  .el-button {
    height: 32px;
    white-space: nowrap;
  }
}
.input_box {
  display: flex;
  // align-items: center;
  // justify-items: left;
  // justify-content: left;
  // background: #91a6bb;
}
.ChangePwd {
  display: flex;
  justify-content: right;
  // width: 30%;
  // margin-top: 10px;
  // font-size: smaller;
  // font-weight: 600;
  // white-space: nowrap;
}
.header {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 20px;

  .title_left {
    display: flex;

    :nth-child(2) {
      margin-left: 40px;
    }
  }

  .title_rigth {
    display: flex;

    :nth-child(2) {
      margin-left: 40px;
    }
  }
}

.account_management {
  // display: flex;
  // justify-content: space-between;
  :nth-child(2) {
    margin-left: 20px;
    margin-right: 15px;
  }
  & > div:nth-of-type(1) {
    display: flex;
    // background-color: red;

    font-weight: 500;
    color: #0e1832;
    font-family: Source Han Sans CN;
    font-size: 16px;
    align-items: center;
    margin: 20px 0 20px 10px;

    &::after {
      content: "";
      display: block;
      width: 100%;
      flex: 1;
      height: 1px;
      background: #e4e7ed;
      margin-left: 16px;
    }
  }
  & > div:nth-of-type(2) {
    text-align: right;
  }
}

.footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  font-size: 14px;
  color: #fff;

  .footer_left {
    display: flex;
    .but {
      padding: 8px 18px;
      cursor: pointer;
      // background: #409EFF;
      border-radius: 8px;
    }
    :nth-child(2) {
      margin-left: 20px;
      margin-right: 20px;
      // background: #ff0000;
      // background: #409EFF;
    }
  }

  .footer_right {
    padding: 8px 18px;
    cursor: pointer;
    background: #409eff;
    border-radius: 8px;
  }
}

:deep(.my-label) {
  background: #fff !important;
  min-width: 150px !important;
}

.star {
  margin-right: 3px;
  color: red;
}

.input_box {
  display: flex;
  // justify-content: space-between;
  width: 100%;
}
.upload-demo {
  display: flex;
  align-items: center;
}
.fileOther {
  // margin-top: 26px;
  margin-left: 20px;
  display: flex;
  align-items: center;
  line-height: 22px;
  width: 420px;
  // margin-bottom: 56px;
  .title {
    color: #0e1832;
    font-family: Source Han Sans CN;
    font-size: 16px;
    font-weight: 400;
    height: 24px;
    white-space: nowrap;
  }
  .link {
    width: 20px;
    height: 20px;
    margin: 0 10px;
    cursor: pointer;
  }
  .fileName {
    color: #3876fd;
    font-family: Source Han Sans CN;
    font-size: 16px;
    font-weight: 400;
    height: 24px;
    cursor: pointer;
    // width: 350px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .linkDetele {
    width: 20px;
    height: 20px;
    margin-left: 10px;
    cursor: pointer;
  }
}
:deep(.el-form-item__content) {
  display: inline-block;
  width: 300px;
  .upload_text {
    display: inline-block;
    font-size: 12px;
    position: relative;
    top: 5px;
    color: #8c939d;
  }
}
.title-text {
  color: #0e1832;
  font-family: Source Han Sans CN;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  margin: 20px 0 20px 10px;

  &::after {
    content: "";
    flex: 1;
    height: 1px;
    background: #e4e7ed;
    margin-left: 16px;
  }
}
.upload_text {
  display: inline-block;
  font-size: 12px;
  position: relative;
  top: 5px;
  color: #8c939d;
}
:deep(
  .el-descriptions__body
    .el-descriptions__table.is-bordered
    .el-descriptions__cell
) {
  border: 0px !important;
}
.selsect-pos {
  position: relative;
  width: 100%;
  // height: 60px;
  // background: red;
  .cover {
    cursor: pointer;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    // background: red;
    width: 100%;
    height: 100%;
  }

  .pos-icon {
    width: 24px;
    height: 24px;
  }
}
:deep(.el-form-item__label) {
  width: 150px !important;
  text-align: right;
  padding: 0 12px !important;
}
:deep(.hideUploadBtn .el-upload--picture-card) {
  display: none;
}
:deep(.el-form-item__error) {
  position: absolute;
  left: 0;
  top: 100%;
  white-space: nowrap;
  z-index: 10;
}
:deep(.el-form-item__content) {
  position: relative;
  min-height: 40px; // 仍建议设置
}
:deep(.formfoot:last-child .el-descriptions__table) {
  td:nth-child(1) {
    min-width: 200px !important;
  }
  display: flex;
  td:nth-child(2) {
    width: 200px;
  }
  td:nth-child(3) {
    width: 10px !important;
    display: block !important;
    margin-top: 10px;
    text-align: right;
  }
  td:nth-child(4) {
    text-align: left;
  }
}
</style>
