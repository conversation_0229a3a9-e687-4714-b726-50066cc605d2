import { http } from "@/utils/http";

/** 评语库列表查询 */
export const commentFindAll = params => {
  return http.request(
    "get",
    "/platform/commentLibrary/findAll",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};

/** 根据Id查询 */
export const commentFindId = params => {
  return http.request(
    "get",
    "/platform/commentLibrary/findById",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};

/** 根据Id删除*/
export const commentdeleteId = (data, operateLog) => {
  return http.request(
    "post",
    "/platform/commentLibrary/delete",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "PLATFORM_SETTINGS",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};

/** 新增 */
export const commentAdd = (data, operateLog) => {
  return http.request(
    "post",
    "/platform/commentLibrary/save",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "PLATFORM_SETTINGS",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};

/** 修改 */
export const commentEdit = (data, operateLog) => {
  return http.request(
    "post",
    "/platform/commentLibrary/update",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "PLATFORM_SETTINGS",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
