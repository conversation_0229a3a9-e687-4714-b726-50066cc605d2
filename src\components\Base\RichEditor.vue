<script setup lang="ts">
import { uploadFile } from "@/utils/upload/upload.js";
import { onBeforeUnmount, ref, shallowRef, defineProps, computed } from "vue";
import "@wangeditor/editor/dist/css/style.css";
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";
import { getObjVal, to } from "@iceywu/utils";
import { ElLoading } from "element-plus";

defineOptions({
  name: "PicUpload"
});
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: true
  },
  height: {
    type: String,
    default: "500px"
  },
  readOnly: {
    type: Boolean,
    default: false
  },
  isShield: {
    type: Boolean,
    default: false
  },
  excludeKeys: {
    type: Array as PropType<string[]>,
    default: () => []
  },
  lineHeight: {
    type: [String, Number],
    default: null
  }
});

const emit = defineEmits(["handleChange"]);

const wangeditorStyle = computed(() => {
  return props.lineHeight ? { "--custom-line-height": props.lineHeight } : {};
});

const valueHtml = defineModel<string>({
  default: ""
});

const mode = "default";
// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef();

// 内容 HTML
// const valueHtml = ref(
//   "<p>仅提供代码参考，暂不可上传图片，可根据实际业务改写</p>"
// );

// 屏蔽所有的菜单组的key
const shield = [
  // "codeBlock", // 代码块
  // "insertTable", // 插入表格
  // "insertImage", // 网络图片
  // "uploadImage", // 上传图片
  // "insertVideo", // 插入视频
  // "uploadVideo" // 上传视频

  "codeBlock", //（代码块）
  "blockquote", //（引用块）
  // "insertLink", //（插入链接）
  "insertTable", //（插入表格）
  "group-image", //（图片组，含插入/上传/编辑图片）
  "group-video", //（视频组，含插入/上传视频）
  "group-link", //（链接组，含插入/编辑/取消链接）
  "fullScreen"
];

const toolbarConfig: any = {
  excludeKeys: props.isShield ? shield : "fullScreen"
};

toolbarConfig.excludeKeys =
  props.excludeKeys.length > 0 ? props.excludeKeys : [];
const editorConfig = {
  placeholder: "请输入内容...",
  MENU_CONF: {},
  readOnly: props.readOnly,
  hoverbarKeys: {
    // 文本类型元素的悬浮工具栏配置
    text: {
      menuKeys: [
        "headerSelect",
        "insertLink",
        "bulletedList",
        "|",
        "bold",
        "through",
        "color",
        "bgColor",
        "clearStyle"
      ].filter(key => !props.excludeKeys.includes(key))
    }
  }
};

// 更多详细配置看 https://www.wangeditor.com/v5/menu-config.html#%E4%B8%8A%E4%BC%A0%E5%9B%BE%E7%89%87
editorConfig.MENU_CONF["uploadImage"] = {
  // 服务端上传地址，根据实际业务改写
  server: "",
  // form-data 的 fieldName，根据实际业务改写
  fieldName: "file",
  // 选择文件时的类型限制，根据实际业务改写
  allowedFileTypes: ["image/png", "image/jpg", "image/jpeg"],
  // 自定义上传
  async customUpload(file: File, insertFn: any) {
    const loading = ElLoading.service({ text: "上传中..." }) as any;
    const result = (
      await to(
        uploadFile(file, res => {
          const { percent, stage = "upload" } = res;
          loading.text = `上传中...${percent}%`;
        })
      )
    )[1] as any;
    console.log("🍭-----result-----", result);
    loading.close();
    // const { url, name } = result || {};
    const url = getObjVal(result, "data.url", "");
    const name = getObjVal(result, "data.fileName", "");
    console.log("🐬-----url, name-----", url, name);
    insertFn(url, name, url);
  }
};
editorConfig.MENU_CONF["uploadVideo"] = {
  // 服务端上传地址，根据实际业务改写
  server: "",
  // form-data 的 fieldName，根据实际业务改写
  fieldName: "file",
  // 选择文件时的类型限制，根据实际业务改写
  allowedFileTypes: ["video/*"],
  // 自定义上传
  async customUpload(file: File, insertFn: any) {
    const loading = ElLoading.service({ text: "上传中..." }) as any;
    const result = (
      await to(
        uploadFile(file, res => {
          const { percent, stage = "upload" } = res;
          loading.text = `上传中...${percent}%`;
        })
      )
    )[1] as any;
    console.log("🍭-----result-----", result);
    loading.close();
    // const { url, name } = result || {};
    const url = getObjVal(result, "data.url", "");
    const name = getObjVal(result, "data.fileName", "");
    console.log("🐬-----url, name-----", url, name);
    insertFn(url, name, url);
  }
};

const handleCreated = editor => {
  // 记录 editor 实例，重要！
  editorRef.value = editor;
  // 打印 hoverbarKeys 配置
  //  console.log('编辑器 hoverbarKeys 配置:', editor.getConfig().hoverbarKeys);
};

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value;
  if (editor == null) return;
  editor.destroy();
});

const onChange = (val: any) => {
  let toolbar = editorRef.value;
  const allMenuKeys = toolbar.getAllMenuKeys();
  // console.log("🎉-----allMenuKeys-----", allMenuKeys);
  const valTemp = val.getHtml();
  emit("handleChange", valTemp);
};

// 自定义粘贴处理器
const isPasting = ref(false);
const customPaste = (editor: any, event: any) => {
  // 在preventDefault之前先获取内容
  const clipboardData = event.clipboardData;
  const html = clipboardData.getData("text/html");
  const text = clipboardData.getData("text/plain");
  event.preventDefault(); // 阻止默认粘贴行为
  // 检测是否已处理粘贴内容
  isPasting.value = true;
  try {
    // 阻止默认行为
    event.preventDefault();
    if (html) {
      // 如果有HTML内容，进行处理
      if (html) {
        console.log("开始处理HTML内容");
        // 创建临时DOM元素
        const tempDiv = document.createElement("div");
        tempDiv.innerHTML = html;

        // 遍历所有元素，执行通用清理
        const elements = tempDiv.querySelectorAll("*");
        elements.forEach(el => {
          const element = el as HTMLElement;
          // 原有逻辑：移除颜色，保留
          if (element.style.color) {
            element.style.removeProperty("color");
          }
        });

        if (props.lineHeight) {
          // 1. 将标题等块级元素替换为<p>
          const tagsToReplace = [
            "h1",
            "h2",
            "h3",
            "h4",
            "h5",
            "h6",
            "blockquote"
          ];
          tagsToReplace.forEach(tag => {
            const headingElements = tempDiv.querySelectorAll(tag);
            headingElements.forEach(oldEl => {
              const p = document.createElement("p");
              p.innerHTML = oldEl.innerHTML; // 移动内容
              oldEl.parentNode.replaceChild(p, oldEl);
            });
          });

          const elementsToStyle = tempDiv.querySelectorAll("p, span");
          elementsToStyle.forEach(el => {
            const element = el as HTMLElement;
            element.style.fontSize = "14px";
            element.style.lineHeight = String(props.lineHeight);
            element.style.margin = "5px";
          });
        }

        // 获取清理后的 HTML 字符串
        const cleanHtml = tempDiv.innerHTML;

        // 将处理后的HTML插入编辑器
        editor.dangerouslyInsertHtml(cleanHtml);
      } else if (text) {
        // 处理纯文本内容
        editor.insertText(text);
      }
    }
    // 阻止事件传播
    event.stopPropagation();
  } finally {
    // 重置标志
    isPasting.value = false;
  }
};
</script>

<template>
  <div class="wangeditor" :style="wangeditorStyle">
    <Toolbar
      v-if="props.isOpen"
      :editor="editorRef"
      :defaultConfig="toolbarConfig"
      :mode="mode"
      style="border-bottom: 1px solid #ccc"
    />
    <Editor
      v-model="valueHtml"
      :defaultConfig="editorConfig"
      :mode="mode"
      :style="{
        'height': props.height,
        'overflow-y': 'hidden',
        'width': `${100}%`
      }"
      @on-created="handleCreated"
      @on-change="onChange"
      @custom-paste="customPaste"
    />
  </div>
</template>

<style lang="scss" scoped>
.table_top {
  background-color: #fff;
}
.wangeditor {
  flex: 1;
}
:deep(.w-e-text-container) {
  .w-e-scroll {
    p {
      margin: 5px !important;
    }
  }
}
.wangeditor[style*="--custom-line-height"]
  :deep(.w-e-text-container .w-e-scroll p) {
  line-height: var(--custom-line-height);
}
:deep(.w-e-text-placeholder) {
  left: 14px;
  top: 1px;
}
</style>
