<script setup>
import { useRouter, useRoute } from "vue-router";
import { onMounted, ref } from "vue";
import { formatTime } from "@/utils/index";
import {
  findByStudentAssignmentId,
  findStudentAssignment
} from "@/api/course.js";
import { requestTo } from "@/utils/http/tool";
import { to } from "@iceywu/utils";
import { ElMessage, ElMessageBox } from "element-plus";
import ImgList from "@/components/Base/list1.vue";

const props = defineProps({
  homeworkData: {
    type: Object,
    default: () => {}
  }
});
const router = useRouter();
const route = useRoute();
const detailShow = ref(false);
const deteilInfo = ref({
  textarea: "",
  score: 0,
  workArea: "",
  name: "",
  files: []
});
const fileListAPi = ref([]);
const srcList = ref([]);
const srcId = ref(0);
const showPreview = ref(false);
// 获取数据（根据学生实践感悟id查实践感悟批改分数内容）
const getFindByStudentAssignmentId = async () => {
  const params = {
    studentAssignmentId: Number(props.homeworkData?.homeworkId)
  };
  const [err, result] = await to(findByStudentAssignmentId(params));
  // console.log("🦄result----111-------------------------->", result);
  if (result?.code === 200) {
    deteilInfo.value.score = result?.data?.score || 0;
    deteilInfo.value.textarea = result?.data?.content;
  } else {
    console.log("获取失败");
  }
};
// 获取数据(查询学生实践感悟)
const getFindStudentAssignment = async () => {
  const params = {
    studentId: Number(props.homeworkData?.studentId),
    coursePeriodId: Number(route.query.periodId)
  };
  const [err, result] = await to(findStudentAssignment(params));
  // console.log("🦄result----111----44444---------------------->", result);
  if (result?.code === 200) {
    deteilInfo.value.name = result?.data?.student?.name;
    deteilInfo.value.workArea = result?.data?.content;
    if (result?.data?.files?.length) {
      result?.data?.files?.map(item => {
        fileListAPi.value.push(item?.uploadFile);
        srcList.value.push(item?.uploadFile?.url);
      });
    }
    deteilInfo.value.files = result?.data?.files;
  } else {
    console.log("获取失败");
  }
};
// 图片预览
const handleClick = id => {
  showPreview.value = true;
  srcId.value = id;
};
onMounted(() => {
  getFindByStudentAssignmentId();
  getFindStudentAssignment();
});
</script>

<template>
  <div class="evaluate-detail">
    <div class="user-comments">
      <div class="evaluate">
        <div class="work-content">
          <div class="title">
            <div class="text">
              <div class="name">{{ "实践感悟描述" }}</div>
            </div>
          </div>
          <!-- <el-input
            v-model="deteilInfo.workArea"
            type="textarea"
            resize="none"
            disabled
            class="area"
            :placeholder="
              deteilInfo?.workArea ? deteilInfo?.workArea : '暂无数据'
            "
          /> -->
          <div class="area">
            {{ deteilInfo.workArea || "暂无数据" }}
          </div>
          <!-- <div class="area" style="white-space: pre-wrap;">{{ deteilInfo.workArea || '暂无数据' }}</div> -->
        </div>
        <div class="pic">
          <div class="title">
            <div class="text">实践感悟配图</div>
          </div>
          <ImgList
            v-if="fileListAPi?.length"
            :imgList="fileListAPi"
            :srcList="srcList"
            :itemCount="fileListAPi?.length"
          />
          <el-empty v-else description="暂无数据" />
          <!-- <div v-if="fileListAPi?.length" class="banner">
            <el-carousel
              :interval="4000"
              type="card"
              height="200px"
              :autoplay="false"
            >
              <el-carousel-item
                v-for="(item, index) in fileListAPi"
                :key="item"
              >
                <img
                  :src="item.url"
                  class="h-full"
                  @click="handleClick(index)"
                >
              </el-carousel-item>
            </el-carousel>
            <el-image-viewer
              v-if="showPreview"
              :url-list="srcList"
              show-progress
              :initial-index="srcId"
              @close="showPreview = false"
            />
          </div> -->
        </div>
      </div>
    </div>
    <div class="replies">
      <div class="content">
        <div class="text-area">
          <div class="title">
            <div class="text">
              {{ "实践感悟批改" }}
            </div>
            <div class="text">
              <span class="name">{{ `实践感悟分数` }}</span>
              <span>{{ deteilInfo.score }}</span>
            </div>
          </div>
          <el-input
            v-model="deteilInfo.textarea"
            type="textarea"
            resize="none"
            :placeholder="
              deteilInfo?.textarea ? deteilInfo?.textarea : '暂无数据'
            "
            disabled
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.evaluate-detail {
  font-size: 14px;
  background-color: #fff;
  width: 100%;
  height: 520px;
  padding: 30px 30px;
  .user-comments {
    width: 100%;
    margin-bottom: 50px;
    .title {
      width: 100%;
      display: flex;
      margin-bottom: 20px;
      .text {
        display: flex;
        white-space: nowrap;
      }

      .name {
        margin-right: 50px;
      }
    }
    .evaluate {
      width: 100%;
      display: flex;
      justify-content: space-between;

      .work-content {
        width: 49%;
        .area {
          width: 100%;
          height: 206px;
          white-space: pre-wrap;
          height: 206px;
          overflow-y: auto;
          color: #b1b1b1;
        }
      }
      :deep(.el-textarea__inner) {
        height: 206px;
      }
      .pic {
        width: 49%;
        height: 170px;
        :deep(.el-carousel--horizontal) {
          height: 170px;
        }
      }
    }
  }
  .replies {
    width: 100%;
    .title {
      width: 100%;
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }
    .content {
      width: 100%;
      display: flex;
      justify-content: space-between;
      .text-area {
        width: 100%;
        :deep(.el-textarea__inner) {
          height: 150px;
        }
        .title {
          width: 100%;
          display: flex;
          justify-content: space-between;
          .text {
            display: flex;
          }
          .name {
            margin-right: 18px;
            white-space: nowrap;
          }
        }
      }
    }
  }
  :deep(.el-carousel__item) {
    margin: 0;
    line-height: 200px;
    text-align: center;
  }
}
:deep(.el-empty) {
  --el-empty-padding: 0;
}
</style>
