<script setup>
import { transformI18n } from "@/plugins/i18n";
import { findRouteByPath, getParentPaths } from "@/router/utils";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import { isEqual } from "@pureadmin/utils";
import { onMounted, ref, toRaw, watch } from "vue";
import { useRoute, useRouter } from "vue-router";

const route = useRoute();
const levelList = ref([]);
const router = useRouter();
const routes = router.options.routes;
const multiTags = useMultiTagsStoreHook().multiTags;
const getIsSameInWhiteList = (route, target) => {
  const { query, path } = route;
  const whiteList = [
    "/welcome/homeEdit",
    "/accountNumber/components/editInformation",
    "/lecturerNumber/components/editInformation",
    "/account/platform/account/add"
  ];
  return whiteList.includes(path) && path === target.path;
};

const getBreadcrumb = () => {
  // 当前路由信息
  let currentRoute;

  if (Object.keys(route.query).length > 0) {
    multiTags.forEach(item => {
      if (
        isEqual(route.query, item?.query) ||
        getIsSameInWhiteList(route, item)
      ) {
        currentRoute = toRaw(item);
      }
    });
  } else if (Object.keys(route.params).length > 0) {
    multiTags.forEach(item => {
      if (isEqual(route.params, item?.params)) {
        currentRoute = toRaw(item);
      }
    });
  } else {
    currentRoute = findRouteByPath(router.currentRoute.value.path, routes);
  }

  // 当前路由的父级路径组成的数组
  const parentRoutes = getParentPaths(
    router.currentRoute.value?.name,
    routes,
    "name"
  );
  // 存放组成面包屑的数组
  const matched = [];

  // 获取每个父级路径对应的路由信息
  parentRoutes.forEach(path => {
    if (path !== "/") matched.push(findRouteByPath(path, routes));
  });

  matched.push(currentRoute);
  matched.forEach((item, index) => {
    if (currentRoute?.query || currentRoute?.params) return;
    if (item?.children) {
      item.children.forEach(v => {
        if (v?.meta?.title === item?.meta?.title) {
          matched.splice(index, 1);
        }
      });
    }
  });

  matched.map((it, index) => {
    multiTags.map(item => {
      if (item.query) {
        if (item?.name == it?.name) {
          it.query = item.query;
        }
      }
    });
  });

  localStorage.setItem("storeRoutingData", JSON.stringify(multiTags));

  levelList.value = matched.filter(
    item => item?.meta && item?.meta.title !== false
  );
};

const handleLink = item => {
  const { redirect, name, path } = item;
  if (redirect) {
    router.push(redirect);
  } else {
    if (name) {
      if (item.query) {
        router.push({
          name,
          query: item.query
        });
      } else if (item.params) {
        router.push({
          name,
          params: item.params
        });
      } else {
        router.push({ name });
      }
    } else {
      router.push({ path });
    }
  }
};

onMounted(() => {
  getBreadcrumb();
});

watch(
  () => route.path,
  () => {
    getBreadcrumb();
  },
  {
    deep: true
  }
);
</script>

<template>
  <el-breadcrumb class="!leading-[50px] select-none" separator="/">
    <transition-group name="breadcrumb">
      <el-breadcrumb-item
        v-for="item in levelList"
        :key="item.path"
        class="!inline !items-stretch"
      >
        <a @click.prevent="handleLink(item)">
          {{ transformI18n(item.meta.title) }}
        </a>
      </el-breadcrumb-item>
    </transition-group>
  </el-breadcrumb>
</template>
