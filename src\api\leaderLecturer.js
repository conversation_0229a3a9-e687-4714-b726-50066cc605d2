import { http } from "@/utils/http";
// 领队讲师/列表查询
export const ledeterAll = params => {
  return http.request(
    "get",
    "/platform/leaderLecturer/findAll",
    { params },
    { isNeedEncrypt: true }
  );
};
// 领队讲师/列表详情
export const ledeterList = params => {
  return http.request(
    "get",
    "/platform/leaderLecturer/findById",
    { params },
    { isNeedEncrypt: true }
  );
};
// 领队讲师/是否冻结
export const isFreeze = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/leaderLecturer/isFreeze",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ACCOUNT_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
// 领队讲师/详情课期
export const ledeterinfoList = params => {
  return http.request(
    "get",
    "/platform/coursePeriod/findByCoursePeriod",
    { params },
    { isNeedEncrypt: true }
  );
};
// // 领队讲师/角色查询
// export const roleList = params => {
//   return http.request(
//     "get",
//     "/organization/role/findAllNotPage",
//     { params },
//     { isNeedEncrypt: true },
//     { isNeedToken: true }
//   );
// };
// 领队讲师/编辑信息
export const editteaInfo = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/leaderLecturer/update",
    { data },
    {
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ACCOUNT_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
// // 账号-账号管理/列表查询
// export const accountAll = params => {
//   return http.request(
//     "get",
//     "/organization/admin/findAll",
//     { params },
//     { isNeedEncrypt: true }
//   );
// };
// 手机号验证
export const verifyPhone = data => {
  return http.request(
    "post",
    "/platform/organizationAdmin/verifyPhone",
    { data },
    { isNeedEncrypt: true }
  );
};
// 获取手机号验证码
export const getPhonecode = data => {
  return http.request(
    "post",
    "/common/verificationCode/generateCode",
    { data },
    { isNeedEncrypt: true }
  );
};

// 根据机构管理员id查询领队资质文件
export const fileLeaderList = params => {
  return http.request(
    "get",
    "/platform/leaderLecturer/findLeaderByOrganizationAdminId",
    { params },
    { isNeedEncrypt: true },
    { isNeedToken: true }
  );
};
// 根据机构管理员id查询讲师资质文件
export const fileLeatureList = params => {
  return http.request(
    "get",
    "/platform/leaderLecturer/findLecturerByOrganizationAdminId",
    { params },
    { isNeedEncrypt: true },
    { isNeedToken: true }
  );
};
