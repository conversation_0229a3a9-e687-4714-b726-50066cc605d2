<script setup>
import { ref, reactive, onMounted } from "vue";
import { Plus } from "@element-plus/icons-vue";
import { useRole } from "./utils/findHook.jsx";
import RichEditor from "@/components/Base/RichEditor.vue";

defineOptions({
  name: "FindManagename"
});

const {
  tableRef,
  defaultTime,
  dataList,
  from,
  pagination,
  columns,

  searchAdd,
  setData,
  handleSizeChange,
  handleCurrentChange,
  isDeleteApi,
  batchImport
} = useRole();
const loadingTable = ref(false);
</script>

<template>
  <div>
    <div class="common">
      <!-- <div class="con_top">
        <div class="titles">发现管理</div>
      </div> -->

      <div class="search">
        <el-form :inline="true" :model="from" class="demo-form-inline">
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="from.time"
              type="daterange"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              :default-time="defaultTime"
            />
          </el-form-item>
          <!-- <el-form-item label="标题">
            <el-input
              v-model.trim="from.name"
              placeholder="请输入标题"
              clearable
            />
          </el-form-item> -->
          <el-form-item>
            <div class="button">
              <el-button type="primary" @click="searchAdd(from)">
                搜索
              </el-button>
              <el-button @click="setData(from)">重置</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <div class="common">
      <div class="buttom">
        <el-button type="primary" @click="batchImport('newly')">新增</el-button>
      </div>
      <div class="puretable">
        <pure-table
          ref="tableRef"
          row-key="id"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="left"
          table-layout="auto"
          :loading="loadingTable"
          :data="dataList"
          :columns="columns"
          :pagination="{ ...pagination }"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #operation="{ row }">
            <div style="width: 120px">
              <el-button
                class="reset-margin"
                link
                type="primary"
                @click="batchImport('details', row)"
              >
                详情
              </el-button>
              <!-- <el-button
                class="reset-margin"
                link
                type="primary"
                @click="batchImport('edit', row)"
              >
                编辑
              </el-button> -->
              <el-button link type="danger" @click="isDeleteApi('delete', row)">
                删除
              </el-button>
            </div>
          </template>
        </pure-table>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.common {
  padding: 20px 20px 2px 20px;
  background-color: #fff;
  margin-bottom: 20px;

  .con_top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: fit-content;
    margin-bottom: 24px;

    .titles {
      font-size: 20px;
      font-weight: bold;
      color: #606266;
    }
  }

  .search {
    display: flex;

    // justify-content: space-between;
    .button {
      display: flex;
      justify-content: right;
    }
  }
}

.buttom {
  display: flex;
  justify-content: end;
  margin-bottom: 20px;
}

.upload-demo {
  margin-bottom: 10px;
}
</style>
