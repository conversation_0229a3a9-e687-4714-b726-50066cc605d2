<script setup>
import { onMounted, ref } from "vue";
import { formatTime } from "@/utils/index";
import { courseReportFindAll } from "@/api/course.js";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import { to } from "@iceywu/utils";
const route = useRoute();
const router = useRouter();
const evaluateValue = ref("");
const resultValue = ref("");
onMounted(() => {
  getTableList();
});
// 表格数据
const tableData = ref([]);
const params = ref({
  page: 1,
  size: 10,
  sort: "createdAt,desc",
  totalElements: 0
});
// 获取列表信息
const getListLoading = ref(false);
const getTableList = async data => {
  if (getListLoading.value) {
    return;
  }
  getListLoading.value = true;
  let paramsData = {
    coursePeriodId: route.query.periodId
  };
  console.log("🍧-----paramsData-----", paramsData);
  const [err, result] = await to(courseReportFindAll(paramsData));
  console.log("🎁-----result-----", result);
  if (result) {
    resultValue.value = result?.data?.achievementSummary || "";
    evaluateValue.value = result?.data?.performanceEvaluation || "";
    // tableData.value = result?.content;

    // params.value.totalElements = result.totalElements;
  } else {
    // ElMessage.error(err);
  }
  getListLoading.value = false;
};
</script>

<template>
  <div class="containers">
    <div class="content">
      <div class="free">
        <div class="title">学生整体表现评价</div>
        <!-- <div class="text">
          <el-input
            v-model="evaluateValue"
            :rows="6"
            type="textarea"
            resize="none"
            disabled
            :placeholder="evaluateValue ? evaluateValue : '暂无数据'"
          />
        </div> -->
        <div class="text-box">
          {{ evaluateValue || "暂无数据" }}
        </div>
      </div>
      <div class="refund">
        <div class="title">教学成果总结</div>
        <!-- <div class="text">
          <el-input
            v-model="resultValue"
            :rows="6"
            type="textarea"
            resize="none"
            disabled
            :placeholder="resultValue ? resultValue : '暂无数据'"
          />
        </div> -->
        <div class="text-box">
          {{ resultValue || "暂无数据" }}
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.containers {
  //   box-sizing: border-box;
  //   width: calc(100% - 48px);
  //   height: 100%;
  //   padding: 24px;
  //   background: #fff;
  .text-box {
    border: 1px solid #e5e5e5;
    border-radius: 3px;
    width: 100%;
    height: 200px;
    padding: 5px 10px;
    font-size: 14px;
    overflow-y: auto;
  }
  .content {
    // display: flex;
    box-sizing: border-box;
    // padding: 20px 90px 30px 25px;

    .free {
      width: 100%;
      margin-bottom: 25px;
    }

    .title {
      margin-bottom: 16px;
      font-size: 14px;
      color: rgb(16 16 16 / 100%);
    }

    .refund {
      width: 100%;
    }

    .text {
      width: 100%;
    }
  }
}
</style>
