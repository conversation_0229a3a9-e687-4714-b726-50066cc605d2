<script setup>
import { ref } from "vue";
const props = defineProps({
  baseInfo: {
    type: Array,
    default: () => []
  },
  baseInfoNo: {
    type: Array,
    default: () => []
  },
  type: {
    type: String,
    default: "approve"
  }
});
</script>

<template>
  <div>
    <el-descriptions class="margin-top" title="" :column="3" border>
      <template v-for="(item, index) in baseInfo" :key="index">
        <el-descriptions-item width="120px" label-align="center">
          <template #label>
            <div class="cell-item">{{ item.label }}</div>
          </template>
          {{ item.value }}
        </el-descriptions-item>
      </template>
    </el-descriptions>
  </div>
</template>
