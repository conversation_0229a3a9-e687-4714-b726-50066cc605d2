<script setup>
import { onMounted, ref, toRaw } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  getAuthorityInterface,
  addAuthorityInterface,
  editAuthorityInterface,
  deleteAuthorityInterface,
  detailsAuthorityInterface
} from "@/api/rightsManagement";
import { formatTime } from "@/utils/index";
// const useDateFormat = data => {
//   return data;
// };
const props1 = {
  value: "id",
  label: "name",
  checkStrictly: true,
  expandTrigger: "hover",
  emitPath: true
};

//权限列表
const ruleFormRef = ref();
const rules = ref({
  parentId: [{ required: true, message: "请选择父级名称", trigger: "blur" }],
  name: [{ required: true, message: "请输入权限名称", trigger: "blur" }],
  idCode: [{ required: true, message: "请输入权限码", trigger: "blur" }],
  serialNumber: [{ required: true, message: "请输入排序", trigger: "blur" }]
});
const dialogVisible = ref(false);
const title = ref("新增");

//弹窗表单
const formLabelAlign = ref({
  name: "", //权限
  idCode: "", //权限码
  parentId: "", //父id
  serialNumber: "" //排序
});

//表格数据
const tableData = ref([]);
const newtableData = ref([]);
const params = ref({
  page: 1,
  size: 15,
  sort: "serialNumber,asc",
  totalElements: 0
});

//获取表格列表
const pageCom = ref();
const getTableList = async () => {
  let paramsData = {
    page: params.value.page - 1,
    size: params.value.size,
    sort: params.value.sort
  };
  let res = await getAuthorityInterface();
  if (res.code === 200) {
    // res.data[0].children= [
    //   {
    //     id: 31,
    //     date: '2016-05-01',
    //     name: '测试2级',
    //     address: 'No. 189, Grove St, Los Angeles',
    //   },
    //   {
    //     id: 32,
    //     date: '2016-05-01',
    //     name: '测试2级（2）',
    //     address: 'No. 189, Grove St, Los Angeles',
    //   },
    // ],
    tableData.value = res.data;
    newtableData.value = toRaw(tableData.value);
    tableData.value.forEach((item, index) => {
      item.index = index + 1;
    });
    // valKey(newtableData.value)
    newtableData.value = filterData(newtableData.value);
    // newtableData.value=newtableData.value.map((item,index)=>{
    //   return {
    //     name:item.name,
    //     id:item.id,
    //     children:item.children
    //   }
    // })
    params.value.totalElements = res.data.totalElements;
    // 翻页置顶事件
    // pageCom.value.resetScrollTop();
  }

  console.log("params", params);

  // console.log("paramsData", paramsData)
};

function filterData(data) {
  return data
    .map(obj => {
      // 仅保留 id、name 和 children 属性
      const filteredObj = {
        id: obj.id,
        name: obj.name
      };
      // 递归处理 children 数组
      if (obj.children && obj.children.length > 0) {
        filteredObj.children = filterData(obj.children);
      }
      return filteredObj;
    })
    .filter(obj => obj.children || obj.children != []); // 删掉不含 children 属性和 children 属性为空数组的对象
}

// const obj_data = {};
// const valKey = (val) => {
//     for (let i = 0; i < val.length; i++) {
//       obj_data[val[i].id] = val[i].idCode;
//         if (val[i].children) {
//             valKey(val[i].children);
//         }
//     }
// };

// 删除
const deleteData = obj => {
  ElMessageBox.confirm("你确定删除这一条数据吗?", "删除提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: ""
  })
    .then(async () => {
      if (obj.subset) {
        ElMessage.error("该权限下还有子权限，请先删除子权限");
        return;
      }
      let { code, msg } = await deleteAuthorityInterface({ id: obj.id });
      if (code === 200) {
        ElMessage.success("删除成功");
        getTableList();
      } else {
        ElMessage.error(msg);
      }
    })
    .catch(() => {
      // ElMessage({
      //   type: "info",
      //   message: "取消删除"
      // });
    });
};

//分页
const handleCurrentChange = e => {
  params.value.page = e;
  getTableList();
};
const handleSizeChange = val => {
  params.value.page = 1;
  params.value.size = val;
  getTableList();
};

const obj_level = ref(true); //当前权限是否第一级
const parentIdcode = ref(""); //父idcode
const is_parentId = ref(""); //父id
const parent_name = ref(false); //编辑子级显示父级名称
//打开弹窗
const addDialog = obj => {
  title.value = "新增";

  if (title.value != "编辑") {
    parent_name.value = false;
  } else {
    parent_name.value = true;
  }
  if (obj.id) {
    obj_level.value = false;
    parentIdcode.value = obj.parentId;
    is_parentId.value = obj.id;
  } else {
    obj_level.value = true;
    parentIdcode.value = "";
    is_parentId.value = "";
  }
  formLabelAlign.value = {
    name: "", //权限
    idCode: "", //权限码
    parentId: "", //父id
    serialNumber: "" //排序
  };
  if (ruleFormRef.value) ruleFormRef.value.resetFields();
  dialogVisible.value = true;
};
const oldobj = ref({});

//编辑
const operate = async data => {
  getTableList();
  console.log("newtableData", newtableData.value);
  if (data.id == data.rootId) {
    parent_name.value = false;
  } else {
    parent_name.value = true;
  }
  if (data.id) {
    obj_level.value = false;
    parentIdcode.value = data.idCode;
    is_parentId.value = data.id;
  } else {
    obj_level.value = true;
    parentIdcode.value = "";
    is_parentId.value = "";
  }
  let res = await detailsAuthorityInterface({ id: data.id });
  console.log("🍭-----res-----", res.data);
  // if(res.data.id!=res.data.rootId){
  //  let id= res.data.idCode.split("_")[0]
  //  res.data.idCode=id
  //  console.log('🌈-----id-----', id);
  // }
  if (res.code === 200) {
    let ids = [];
    res.data.parent.reverse().map(s => {
      ids.push(s.id);
    });
    res.data.parentId = ids;
    // formLabelAlign.value = {
    //   ...res.data
    // }
    formLabelAlign.value.id = res.data.id;
    formLabelAlign.value.idCode = res.data.idCode;
    formLabelAlign.value.name = res.data.name;
    formLabelAlign.value.parentId = res.data.parentId;
    formLabelAlign.value.serialNumber = res.data.serialNumber;

    oldobj.value = JSON.parse(JSON.stringify(formLabelAlign.value));
    title.value = "编辑";
    dialogVisible.value = true;
  }
};
//确定事件-新增
const confirm = () => {
  if (!ruleFormRef.value) return;
  ruleFormRef.value.validate((valid, fields) => {
    if (valid) {
      interfaceOperate();
    }
  });

  async function interfaceOperate() {
    let axios, params, msg;
    if (formLabelAlign.value.id) {
      axios = editAuthorityInterface;
      msg = "编辑";
      params = {
        parentId:
          formLabelAlign.value.parentId[
            formLabelAlign.value.parentId.length - 1
          ],
        idCode: formLabelAlign.value.idCode,
        name: formLabelAlign.value.name,
        id: formLabelAlign.value.id
      };
      let newObj = {};
      console.log(oldobj.value, formLabelAlign.value, "===");
      for (const key in oldobj.value) {
        if (
          JSON.stringify(oldobj.value[key]) !=
          JSON.stringify(formLabelAlign.value[key]) ||
          key == "id"
        ) {
          newObj[key] = formLabelAlign.value[key];
        }
      }
      console.log(newObj, "------");
      params = newObj;
      // return;
    } else {
      axios = addAuthorityInterface;
      msg = "新增";

      //新增子级时
      if (!obj_level.value) {
        params = toRaw(formLabelAlign.value);
        params.idCode = formLabelAlign.value.idCode;
        params.parentId = is_parentId.value;
      } else {
        params = formLabelAlign.value;
      }
    }

    if (!params.parentId) {
      delete params.parentId;
    }
    if (!params.serialNumber) {
      delete params.serialNumber;
    }
    console.log("🎁-----params-----", params);

    // return
    let res = await axios(params);
    console.log("🐬-----res-----", res);
    if (res.code === 200) {
      dialogVisible.value = false;
      ElMessage.success(`${msg}成功`);
      getTableList();
    } else {
      ElMessage.error(res.msg);
    }
  }
};

onMounted(() => {
  //获取表格数据
  getTableList();
});
const open = () => {
  if (title.value == "编辑") {
    for (const key in formLabelAlign.value) {
      ruleFormRef.value.clearValidate(key, () => null);
    }
  }
};

let cascader = ref(null);
const handleChange = (name, val) => {
  console.log("🌈-----val-----", val);
  cascader.value.togglePopperVisible(false);
  if (formLabelAlign.value.parentId?.[0]) {
    formLabelAlign.value.parentId =
      formLabelAlign.value.parentId[formLabelAlign.value.parentId.length - 1];
  }
};
</script>

<template>
  <div class="containers">
    <div class="con_top">
      <!-- <div class="titles">权限管理</div> -->
      <el-button type="primary" class="addDialog" @click="addDialog">
        新增
      </el-button>
    </div>
    <el-scrollbar class="scrollbar">
      <div class="con_table">
        <el-table
          :data="tableData"
          row-key="id"
          :header-cell-style="{ backgroundColor: '#fafafa', color: '#565353' }"
          max-height="calc(100vh - 260px)"
        >
          >
          <el-table-column prop="name" label="名称" width="230" />
          <el-table-column prop="idCode" label="权限码" width="150" />
          <el-table-column prop="serialNumber" label="排序" width="80" />
          <!-- <el-table-column prop="id" label="id" align="center" /> -->
          <!-- <el-table-column prop="routeUrl" label="路由路径" align="center" />  -->
          <el-table-column prop="createdAt" label="创建时间">
            <template #default="scope">
              <!-- <sapn> -->
              {{ formatTime(scope.row.createdAt, "YYYY-MM-DD hh:mm:ss") }}
              <!-- </sapn> -->
            </template>
          </el-table-column>
          <el-table-column prop="updatedAt" label="修改时间">
            <template #default="scope">
              <!-- <sapn> -->
              {{ formatTime(scope.row.updatedAt, "YYYY-MM-DD hh:mm:ss") }}
              <!-- </sapn> -->
            </template>
          </el-table-column>
          <el-table-column align="left" label="操作" fixed="right" width="220">
            <template #default="scope">
              <el-link
                :underline="false"
                type="primary"
                size="small"
                style="margin-right: 20px"
                @click="addDialog(scope.row)"
              >
                新增子项
              </el-link>
              <el-link
                :underline="false"
                type="primary"
                size="small"
                style="margin-right: 20px"
                @click="operate(scope.row)"
              >
                编辑
              </el-link>
              <el-link
                :underline="false"
                type="danger"
                size="small"
                @click="deleteData(scope.row)"
              >
                删除
              </el-link>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-scrollbar>
    <div class="con_pagination">
      <!-- 分页 -->
      <el-pagination
        v-model:current-page="params.page"
        v-model:page-size="params.size"
        class="pagination"
        background
        :page-sizes="[15, 30, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="params.totalElements"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <div class="dialog">
      <!--    新增 -->
      <el-dialog
        v-model="dialogVisible"
        :title="`${title}权限`"
        width="30%"
        :close-on-click-modal="false"
        @open="open"
      >
        <el-form
          ref="ruleFormRef"
          label-width="110px"
          :model="formLabelAlign"
          :rules="rules"
        >
          <el-form-item v-if="parent_name" label="父级名称" prop="parentId">
            <el-cascader
              ref="cascader"
              v-model="formLabelAlign.parentId"
              :options="newtableData"
              :props="props1"
              clearable
              :show-all-levels="false"
              @change="handleChange($event, item)"
            />
            <!-- <el-tree-select v-model="formLabelAlign.parentId" :data="data" check-strictly:render-after-expand="false" style="width: 240px"/> -->
          </el-form-item>
          <el-form-item label="权限名称" prop="name">
            <el-input
              v-model="formLabelAlign.name"
              placeholder="请输入"
              maxlength="20"
              show-word-limit
            />
          </el-form-item>
          <el-form-item label="权限码" prop="idCode">
            <el-input
              v-model="formLabelAlign.idCode"
              :disabled="title === '编辑'"
              placeholder="请输入"
              maxlength="20"
              show-word-limit
            />
          </el-form-item>
          <el-form-item
            v-if="title === '编辑'"
            label="排序"
            prop="serialNumber"
          >
            <el-input-number
              v-model="formLabelAlign.serialNumber"
              :min="1"
              :precision="0"
            />
          </el-form-item>
          <!-- <el-form-item  prop="idCode" label="权限码">
          <el-input v-model="formLabelAlign.idCode"  placeholder="请输入" maxlength="20" show-word-limit>
            <template #prepend>{{ parentIdcode }}</template>
          </el-input>
        </el-form-item> -->
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" @click="confirm"> 确定 </el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.scrollbar {
  height: calc(100vh - 206px);
  background-color: #fff;
}

.containers {
  box-sizing: border-box;
  width: calc(100% - 48px);
  // height: 100%;
  padding: 20px;
  background: #fff;
  .addDialog {
    margin-bottom: 20px;
  }
  .con_top {
    display: flex;
    align-items: center;
    justify-content: right;
    width: 100%;
    height: fit-content;
    .titles {
      font-size: 20px;
      font-weight: bold;
      color: #606266;
    }
  }

  .con_search {
    display: flex;
    align-items: center;
    width: 100%;
    height: fit-content;
    margin-bottom: 24px;
  }

  .con_table {
    // width: calc(100% - 25px);
    // margin-bottom: 24px;
    // margin-left: 25px;
    .btnse {
      color: #409eff;
    }
  }

  .con_pagination {
    display: flex;
    justify-content: flex-end;
    width: 100%;
  }
  .eye_style {
    display: flex;
    // align-items: center;
    // justify-content: center;
    .eye {
      margin-left: 20px;
      cursor: pointer;
      :hover {
        color: #409eff;
      }
    }
  }
  // ˇ
  // :deep(.el-table__expand-icon--expanded) {
  //   transform: rotate(180deg) !important;
  // }
  // :deep(.el-table__expand-icon) {
  //   transition: "none" !important;

  //   .el-icon {
  //     display: none !important;
  //   }
  //   content: url("../../../assets/index/add.png") !important;
  // }
  // :deep(.el-table__expand-icon--expanded) {
  //   content: url("../../../assets/index/jian.png") !important;
  // }
  // :deep(.el-table__row--level-1 .el-table__indent) {
  //   padding-left: 25px !important;
  // }
  // :deep(.el-table__row--level-2 .el-table__indent) {
  //   padding-left: 50px !important;
  // }
  // :deep(.el-table__row--level-3 .el-table__indent) {
  //   padding-left: 75px !important;
  // }
  // :deep(.el-table__row--level-4 .el-table__indent) {
  //   padding-left: 100px !important;
  // }
  // :deep(.is-left) {
  //   padding-left: 15px !important;
  // }
  // .addDialog {
  //   margin-right: 50px;
  // }
  // .dialog {
  //   :deep(.el-dialog__body) {
  //     padding-right: 50px;
  //   }
  //   :deep(.el-dialog__footer) {
  //     text-align: center;
  //   }
  // }
  // :deep(.cell){
  //   position: relative;
  //   .el-table__expand-icon{
  //     position: absolute;
  //     left: 20px;
  //   }
  // }

  .pagination {
    display: flex;
    justify-content: center;
    padding: 12px 0;
    box-sizing: border-box;
  }
  :deep(.el-input__wrapper) {
    width: 120px;
  }
  .picker {
    :deep(.el-input__wrapper) {
      width: 100%;
    }
  }
  .table {
    margin-left: 25px;
    :deep(.el-table .cell) {
      padding: 0;
    }
  }
}
</style>
<!-- <style>
.el-cascader-panel .el-radio {
  width: 100%;
  height: 100%;
  z-index: 10;
  position: absolute;
  top: 10px;
  right: 10px;
}

.el-cascader-panel .el-radio__input {
  visibility: hidden;
}

/* 这个样式针对IE有用，不考虑IE的可以不用管*/
.el-cascader-panel .el-cascader-node__postfix {
  top: 10px;
}

/* .el-cascader-panels{
    display: none;
    color: red;
    font-size: 700;
} */
</style> -->
