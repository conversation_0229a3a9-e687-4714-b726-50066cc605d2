import { $t } from "@/plugins/i18n";
import { account } from "@/router/enums.js";
import { acCodesList, reCodesList } from "@/router/accidCode.js";
import AccountIcon from "@/assets/home/<USER>";
import AccountIconActive from "@/assets/home/<USER>";
export default {
  path: "/account",
  redirect: "/account/leader/management",
  meta: {
    icon: "ri:information-line",
    imgIcon: AccountIcon,
    imgIconActive: AccountIconActive,
    // showLink: false,
    title: "账号",
    rank: account,
    idCode: acCodesList.baseCode
  },

  children: [
    {
      // path: "/account/leader/management",
      path: "/account/management",
      name: "management",
      redirect: "/account/leader/management",
      // component: () =>
      //   import("@/views/accountNumber/leaderManagement/index.vue"),
      meta: {
        // title: $t("menus.pureFourZeroOne")
        title: "领队管理"
        // idCode: acCodesList.leader
      },
      children: [
        {
          path: "/account/leader/management",
          name: "LeaderManagement",
          component: () =>
            import("@/views/accountNumber/leaderManagement/index.vue"),
          meta: {
            // title: $t("menus.pureFourZeroOne")
            title: "领队管理",
            keepAlive: true,
            idCode: acCodesList.leader
          }
        },
        {
          path: "/accountNumber/components/teacherDetails",
          name: "TeacherDetails",
          component: () =>
            import("@/views/accountNumber/components/teacherDetails.vue"),
          meta: {
            title: "详情",
            keepAlive: true,
            idCode: reCodesList.teacherDetails,
            showLink: false
          },
          children: [
            {
              path: "/accountNumber/components/qualificationsFile",
              name: "qualificationsFile",
              component: () =>
                import(
                  "@/views/accountNumber/components/qualificationsFile.vue"
                ),
              meta: {
                title: "资质文件",
                idCode: reCodesList.qualificationsFile,
                showLink: false
              }
            },
            {
              path: "/accountNumber/components/editInformation",
              name: "editInformation",
              component: () =>
                import("@/views/accountNumber/components/editInformation.vue"),
              meta: {
                title: "编辑信息",
                idCode: reCodesList.editInformation,
                showLink: false
              }
            }
          ]
        }
      ]
    },
    {
      path: "account/management",
      name: "lecturer",
      redirect: "/account/lecturer/management",
      // component: () =>
      //   import("@/views/accountNumber/lecturerManagement/index.vue"),
      meta: {
        title: "讲师管理"
        // keepAlive: true,
        // idCode: acCodesList.lecturer
      },
      children: [
        {
          path: "/account/lecturer/management",
          name: "LecturerManagement",
          component: () =>
            import("@/views/accountNumber/lecturerManagement/index.vue"),
          meta: {
            title: "讲师管理",
            keepAlive: true,
            idCode: acCodesList.lecturer
          }
        },
        {
          path: "/lecturerNumber/components/teacherDetails",
          name: "lecturerDetails",
          component: () =>
            import("@/views/accountNumber/components/teacherDetails.vue"),
          meta: {
            title: "详情",
            idCode: reCodesList.teacherDetails,
            showLink: false
          },
          children: [
            {
              path: "/lecturerNumber/components/qualificationsFile",
              name: "lecturerQualificationsFile",
              component: () =>
                import(
                  "@/views/accountNumber/components/qualificationsFile.vue"
                ),
              meta: {
                title: "资质文件",
                idCode: reCodesList.qualificationsFile,
                showLink: false
              }
            },
            {
              path: "/lecturerNumber/components/editInformation",
              name: "lecturerEditInformation",
              component: () =>
                import("@/views/accountNumber/components/editInformation.vue"),
              meta: {
                title: "编辑信息",
                idCode: reCodesList.editInformation,
                showLink: false
              }
            }
          ]
        }
      ]
    },
    {
      path: "/account/parent",
      name: "accountparent",
      redirect: "/account/parent/management",
      meta: {
        title: "家长管理"
        // showLink: true
      },
      children: [
        {
          path: "/account/parent/management",
          name: "ParentManagement",
          component: () =>
            import("@/views/accountNumber/parentManagement/index.vue"),
          meta: {
            title: "家长管理",
            keepAlive: true,
            idCode: acCodesList.parent
            // showLink: false
          }
        },
        {
          path: "/parentManagement/componts/parentOrderManage",
          name: "ParentManagementCompontsParentOrderManage",
          component: () =>
            import(
              "@/views/accountNumber/parentManagement/componts/parentOrderManage.vue"
            ),
          meta: {
            // title: $t("menus.pureFourZeroOne")
            title: "订单管理",
            keepAlive: true,
            idCode: reCodesList.parentManagementCompontsParentOrderManage,
            showLink: false
            // keepAlive: true
          },
          children: [
            {
              path: "/parentManagement/orderManagement/orderDetails",
              name: "parentManagementOrderDetails",
              component: () =>
                import("@/views/course/orderManagement/orderDetails.vue"),
              meta: {
                title: "订单管理-订单详情",
                idCode: reCodesList.courseOrderDetails,
                showLink: false
              },
              children: [
                {
                  path: "/parentManagement/management/current",
                  name: "parentManagementManagementCurrent",
                  component: () =>
                    import(
                      "@/views/course/courseManagement/currentDetails.vue"
                    ),
                  meta: {
                    // title: $t("menus.pureFourZeroOne")
                    title: "当期课程详情",
                    idCode: reCodesList.courseManagementCurrentDetails,
                    showLink: false
                  }
                }
              ]
            }
          ]
        },
        {
          path: "/parentManagement/componts/parentDetails",
          name: "ParentManagementCompontsParentDetails",
          component: () =>
            import(
              "@/views/accountNumber/parentManagement/componts/parentDetails.vue"
            ),
          meta: {
            title: "详情",
            keepAlive: true,
            idCode: reCodesList.parentManagementCompontsParentDetails,
            showLink: false
            // keepAlive: true
          },
          children: [
            {
              path: "/parentManagement/componts/childrenDetails",
              name: "ParentManagementCompontsChildrenDetails",
              component: () =>
                import(
                  "@/views/accountNumber/parentManagement/componts/childrenDetails.vue"
                ),
              meta: {
                // title: $t("menus.pureFourZeroOne")
                title: "子女详情",
                keepAlive: true,
                showLink: false,
                idCode: reCodesList.parentManagementCompontsChildrenDetails
              },
              children: [
                {
                  path: "/parentManagement/management/current/details",
                  name: "parentManagementManagementCurrentDetails",
                  component: () =>
                    import(
                      "@/views/course/courseManagement/currentDetails.vue"
                    ),
                  meta: {
                    // title: $t("menus.pureFourZeroOne")
                    title: "当期详情",
                    idCode: reCodesList.courseManagementCurrentDetails,
                    showLink: false
                  }
                },
                {
                  path: "/parentManagement/orderManagement/orderDetail",
                  name: "parentManagementCourseOrderDetail",
                  component: () =>
                    import("@/views/course/orderManagement/orderDetails.vue"),
                  meta: {
                    title: "订单详情",
                    idCode: reCodesList.courseOrderDetails,
                    showLink: false
                  }
                }
              ]
            }
          ]
        }
      ]
    },
    {
      path: "/account/student",
      name: "accountStudent",
      redirect: "/account/student/management",
      meta: {
        title: "学生管理"
      },
      children: [
        {
          path: "/account/student/management",
          name: "StudentManagement",
          component: () =>
            import("@/views/accountNumber/studentManagement/index.vue"),
          meta: {
            title: "学生管理",
            keepAlive: true,
            idCode: acCodesList.student
          }
        },
        {
          path: "/studentManagement/componts/studentDetails",
          name: "StudentManagementCompontsStudentDetails",
          component: () =>
            import(
              "@/views/accountNumber/studentManagement/componts/studentDetails.vue"
            ),
          meta: {
            // title: $t("menus.pureFourZeroOne")
            title: "学生详情",
            keepAlive: true,
            idCode: reCodesList.studentManagementCompontsStudentDetails,
            showLink: false
          },
          children: [
            {
              path: "/studentManagement/management/current/details",
              name: "studentManagementManagementCurrentDetails",
              component: () =>
                import("@/views/course/courseManagement/currentDetails.vue"),
              meta: {
                // title: $t("menus.pureFourZeroOne")
                title: "当期详情",
                idCode: reCodesList.courseManagementCurrentDetails,
                showLink: false
              }
            },
            {
              path: "/studentManagement/orderManagement/orderDetail",
              name: "studentManagementCourseOrderDetail",
              component: () =>
                import("@/views/course/orderManagement/orderDetails.vue"),
              meta: {
                title: "订单详情",
                idCode: reCodesList.courseOrderDetails,
                showLink: false
              }
            }
          ]
        }
      ]
    },
    {
      path: "/account/localend",
      name: "localend",
      redirect: "/account/localend/account",
      // component: () =>
      //   import("@/views/accountNumber/localEndAccount/index.vue"),
      meta: {
        title: "局端账号",
        idCode: acCodesList.Localend
      },
      children: [
        {
          path: "/account/localend/account",
          name: "LocalendAccount",
          component: () =>
            import("@/views/accountNumber/localEndAccount/index.vue"),
          meta: {
            title: "局端账号",
            idCode: acCodesList.Localend,
            keepAlive: true
          }
        },
        {
          path: "/account/localend/account/add",
          name: "localendAccountAdd",
          component: () =>
            import("@/views/accountNumber/localEndAccount/add.vue"),
          meta: {
            // title: $t("menus.pureFourZeroOne")
            title: "账号创建编辑",
            idCode: reCodesList.localendAccountAdd,
            showLink: false
          }
        }
      ]
    },
    {
      path: "/account/platform",
      name: "platform",
      redirect: "/account/platform/account",
      // component: () =>
      //   import("@/views/accountNumber/platformAccount/index.vue"),
      meta: {
        title: "平台账号",
        idCode: acCodesList.platform
      },
      children: [
        {
          path: "/account/platform/account",
          name: "PlatformAccount",
          component: () =>
            import("@/views/accountNumber/platformAccount/index.vue"),
          meta: {
            title: "平台账号",
            idCode: acCodesList.platform,
            keepAlive: true
          }
        },
        {
          path: "/account/platform/account/detail",
          name: "platformAccountDetail",
          component: () =>
            import("@/views/accountNumber/platformAccount/detail.vue"),
          meta: {
            // title: $t("menus.pureFourZeroOne")
            title: "平台账号详情",
            idCode: reCodesList.platformAccountDetail,
            showLink: false
          },
          children: [
            {
              path: "/account/platform/account/add",
              name: "platformAccountAdd",
              component: () =>
                import("@/views/accountNumber/platformAccount/add.vue"),
              meta: {
                // title: $t("menus.pureFourZeroOne")
                title: "账号编辑",
                idCode: reCodesList.platformAccountAdd,
                showLink: false
              }
            }
          ]
        },
        {
          path: "/account/platform/account/addAdd",
          name: "platformAccountAddAdd",
          component: () =>
            import("@/views/accountNumber/platformAccount/add.vue"),
          meta: {
            // title: $t("menus.pureFourZeroOne")
            title: "账号创建",
            idCode: reCodesList.platformAccountAdd,
            showLink: false
          }
        }
      ]
    },
    {
      path: "/account/role/management",
      name: "roleManagement",
      component: () => import("@/views/accountNumber/roleManagement/index.vue"),
      meta: {
        title: "角色管理",
        idCode: acCodesList.roles
      }
    },
    {
      path: "/account/rights/management",
      name: "rightsManagement",
      component: () =>
        import("@/views/accountNumber/rightsManagement/index.vue"),
      meta: {
        title: "权限管理",
        idCode: acCodesList.permission
      }
    }
  ]
};
