<script setup>
import { ref, onMounted, reactive, onBeforeMount, onBeforeUnmount } from "vue";
import { useRouter, useRoute } from "vue-router";
import RichEditor from "@/components/Base/RichEditor.vue";
import { complexFindById, findArea } from "@/api/institution";
import { ElMessage } from "element-plus";
import { Edit, Hide, View } from "@element-plus/icons-vue";
import { formatTime } from "@/utils/index";
import { decrypt, encryption } from "@/utils/SM4.js";
import { template } from "lodash";
import FileItem from "@/components/PreviewV2/FileItem.vue";
import { ImageThumbnail } from "@/utils/imageProxy";
import areaList from "@/utils/areaList.js";

const router = useRouter();
const route = useRoute();
const form = ref({});
const newData = ref({});

const formRef = ref(null);
const richFlag = ref(false);
const adminId = ref(null);
const jsonData = ref([]);
onMounted(async () => {
  adminId.value = route.query.id;
  jsonData.value = await areaList();
  await getData();
  richFlag.value = true;
});

const formType = {
  MEETING_ROOM: "会议室",
  CLASSROOM: "教室",
  LOBBY: "大厅",
  SQUARE: "广场",
  PARK: "公园",
  ATTRACTION: "景点",
  OUTDOORS: "户外",
  OTHER: "其他"
};

const formFile = ref({
  organizationName: [],
  video: []
});

const formData = ref([
  {
    label: "实践点名称",
    type: "text",
    prop: "name",
    check: true,
    placeholder: "请输入机构名称",
    width: "200px"
  },
  {
    label: "机构",
    type: "text",
    // check: true,
    prop: "organizationName",
    placeholder: "请输入机构别名",
    width: "200px"
  },
  {
    label: "创建时间",
    type: "text",
    // check: true,
    prop: "createdAt",
    placeholder: "请输入机构别名",
    width: "200px",
    rowspan: 2
  },
  {
    label: "归属地信息",
    type: "text",
    // check: true,
    prop: "address",
    placeholder: "请输入机构别名",
    width: "200px",
    rowspan: 2
  },
  {
    label: "详细地址",
    type: "text",
    // check: true,
    prop: "detailedAddress",
    placeholder: "请输入机构别名",
    width: "200px",
    rowspan: 2
  },
  {
    label: "实践点联系人",
    type: "text",
    // check: true,
    prop: "emergencyPeople",
    placeholder: "请输入客服热线",
    width: "200px"
  },
  {
    label: "实践点联系电话",
    type: "text",
    // check: true,
    prop: "emergencyPhone",
    placeholder: "请输入客服热线",
    width: "200px"
  },
  {
    label: "类型",
    type: "textString",
    prop: "complexType",
    placeholder: "请输入机构别名",
    width: "200px",
    rowspan: 2
  },
  {
    label: "标签",
    type: "textString",
    prop: "tags",
    placeholder: "请输入机构别名",
    width: "200px",
    rowspan: 2
  },
  {
    label: "一句话简介",
    type: "text",
    prop: "oneSentenceIntroduction",
    placeholder: "请输入机构别名",
    width: "200px",
    rowspan: 2
  },
  {
    label: "实践点介绍",
    type: "editor",
    // check: true,
    prop: "introduction",
    placeholder: "",
    width: "200px",
    rowspan: 2
  },
  {
    label: "照片",
    type: "images",
    type2: "images",
    prop: "organizationName",
    placeholder: "请输入机构别名",
    width: "200px",
    rowspan: 2
  },
  {
    label: "视频",
    type: "video",
    type2: "video",
    prop: "video",
    placeholder: "请输入机构别名",
    width: "200px",
    rowspan: 2
  }
]);

// 根据id查询
const getData = async () => {
  let params = { id: route.query.id };
  try {
    const { code, data, msg } = await complexFindById(params);
    console.log("🎁-----data-----", code, data, msg);
    if (code == 200) {
      form.value = data;
      newData.value = JSON.parse(JSON.stringify(data));
      // 解密手机号并直接显示
      if (newData.value.emergencyPhoneCt) {
        form.value.emergencyPhone = decrypt(newData.value.emergencyPhoneCt);
      }
      // 文件集合回显
      if (data?.files) {
        data.files.forEach(item => {
          console.log("🐳-----item-----", item);
          if (item.fileType === "PHOTO") {
            formFile.value.organizationName.push(item.uploadFile);
          } else if (item.fileType === "PROMOTIONAL_VIDEO") {
            formFile.value.video.push(item.uploadFile);
          }
        });
      }
      // 查询归属地
      await initAddressEcho(data);
    }
  } catch (error) {
    console.log("catch-----error-----", error);
  }
};

//返回上一页
const cancel = () => {
  router.go(-1);
};

// const isView = ref(true);
// const isViewFn = () => {
//   isView.value = !isView.value;
//   if (isView.value) {
//     form.value.emergencyPhone = newData.value.emergencyPhone;
//   } else {
//     form.value.emergencyPhone = decrypt(newData.value.emergencyPhoneCt);
//   }
// };

//  数组字符串处理
const findTypeItem = obj => {
  for (const i in formType) {
    if (obj === i) {
      return formType[i];
    }
  }
};
// 获取图片URL列表用于预览
const getImageUrlList = prop => {
  if (!formFile.value[prop] || formFile.value[prop].length === 0) {
    return [];
  }
  return formFile.value[prop].map(item => item.url);
};

const initAddressEcho = async data => {
  console.log("🎉-----data-----", data);
  if (data?.district && data.city && data.province) {
    const addressArray = [];

    try {
      // 设置地址数组
      if (data.city !== "直辖市") {
        form.value.address = [
          data.province,
          data.city,
          data.district,
          data.street
        ];
      } else {
        form.value.address = [data.province, data.district, data.street];
      }
      const list = await collectLabelsFromTree(
        jsonData.value,
        form.value.address
      );
      // console.log('🌈-----list-----', list);
      form.value.address = list.join("/");
    } catch (error) {
      console.error("获取地址ID失败:", error);
    }
  }
};

// 通用的函数，从具有id的树中收集标签
function collectLabelsFromTree(treeData, targetIds, options = {}) {
  // 默认配置，可通过options覆盖
  const {
    idKey = "value",
    labelKey = "label",
    childrenKey = "children"
  } = options;

  // 存储结果的数组
  const labels = [];

  // 递归遍历函数
  function traverse(node) {
    // 如果是数组，遍历每个元素
    if (Array.isArray(node)) {
      node.forEach(item => traverse(item));
      return;
    }

    // 如果不是对象，直接返回
    if (typeof node !== "object" || node === null) {
      return;
    }

    // 检查当前节点ID是否在目标ID数组中（考虑类型转换）
    const nodeId = node[idKey];
    const isMatch = targetIds.some(
      targetId => String(nodeId) === String(targetId)
    );

    // 如果匹配，添加标签到结果数组
    if (isMatch) {
      labels.push(node[labelKey]);
    }

    // 递归处理子节点
    const children = node[childrenKey];
    if (children && Array.isArray(children) && children.length > 0) {
      traverse(children);
    }
  }

  // 开始遍历树形数据
  traverse(treeData);

  return labels;
}
</script>

<template>
  <div class="main">
    <!-- <div class="header">
      <div class="title_left">
        <p>创建时间</p>
        <p>{{ form.time }}</p>
      </div>
      <div class="title_rigth">
        <p>机构ID</p>
        <p>{{ form.institutionID }}</p>
      </div>
    </div> -->
    <div class="containers">
      <el-scrollbar>
        <!-- <el-form ref="formRef" :model="form" :rules="rules"> -->
        <el-descriptions title="" :column="2" border :label-width="'200px'">
          <el-descriptions-item
            v-for="(item, index) in formData"
            :key="index"
            label-align="center"
            label-class-name="my-label"
            :span="item.rowspan || 1"
          >
            <template #label>
              {{ item.label }}
            </template>
            <el-form-item
              :prop="item.prop"
              :inline-message="item.check"
              style="margin-bottom: 0"
              :show-message="true"
              error-placement="right"
            >
              <!-- text -->
              <template v-if="item.type === 'text'">
                <div class="cell_item">
                  <div>
                    {{
                      item.prop === "createdAt"
                        ? formatTime(form[item.prop], "YYYY-MM-DD HH:mm:ss")
                        : form[item.prop] || "--"
                    }}
                  </div>
                  <!-- <span v-if="item.prop === 'emergencyPhone'" class="icon">
                  <el-icon
                    v-if="isView"
                    style="cursor: pointer"
                    @click="isViewFn"
                  >
                    <Hide />
                  </el-icon>
                  <el-icon v-else style="cursor: pointer" @click="isViewFn">
                    <View />
                  </el-icon>
                </span> -->
                </div>
              </template>
              <!-- 富文本 -->
              <template v-else-if="item.type === 'editor'">
                <div style="width: 100%">
                  <RichEditor
                    v-model="form[item.prop]"
                    height="200px"
                    :isOpen="false"
                    :readOnly="true"
                  />
                </div>
              </template>
              <!-- 需要处理字符串 -->
              <template v-else-if="item.type === 'textString'">
                <template v-if="form[item.prop]">
                  <div v-if="item.prop === 'tags'">
                    {{ form[item.prop].join(", ") }}
                  </div>
                  <div v-if="item.prop === 'complexType'">
                    {{ findTypeItem(form[item.prop]) }}
                  </div>
                </template>
                <span v-else>--</span>
              </template>
              <template v-else-if="item.type2 === 'video'">
                <template
                  v-for="(item2, index2) in formFile[item.prop]"
                  :key="index2"
                >
                  <div v-if="item2?.fileName" class="fileOther">
                    <FileItem
                      :data="item2"
                      :index="index2"
                      style="width: 600px; min-width: 130px"
                      @delete="getDeleted(item2, index2)"
                    />
                  </div>
                </template>
                <div v-show="formFile[item.prop]?.length === 0">{{ "--" }}</div>
              </template>
              <template v-else-if="item.type2 === 'images'">
                <text v-if="formFile[item.prop].length === 0">--</text>
                <template v-else>
                  <el-image
                    v-for="(item2, index2) in formFile[item.prop]"
                    :key="index2"
                    :src="ImageThumbnail(item2.url, '120px')"
                    fit="scale-down"
                    :preview-src-list="getImageUrlList(item.prop)"
                    :initial-index="index2"
                    class="img-pic"
                  />
                </template>
              </template>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
      </el-scrollbar>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.main {
  padding: 20px;
  background: #fff;
  .header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 20px;
    .title_left {
      display: flex;
      :nth-child(2) {
        margin-left: 40px;
      }
    }
    .title_rigth {
      display: flex;
      :nth-child(2) {
        margin-left: 40px;
      }
    }
  }
  .containers {
    box-sizing: border-box;
    height: calc(100vh - 154px);
    padding: 20px;
    background: #fff;
  }
}

.cell_item {
  min-width: 150px;
  display: flex;
}

:deep(.my-label) {
  background: #e1f5ff !important;
}

.img-pic {
  width: 120px;
  height: 120px;
  margin: 10px;
  // object-fit: cover;
}
// .fileOther{

// }

.fileOther {
  // margin-top: 26px;
  margin-left: 20px;
  display: flex;
  align-items: center;
  line-height: 22px;
  width: 420px;
  // margin-bottom: 56px;
  .title {
    color: #0e1832;
    font-family: Source Han Sans CN;
    font-size: 16px;
    font-weight: 400;
    height: 24px;
    white-space: nowrap;
  }
  .link {
    width: 20px;
    height: 20px;
    margin: 0 10px;
    cursor: pointer;
  }
  .fileName {
    color: #3876fd;
    font-family: Source Han Sans CN;
    font-size: 16px;
    font-weight: 400;
    height: 24px;
    cursor: pointer;
    // width: 350px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .linkDetele {
    width: 20px;
    height: 20px;
    margin-left: 10px;
    cursor: pointer;
  }
}
</style>
