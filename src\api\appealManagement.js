import { http } from "@/utils/http";

/** 分页查询申诉 */
export const appealHandle = params => {
  return http.request(
    "get",
    "/platform/appeal/findReview",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
/** 处理申诉 */
export const appealHandleFind = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/appeal/review",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ACCOUNT_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
/** 根据Id查询 */
export const appealHandleId = params => {
  return http.request(
    "get",
    "/platform/appeal/findById",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
