import { $t } from "@/plugins/i18n";
import { platform } from "@/router/enums.js";
import { plCodesList, reCodesList } from "@/router/accidCode.js";
import PlatformIcon from "@/assets/home/<USER>";
import PlatformIconActive from "@/assets/home/<USER>";
export default {
  path: "/platform",
  redirect: "/platform/platformSettings",
  meta: {
    icon: "ri:information-line",
    imgIcon: PlatformIcon,
    imgIconActive: PlatformIconActive,
    // showLink: false,
    title: "平台",
    rank: platform,
    idCode: plCodesList.baseCode
  },
  children: [
    {
      path: "/platform/knowledge/base",
      name: "知识库",
      component: () => import("@/views/platform/knowledgeBase.vue"),
      meta: {
        title: "知识库",
        idCode: plCodesList.knowledgeBase
      }
    },
    // {
    //   path: "/platform/teacher/resource/pool",
    //   name: "TeacherResourcePool",
    //   redirect: "/platform/teacherResourcePool",
    //   meta: {
    //     title: "师资库"
    //   },
    //   children: [
    //     {
    //       path: "/platform/teacherResourcePool",
    //       name: "InstitutionalFacultyIndex",
    //       component: () => import("@/views/platform/teacherResourcePool.vue"),
    //       meta: {
    //         title: "师资库",
    //         idCode: plCodesList.teacherResource,
    //         keepAlive: true
    //       }
    //     },
    //     {
    //       path: "/platform/teacherResourcePool/add",
    //       name: "新增师资",
    //       component: () =>
    //         import("@/views/platform/components/teacherResourcePoolEdit.vue"),
    //       meta: {
    //         title: "新增师资",
    //         // idCode: reCodesList.expertEdit,
    //         keepAlive: true
    //       }
    //     },
    //     {
    //       path: "/platform/teacherResourcePool/edit",
    //       name: "师资编辑",
    //       component: () =>
    //         import("@/views/platform/components/teacherResourcePoolEdit.vue"),
    //       meta: {
    //         title: "师资编辑",
    //         // idCode: reCodesList.expertEdit,
    //         keepAlive: true
    //       }
    //     },
    //     {
    //       path: "/platform/teacherResourcePool/detail",
    //       name: "师资详情",
    //       component: () =>
    //         import("@/views/platform/components/teacherResourcePoolDetail.vue"),
    //       meta: {
    //         title: "师资详情",
    //         // idCode: plCodesList.teacherResourceDetail,
    //         keepAlive: true
    //       }
    //     },
    //     {
    //       path: "/platform/teacherResourcePool/evaluation",
    //       name: "专家评价",
    //       component: () =>
    //         import("@/views/platform/components/expertEvaluation.vue"),
    //       meta: {
    //         title: "专家评价",
    //         // idCode: plCodesList.expertEvaluation,
    //         keepAlive: true
    //       }
    //     },
    //     {
    //       path: "/platform/teacherResourcePool/coursesParticipated",
    //       name: "参与课程",
    //       component: () =>
    //         import("@/views/platform/components/joinCourseInstitution.vue"),
    //       meta: {
    //         title: "参与课程",
    //         // idCode: plCodesList.coursesParticipated,
    //         keepAlive: true
    //       }
    //     }
    //   ]
    // },
    // {
    //   path: "/platform/faculty/review",
    //   redirect: "/platform/facultyReview",
    //   name: "teacherReview",
    //   meta: {
    //     title: "师资审核"
    //   },
    //   children: [
    //     {
    //       path: "/platform/facultyReview",
    //       name: "FacultyReview",
    //       component: () => import("@/views/platform/facultyReview.vue"),
    //       meta: {
    //         title: "师资审核",
    //         idCode: plCodesList.facultyReview,
    //         keepAlive: true
    //       }
    //     },
    //     {
    //       path: "/platform/facultyReview/addDetail",
    //       name: "FacultyReviewAddDetail",
    //       component: () =>
    //         import("@/views/platform/components/teacherResourcePoolDetail.vue"),
    //       meta: {
    //         title: "详情",
    //         // idCode: plCodesList.facultyReviewDetail,
    //         keepAlive: true
    //       }
    //     },
    //     {
    //       path: "/platform/facultyReview/editDetail",
    //       name: "FacultyReviewEditDetail",
    //       component: () =>
    //         import("@/views/platform/components/facultyReviewDetail.vue"),
    //       meta: {
    //         title: "详情",
    //         // idCode: plCodesList.facultyReviewDetail,
    //         keepAlive: true
    //       }
    //     }
    //   ]
    // },
    {
      path: "/platform/platformSettings",
      name: "平台设置",
      component: () => import("@/views/platform/platformSettings.vue"),
      meta: {
        title: "平台设置",
        idCode: plCodesList.plPlatform
      }
    },
    {
      path: "/platform/logManage",
      name: "日志管理",
      component: () => import("@/views/platform/logManage.vue"),
      meta: {
        title: "日志管理",
        idCode: plCodesList.Log
      }
    },
    {
      path: "/platform/expert",
      name: "expertDatabase",
      redirect: "/platform/expertDatabase",
      // component: () => import("@/views/platform/expertDatabase.vue"),
      meta: {
        title: "专家库"
      },
      children: [
        {
          path: "/platform/expertDatabase",
          name: "expertDatabasename",
          component: () => import("@/views/platform/expertDatabase.vue"),
          meta: {
            title: "专家库",
            idCode: plCodesList.expertDatabase,
            keepAlive: true
          }
        },
        {
          path: "/platform/components/expertCreateEdit",
          name: "编辑",
          component: () =>
            import("@/views/platform/components/expertCreateEdit.vue"),
          meta: {
            title: "编辑",
            // idCode: reCodesList.expertEdit,
            showLink: false
          }
        }
      ]
    },
    {
      path: "/platform/find",
      name: "findManage",
      redirect: "/platform/findManage",
      meta: {
        title: "发现管理"
      },
      children: [
        {
          path: "/platform/findManage",
          name: "FindManagename",
          component: () => import("@/views/platform/findManage.vue"),
          meta: {
            title: "发现管理",
            idCode: plCodesList.find,
            keepAlive: true
          }
        },

        {
          path: "/platform/components/findDetails",
          name: "findDetails",
          component: () =>
            import("@/views/platform/components/findDetails.vue"),
          meta: {
            title: "详情",
            idCode: reCodesList.findDetails,
            showLink: false
          }
        },
        {
          path: "/platform/components/findEdit",
          name: "findEdit",
          component: () => import("@/views/platform/components/findEdit.vue"),
          meta: {
            title: "编辑",
            idCode: reCodesList.findEdit,
            showLink: false
          }
        },
        {
          path: "/platform/components/findNewly",
          name: "findNewly",
          component: () => import("@/views/platform/components/findNewly.vue"),
          meta: {
            title: "新增",
            idCode: reCodesList.findNewly,
            showLink: false
          }
        }
      ]
    },
    // {
    //   path: "/platform/news",
    //   name: "newsManage",
    //   redirect: "/platform/newsManage",
    //   meta: {
    //     title: "新闻管理"
    //   },
    //   children: [
    //     {
    //       path: "/platform/newsManage",
    //       name: "NewsManagename",
    //       component: () => import("@/views/platform/newsManage.vue"),
    //       meta: {
    //         title: "新闻管理",
    //         idCode: plCodesList.news,
    //         keepAlive: true
    //       }
    //     },

    //     {
    //       path: "/platform/components/newsDetails",
    //       name: "newsDetails",
    //       component: () =>
    //         import("@/views/platform/components/newsDetails.vue"),
    //       meta: {
    //         title: "新闻详情",
    //         idCode: reCodesList.newsDetails,
    //         showLink: false
    //       }
    //     },
    //     {
    //       path: "/platform/components/newsEdit",
    //       name: "newsEdit",
    //       component: () => import("@/views/platform/components/newsEdit.vue"),
    //       meta: {
    //         title: "新闻编辑",
    //         idCode: reCodesList.newsEdit,
    //         showLink: false
    //       }
    //     },
    //     {
    //       path: "/platform/components/newsNewly",
    //       name: "newsNewly",
    //       component: () => import("@/views/platform/components/newsNewly.vue"),
    //       meta: {
    //         title: "新闻新增",
    //         idCode: reCodesList.newsNewly,
    //         showLink: false
    //       }
    //     }
    //   ]
    // },
    {
      path: "/platform/data/analysis",
      name: "数据分析",
      component: () => import("@/views/platform/dataAnalysis.vue"),
      meta: {
        title: "数据分析",
        idCode: plCodesList.analysis
        // showLink: true
      }
    },
    // {
    //   path: "/platform/commentLibrary",
    //   name: "评语库",
    //   component: () => import("@/views/platform/commentLibrary.vue"),
    //   meta: {
    //     title: "评语库",
    //     idCode: plCodesList.analysis
    //     // showLink: true
    //   }
    // },
    {
      path: "/platform/commentLibrary",
      name: "commentLibraryDatabase",
      redirect: "/platform/commentLibraryDatabase",
      // component: () => import("@/views/platform/expertDatabase.vue"),
      meta: {
        title: "评语库"
      },
      children: [
        {
          path: "/platform/commentLibraryDatabase",
          name: "commentLibraryname",
          component: () => import("@/views/platform/commentLibrary.vue"),
          meta: {
            title: "评语库",
            idCode: plCodesList.commentLibrary,
            keepAlive: true
          }
        }
        // {
        //   path: "/platform/components/commentLibraryEdit",
        //   name: "编辑",
        //   component: () =>
        //     import("@/views/platform/components/commentLibraryEdit.vue"),
        //   meta: {
        //     title: "编辑",
        //     // idCode: reCodesList.expertEdit,
        //     showLink: false
        //   }
        // }，
        // {
        //   path: "/platform/components/commentLibraryAdd",
        //   name: "新增",
        //   component: () =>
        //     import("@/views/platform/components/commentLibraryAdd.vue"),
        //   meta: {
        //     title: "编辑",
        //     // idCode: reCodesList.expertEdit,
        //     showLink: false
        //   }
        // }
      ]
    }
  ]
};
