<script setup>
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
defineOptions({
  name: "ParentManagement"
});

const router = useRouter();

import { useRole } from "./utils/hook.jsx";
const {
  from,
  pagination,
  dataList,
  columns,
  onSearch,
  setData,
  handleInput,
  toAdd,
  openDialog,
  isFreezeApi,
  handleCurrentChange,
  handleSizeChange,
  loadingTable,
  courseTypeoptions
} = useRole();
const defaultTime = [
  new Date(2024, 4, 1, 0, 0, 0),
  new Date(2024, 4, 1, 23, 59, 59)
];
</script>

<template>
  <div>
    <div class="common bottom">
      <!-- <div class="title">家长管理</div> -->
      <div class="search">
        <el-form :inline="true" :model="from" class="demo-form-inline">
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="from.time"
              type="daterange"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              :default-time="defaultTime"
            />
          </el-form-item>
          <el-form-item label="家长姓名">
            <el-input
              v-model.trim="from.name"
              placeholder="请输入家长姓名"
              clearable
            />
          </el-form-item>
          <el-form-item label="家长手机号">
            <el-input
              v-model.trim="from.phone"
              placeholder="请输入家长手机号"
              clearable
              @blur="handleInput(from.phone)"
            />
          </el-form-item>
          <el-form-item label="账号状态">
            <el-select
              v-model="from.freeze"
              placeholder="请选择账号状态"
              style="width: 200px"
              :empty-values="[null, undefined]"
              :value-on-clear="null"
            >
              <el-option
                v-for="item in courseTypeoptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <div class="button">
              <el-button type="primary" @click="onSearch(from)">搜索</el-button>
              <el-button @click="setData(from)">重置</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <div class="common">
      <div class="puretable">
        <pure-table
          ref="tableRef"
          row-key="id"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="left"
          table-layout="auto"
          :loading="loadingTable"
          :data="dataList"
          :columns="columns"
          :pagination="{ ...pagination }"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #operation="{ row }">
            <el-button
              v-code="['612']"
              class="reset-margin"
              link
              type="primary"
              @click="openDialog('详情', row)"
            >
              详情
            </el-button>
            <el-button
              v-code="['611']"
              link
              type="primary"
              @click="toAdd('订单管理', row)"
            >
              订单管理
            </el-button>
            <el-button
              v-if="row?.freeze === false"
              link
              type="primary"
              @click="isFreezeApi('冻结', row)"
            >
              冻结
            </el-button>
            <el-button
              v-if="row?.freeze === true"
              link
              type="danger"
              @click="isFreezeApi('解冻', row)"
            >
              解冻
            </el-button>
          </template>
        </pure-table>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.common {
  // width: 100%;
  // height: 100%;
  padding: 20px 20px 2px;
  background-color: #fff;
  .title {
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: bold;
    color: #606266;
  }
  .search {
    display: flex;
    // justify-content: space-between;
    .button {
      display: flex;
      justify-content: right;
    }
  }
  // .puretable {
  //   margin-left: 25px;
  // }
}

.button_A {
  width: 80px;
  height: 30px;
  font-size: 16px;
  line-height: 30px;
  color: rgb(121.3 187.1 255);
  text-align: center;
  cursor: pointer;
  border: solid 1px rgb(121.3 187.1 255);
  border-radius: 8px;
}

.bottom {
  margin-bottom: 20px;
  padding-bottom: 2px;
}
</style>
