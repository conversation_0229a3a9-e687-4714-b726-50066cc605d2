<script setup>
import { ref, reactive, onMounted, onActivated } from "vue";
import { formatTime } from "@/utils/index";
import dayjs from "dayjs";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox, genFileId, ElUpload } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import {
  teacherResourcePoolFindAll,
  verificationTeacherDatabase,
  isFreeze,
  teacherDatabaseImportExcel,
  getDictOptions
} from "@/api/teacherResourcePool.js";
import { decrypt } from "@/utils/SM4.js";
import { View, Hide, WarningFilled, Loading } from "@element-plus/icons-vue";
import { uploadFile } from "@/utils/upload/upload";
import { useIntervalFn } from "@vueuse/core";
import { getAsyncTask } from "@/utils/common";
import { saveAs } from "file-saver";
import domain from "@/utils/http/base";
import { downloadFile } from "@iceywu/utils";
defineOptions({
  name: "InstitutionalFacultyIndex"
});

const { staffTemplate } = domain;

const router = useRouter();
const route = useRoute();

const loadingTable = ref(false);
const exportLoading = ref(false); // 批量导入 弹框确认按钮加载状态
const defaultTime = [
  new Date(2024, 4, 1, 0, 0, 0),
  new Date(2024, 4, 1, 23, 59, 59)
];
const dataList = ref([]);
const from = reactive({
  name: "",
  gender: "",
  educationLevel: "",
  major: "",
  file: {}
});

// 校验结果相关
const verificationResultVisible = ref(false); // 控制校验结果对话框显示
const verificationResultData = ref([]); // 存储校验结果数据
const hasVerificationError = ref(false); // 是否存在校验错误

// 性别选项
const genderOptions = ref([
  {
    name: "男",
    id: 1
  },
  {
    name: "女",
    id: 2
  },
  {
    name: "未知",
    id: "0"
  }
]);

// 学历选项
const educationOptions = ref([]);
const majorOptions = ref([]);

const pagination = {
  total: 0,
  pageSize: 15,
  currentPage: 1,
  background: true
};

const columns = [
  {
    label: "教师姓名",
    prop: "name",
    minWidth: 90,
    formatter: ({ name }) => {
      return name || "--";
    }
  },
  {
    label: "性别",
    prop: "gender",
    minWidth: 90,
    formatter: ({ gender }) => {
      if (gender === 1) return "男";
      else if (gender === 2) return "女";
      else if (gender === 0) return "未知";
      else return "--";
    }
  },
  {
    label: "联系方式",
    prop: "phone",
    minWidth: 90,
    slot: "phone"
  },
  {
    label: "学历",
    prop: "educationLevel",
    minWidth: 90,
    formatter: ({ educationLevel }) => {
      return educationLevel || "--";
    }
  },
  {
    label: "专业",
    prop: "major",
    minWidth: 90,
    formatter: ({ major }) => {
      return major || "--";
    }
  },
  {
    label: "创建时间",
    minWidth: 180,
    prop: "createdAt",
    formatter: ({ createdAt }) => {
      return createdAt ? dayjs(createdAt).format("YYYY-MM-DD HH:mm:ss") : "--";
    }
  },
  {
    label: "操作",
    fixed: "right",
    width: 450,
    slot: "operation"
  }
];

const removeEmptyValues = obj => {
  return Object.fromEntries(
    Object.entries(obj).filter(
      ([key, value]) =>
        value !== "" && !(Array.isArray(value) && value.length === 0)
    )
  );
};
//模版下载
const Template_Download = async (url, fileName, type) => {
  if (type == "oss") {
    window.location.href = url;
  } else {
    fetch(url)
      .then(res => res.blob())
      .then(blob => {
        saveAs(blob, fileName);
      })
      .catch(error => console.error(error));
  }
};

// 列表 api
const onSearch = async val => {
  const paramsArg = {
    page: val.page,
    size: val.size,
    sort: "createdAt,desc",
    name: val.name || "",
    gender: val.gender || "",
    educationLevel: val.educationLevel || "",
    major: val.major || ""
  };
  let aee = removeEmptyValues(paramsArg);
  const [err, res] = await requestTo(teacherResourcePoolFindAll(aee));
  if (res) {
    // 初始化电话号码显示状态
    res.content.forEach(item => {
      item.type_phone = false;
    });
    dataList.value = res.content;
    pagination.total = res.totalElements;
  }
  if (err) {
    console.error(err);
  }
};

// 搜索
const searchAdd = () => {
  const paramsArg = {
    name: from.name || "",
    gender: from.gender || "",
    educationLevel: from.educationLevel || "",
    major: from.major || "",
    page: pagination.currentPage - 1 || "",
    size: pagination.pageSize || ""
  };
  onSearch(paramsArg);
};

// 重置
const setData = () => {
  from.name = "";
  from.gender = "";
  from.educationLevel = "";
  from.major = "";
  onSearch({
    page: 0,
    size: 15
  });
};

// 每页多少条
async function handleSizeChange(val) {
  pagination.pageSize = val;
  const paramsArg = {
    name: from.name || "",
    gender: from.gender || "",
    educationLevel: from.educationLevel || "",
    major: from.major || "",
    page: 0,
    size: val
  };
  onSearch(paramsArg);
}
// 前往页数
async function handleCurrentChange(val) {
  pagination.currentPage = val;
  const paramsArg = {
    name: from.name || "",
    gender: from.gender || "",
    educationLevel: from.educationLevel || "",
    major: from.major || "",
    page: val - 1,
    size: pagination.pageSize
  };
  onSearch(paramsArg);
}

// 编辑/创建 跳转
const openJumpTo = (title, val) => {
  if (title === "edit") {
    router.push({
      path: "/platform/components/teacherCreateEdit",
      query: { title: "edit", id: val.id }
    });
  } else {
    router.push({
      path: "/platform/teacherResourcePool/add",
      query: { title: "create" }
    });
  }
};

// 查看详情
const openDetail = row => {
  router.push({
    path: "/platform/teacherResourcePool/detail",
    query: { id: row.id }
  });
};

// 编辑
const openEdit = row => {
  router.push({
    path: "/platform/teacherResourcePool/edit",
    query: { id: row.id }
  });
};

// 专家评价
const openExpertEvaluation = row => {
  router.push({
    path: "/platform/teacherResourcePool/evaluation",
    query: { id: row.id }
  });
};

// 参与课程
const openCourseParticipation = row => {
  router.push({
    path: "/platform/teacherResourcePool/coursesParticipated",
    query: { id: row.id }
  });
};

// 冻结/解冻
const freezeTeacher = row => {
  const isFreezing = !row.isFreeze; // 当前未冻结则执行冻结，反之亦然
  const confirmText = isFreezing
    ? `确定要冻结该教师吗？`
    : `确定要解冻该教师吗？`;
  const confirmTitle = isFreezing ? `确定冻结` : `确定解冻`;

  ElMessageBox.confirm(confirmText, confirmTitle, {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(async () => {
      const paramsArg = {
        id: row.id,
        freeze: isFreezing // true表示冻结，false表示解冻
      };
      const operateLog = {
        operateLogType: "PARENT_MANAGEMENT",
        operateType: isFreezing ? "冻结了" : "解冻了",
        operatorTarget: row.name
      };
      const { code } = await isFreeze(paramsArg, operateLog);
      if (code === 200) {
        ElMessage({
          type: "success",
          message: isFreezing ? "冻结成功" : "解冻成功"
        });
        // 刷新表格数据，保留当前搜索条件
        const refreshParams = {
          page: pagination.currentPage - 1,
          size: pagination.pageSize,
          name: from.name || "",
          gender: from.gender || "",
          educationLevel: from.educationLevel || "",
          major: from.major || ""
        };
        onSearch(refreshParams);
      } else {
        ElMessage({
          type: "error",
          message: "操作失败"
        });
      }
    })
    .catch(() => {});
};

// 批量导出 按钮
const batchImportDom = ref(false);
const batchImport = () => {
  batchImportDom.value = true;
};

// 文件上传
const fileList = ref([]);
const hasUploadedFile = ref(false); // 标记是否已成功上传文件
const verifyLoading = ref(false); // 校验按钮的加载状态
const verificationPassed = ref(false); // 标记校验是否通过

const beforeAvatarUpload = async rawFile => {
  if (!rawFile) return;
  from.file = rawFile;
  hasUploadedFile.value = true; // 文件上传成功
  verificationPassed.value = false; // 重置校验状态，新文件需要重新校验

  // 自动校验文件
  await verifyFile();
};
const upload = ref();
// 文件上传后替换上一个文件
const handleExceed = files => {
  upload.value.clearFiles();
  const file = files[0];
  from.file = file;
  file.uid = genFileId();
  upload.value.handleStart(file);
  hasUploadedFile.value = true; // 文件上传成功
  verificationPassed.value = false; // 重置校验状态，新文件需要重新校验

  // 自动校验文件
  verifyFile();
};
// 显示文件大小
const getFileSize = size => {
  const units = ["B", "KB", "MB", "GB", "TB"];
  let index = 0;
  while (size >= 1024 && index < units.length - 1) {
    size /= 1024;
    index++;
  }
  return `${size.toFixed(2)} ${units[index]}`;
};
// 批量导出弹框 取消
const handleClose = () => {
  batchImportDom.value = false;
  upload.value.clearFiles();
  from.file = {};
  hasUploadedFile.value = false; // 重置文件上传状态
  verificationPassed.value = false; // 重置校验状态
  verificationResultVisible.value = false; // 关闭校验结果弹窗
};
// 批量导出弹框 确认
const sureImport = async () => {
  exportLoading.value = true;
  let formdata = new FormData();
  formdata.append("file", from.file);
  const operateLog = {
    operateLogType: "TEACHER_DATABASE",
    operateType: "批量导入了师资信息"
    // operatorTarget: form.value.name,
  };
  const { code, data } = await teacherDatabaseImportExcel(formdata, operateLog);
  if (code === 200 && data) {
    const { id, complete, success } = data;
    const task = await getAsyncTask(id);
    if (task.data.complete && task.data.success) {
      const paramsArgapi = {
        page: pagination.currentPage - 1,
        size: pagination.pageSize,
        name: from.name || "",
        gender: from.gender || "",
        educationLevel: from.educationLevel || "",
        major: from.major || ""
      };
      onSearch(paramsArgapi);
      ElMessage({
        type: "success",
        message: "导入成功"
      });
      handleClose();
      exportLoading.value = false;
    } else {
      ElMessage.error(task.data.errMsg);
      exportLoading.value = false;
    }
  } else {
    ElMessage.error("文件不能为空");
    exportLoading.value = false;
  }
};

// 师资库下载模板文件
const TemplateDownload = () => {
  downloadFile(
    staffTemplate + "/gostaredu-cdn/template/师资库导入模板.xlsx",
    "师资库导入模板.xlsx"
  );
};

// 校验文件
const verifyFile = async () => {
  if (!from.file || !hasUploadedFile.value) {
    return;
  }

  verificationPassed.value = false; // 重置校验状态
  verifyLoading.value = true;
  try {
    const formData = new FormData();
    formData.append("file", from.file);

    const { code, data, msg } = await verificationTeacherDatabase(formData);

    if (code === 200) {
      if (data && data.length > 0) {
        verificationResultData.value = data;

        // 检查是否所有项都通过验证
        hasVerificationError.value = false;
        for (const row of data) {
          const failedItems = row.filter(item => !item.pass);
          if (failedItems.length > 0) {
            hasVerificationError.value = true;
            break;
          }
        }

        if (hasVerificationError.value) {
          // 有校验失败的项目，显示校验结果对话框
          verificationResultVisible.value = true;
          verificationPassed.value = false;
          ElMessage.warning("文件校验不通过，请查看详情");
        } else {
          // 所有项目都校验通过
          ElMessage.success("文件校验通过");
          verificationPassed.value = true;
        }
      } else {
        // 当code为200但data为空时，显示服务器异常警告
        ElMessage.error("服务器异常，请稍后再尝试");
        verificationPassed.value = false;
      }
    } else {
      ElMessage.error(msg || "校验失败");
      verificationPassed.value = false;
    }
  } catch (error) {
    console.error("校验错误:", error);
    ElMessage.error("校验过程中发生错误");
    verificationPassed.value = false;
  } finally {
    verifyLoading.value = false;
  }
};

// 切换手机号码显示状态
const eye_phone = (id, phoneCt) => {
  const item = dataList.value.find(item => item.id === id);
  if (item) {
    item.type_phone = !item.type_phone;
  }
};

// 根据key获取字段数据
const getFieldByKey = (row, key) => {
  const field = row.find(item => item.key === key);
  return field || { value: "--", pass: true, failResult: null };
};

const processData = nodes => {
  return nodes.map(node => {
    const { leaf, ...newNode } = node;
    if (newNode.children && newNode.children.length > 0) {
      newNode.children = processData(newNode.children);
    }
    return newNode;
  });
};

const getEducationOptionsData = async () => {
  const { code, data } = await getDictOptions({
    parentId: "453"
  });
  if (code === 200 && data) {
    educationOptions.value = processData(data);
  }
};

const getMajorOptionsData = async () => {
  const { code, data } = await getDictOptions({
    parentId: "548"
  });
  if (code === 200 && data) {
    majorOptions.value = processData(data);
  }
};

onMounted(async () => {
  const paramsArg = {
    page: 0,
    size: 15
  };
  onSearch(paramsArg);
  getEducationOptionsData();
  getMajorOptionsData();
});

onActivated(async () => {
  const paramsArg = {
    page: 0,
    size: 15
  };
  onSearch(paramsArg);
  getEducationOptionsData();
  getMajorOptionsData();
});
</script>

<template>
  <div>
    <div class="common">
      <div class="search">
        <div class="search-form">
          <el-form :inline="true" :model="from" class="demo-form-inline">
            <el-form-item label="姓名">
              <el-input
                v-model.trim="from.name"
                placeholder="请输入姓名"
                clearable
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="性别">
              <el-select
                v-model="from.gender"
                style="width: 200px"
                placeholder="请选择性别"
                clearable
                value-key="id"
              >
                <el-option
                  v-for="item in genderOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="学历">
              <el-cascader
                v-model="from.educationLevel"
                :options="educationOptions"
                :props="{
                  expandTrigger: 'hover',
                  value: 'name',
                  label: 'name',
                  children: 'children',
                  emitPath: false
                }"
                style="width: 200px"
                placeholder="请选择学历"
                clearable
                :show-all-levels="false"
              />
            </el-form-item>
            <el-form-item label="专业">
              <el-cascader
                v-model="from.major"
                :options="majorOptions"
                :props="{
                  expandTrigger: 'hover',
                  value: 'name',
                  label: 'name',
                  children: 'children',
                  emitPath: false
                }"
                style="width: 200px"
                placeholder="请选择专业"
                clearable
                :show-all-levels="false"
              />
            </el-form-item>
          </el-form>
        </div>
        <div class="button">
          <el-button @click="setData(from)">重置</el-button>
          <el-button type="primary" @click="searchAdd(from)"> 搜索 </el-button>
          <el-button type="primary" @click="batchImport()">
            批量导入
          </el-button>
          <el-button type="primary" @click="openJumpTo('create')">
            新增师资
          </el-button>
        </div>
      </div>
    </div>

    <div class="common">
      <div class="puretable">
        <pure-table
          ref="tableRef"
          row-key="id"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="left"
          table-layout="auto"
          :loading="loadingTable"
          :data="dataList"
          :columns="columns"
          :pagination="{ ...pagination }"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #phone="{ row }">
            <div class="phone-container">
              {{
                row.phone
                  ? row.type_phone
                    ? decrypt(row.phoneCt)
                    : row.phone
                  : "--"
              }}
              <div
                v-if="row.phone"
                class="eye-icon"
                @click="eye_phone(row.id, row.phoneCt)"
              >
                <el-icon v-if="!row.type_phone">
                  <Hide />
                </el-icon>
                <el-icon v-else>
                  <View />
                </el-icon>
              </div>
            </div>
          </template>
          <template #operation="{ row }">
            <el-button link type="primary" @click="openDetail(row)">
              详情
            </el-button>
            <el-button link type="primary" @click="openEdit(row)">
              编辑
            </el-button>
            <el-button link type="primary" @click="openExpertEvaluation(row)">
              专家评价
            </el-button>
            <el-button
              link
              type="primary"
              @click="openCourseParticipation(row)"
            >
              参与课程
            </el-button>
            <el-button
              :link="true"
              :type="row.isFreeze ? 'danger' : 'primary'"
              @click="freezeTeacher(row)"
            >
              {{ row.isFreeze ? "解冻" : "冻结" }}
            </el-button>
          </template>
        </pure-table>
      </div>
    </div>

    <el-dialog
      v-model="batchImportDom"
      style="min-width: 420px"
      width="30%"
      :title="`批量导入`"
      :before-close="handleClose"
    >
      <el-upload
        ref="upload"
        v-model:file-list="fileList"
        drag
        class="upload-demo"
        accept=".xlsx"
        :limit="1"
        :show-file-list="false"
        :http-request="() => {}"
        :on-exceed="handleExceed"
        :before-upload="beforeAvatarUpload"
      >
        <div v-if="!fileList.length" class="el-upload__text">
          将文件拖放到此处或 <em>单击上传</em>
        </div>
        <template v-else>
          <div class="custom-preview">
            <!-- 自定义文件预览区域 -->
            <div v-for="file in fileList" :key="file.uid" class="preview-item">
              <!-- 显示文件名  -->
              <div>{{ file.name }}</div>
              <!-- 显示文件大小 -->
              <div>{{ getFileSize(file.size) }}</div>
              <!-- 显示文件类型  -->
              <div>{{ file.type }}</div>
            </div>
          </div>
          <div v-if="verifyLoading" class="verification-status">
            <el-icon class="is-loading"><loading /></el-icon>
            文件校验中，请稍候...
          </div>
        </template>
      </el-upload>
      <div class="template-buttons">
        <el-button link type="primary" @click="TemplateDownload()">
          下载师资库导入模板
        </el-button>
      </div>
      <div class="buttom">
        <el-button @click="handleClose()">取消</el-button>
        <el-button
          v-if="verificationPassed"
          type="primary"
          :loading="exportLoading"
          @click="sureImport()"
        >
          确定
        </el-button>
      </div>
    </el-dialog>

    <!-- 校验结果对话框 -->
    <el-dialog
      v-model="verificationResultVisible"
      title="数据校验"
      width="70%"
      :close-on-click-modal="false"
      top="5vh"
      class="verification-dialog"
    >
      <div class="verification-result">
        <el-table
          border
          :data="verificationResultData"
          style="width: 100%"
          :header-cell-style="{
            background: '#f5f7fa',
            color: '#606266',
            fontWeight: 'bold',
            fontSize: '14px',
            padding: '12px 0',
            textAlign: 'center'
          }"
          :row-class-name="
            (row, rowIndex) => {
              return rowIndex === verificationResultData.length - 1
                ? 'error-example-row'
                : '';
            }
          "
        >
          <el-table-column
            type="index"
            label="序号"
            width="70"
            align="center"
          />
          <el-table-column label="姓名" min-width="120" align="center">
            <template #default="{ row }">
              <div :class="{ 'error-field': !getFieldByKey(row, 'name').pass }">
                <span class="field-value">{{
                  getFieldByKey(row, "name").value || "--"
                }}</span>
                <div
                  v-if="!getFieldByKey(row, 'name').pass"
                  class="error-message"
                >
                  <el-icon class="error-icon"><warning-filled /></el-icon>
                  {{ getFieldByKey(row, "name").failResult }}
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="性别" min-width="100" align="center">
            <template #default="{ row }">
              <div
                :class="{ 'error-field': !getFieldByKey(row, 'gender').pass }"
              >
                <span class="field-value">{{
                  getFieldByKey(row, "gender").value || "--"
                }}</span>
                <div
                  v-if="!getFieldByKey(row, 'gender').pass"
                  class="error-message"
                >
                  <el-icon class="error-icon"><warning-filled /></el-icon>
                  {{ getFieldByKey(row, "gender").failResult }}
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="联系方式" min-width="140" align="center">
            <template #default="{ row }">
              <div
                :class="{ 'error-field': !getFieldByKey(row, 'phone').pass }"
              >
                <span class="field-value">{{
                  getFieldByKey(row, "phone").value || "--"
                }}</span>
                <div
                  v-if="!getFieldByKey(row, 'phone').pass"
                  class="error-message"
                >
                  <el-icon class="error-icon"><warning-filled /></el-icon>
                  {{ getFieldByKey(row, "phone").failResult }}
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="学历" min-width="140" align="center">
            <template #default="{ row }">
              <div
                :class="{
                  'error-field': !getFieldByKey(row, 'educationLevel').pass
                }"
              >
                <span class="field-value">{{
                  getFieldByKey(row, "educationLevel").value || "--"
                }}</span>
                <div
                  v-if="!getFieldByKey(row, 'educationLevel').pass"
                  class="error-message"
                >
                  <el-icon class="error-icon"><warning-filled /></el-icon>
                  {{ getFieldByKey(row, "educationLevel").failResult }}
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="专业" min-width="140" align="center">
            <template #default="{ row }">
              <div
                :class="{ 'error-field': !getFieldByKey(row, 'major').pass }"
              >
                <span class="field-value">{{
                  getFieldByKey(row, "major").value || "--"
                }}</span>
                <div
                  v-if="!getFieldByKey(row, 'major').pass"
                  class="error-message"
                >
                  <el-icon class="error-icon"><warning-filled /></el-icon>
                  {{ getFieldByKey(row, "major").failResult }}
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="dialog-footer">
        <el-button
          type="primary"
          plain
          @click="verificationResultVisible = false"
        >
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.common {
  padding: 20px 20px 2px 20px;
  background-color: #fff;
  margin-bottom: 20px;

  .con_top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: fit-content;
    margin-bottom: 24px;

    .titles {
      font-size: 20px;
      font-weight: bold;
      color: #606266;
    }
  }

  .search {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
    .search-form {
      flex: 1;
    }
    .button {
      display: flex;
      justify-content: flex-end;
      margin-left: 20px;
      flex-wrap: wrap;
    }
  }
}

.buttom {
  display: flex;
  justify-content: end;
}

.upload-demo {
  margin-bottom: 10px;
}

.template-buttons {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.phone-container {
  display: flex;
  align-items: center;

  .eye-icon {
    margin-left: 10px;
    display: flex;
    align-items: center;
    cursor: pointer;
    &:hover {
      color: #409eff;
    }
  }
}

// 校验结果样式
.verification-result {
  max-height: 60vh;
  overflow-y: auto;
}

.error-field {
  color: #f56c6c;
}

.error-icon {
  color: #f56c6c;
  font-size: 14px;
  margin-right: 4px;
  vertical-align: -2px;
}

.error-message {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  margin-top: 4px;
  color: #f56c6c;
}

.field-value {
  display: inline-block;
}

.error-example-row {
  background-color: #fef0f0;
}

.verification-dialog .el-dialog__body {
  padding: 15px 20px;
}

.dialog-footer {
  margin-top: 20px;
  text-align: right;
}

.verification-status {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 10px;
  color: #909399;
  font-size: 14px;

  .is-loading {
    margin-right: 5px;
    animation: rotating 2s linear infinite;
  }
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
