/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {};

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ClassTrack: typeof import('./src/components/course/classTrack.vue')['default']
    CourseIntroduction: typeof import('./src/components/course/courseIntroduction.vue')['default']
    CourseReport: typeof import('./src/components/course/courseReport.vue')['default']
    Cover: typeof import('./src/components/Preview/cover.vue')['default']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopconfirm: typeof import('element-plus/es')['ElPopconfirm']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    Empty: typeof import('./src/components/Empty/index.vue')['default']
    EmptyData: typeof import('./src/components/PreviewV2/EmptyData.vue')['default']
    EvaluateContent: typeof import('./src/components/course/evaluateContent.vue')['default']
    Eyes: typeof import('./src/components/Base/eyes.vue')['default']
    FileItem: typeof import('./src/components/PreviewV2/FileItem.vue')['default']
    HomeworkContent: typeof import('./src/components/course/homeworkContent.vue')['default']
    JobDesign: typeof import('./src/components/course/jobDesign.vue')['default']
    List1: typeof import('./src/components/Base/list1.vue')['default']
    Lottie: typeof import('./src/components/Lottie/index.vue')['default']
    Preview: typeof import('./src/components/Preview/index.vue')['default']
    PreviewDialog: typeof import('./src/components/PreviewV2/PreviewDialog.vue')['default']
    PreviewFileDialog: typeof import('./src/components/PreviewV2/PreviewFileDialog.vue')['default']
    PreviewFileDialogV2: typeof import('./src/components/PreviewV2/PreviewFileDialogV2.vue')['default']
    PriceSetting: typeof import('./src/components/course/priceSetting.vue')['default']
    ReDialog: typeof import('./src/components/ReDialog/index.vue')['default']
    RichEditor: typeof import('./src/components/Base/RichEditor.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Scheduling: typeof import('./src/components/course/scheduling.vue')['default']
    Select: typeof import('./src/components/ReIcon/src/Select.vue')['default']
    Src: typeof import('./src/components/ReAnimateSelector/src/index.vue')['default']
    StudentSituation: typeof import('./src/components/course/studentSituation.vue')['default']
    TabInfo: typeof import('./src/components/Base/tabInfo.vue')['default']
    Table: typeof import('./src/components/Base/table.vue')['default']
    UserEvaluate: typeof import('./src/components/course/userEvaluate.vue')['default']
    Verify: typeof import('./src/components/Verifition/Verify.vue')['default']
    VerifyPoints: typeof import('./src/components/Verifition/Verify/VerifyPoints.vue')['default']
    VerifySlide: typeof import('./src/components/Verifition/Verify/VerifySlide.vue')['default']
    WxQrCode: typeof import('./src/components/WxQrCode/index.vue')['default']
  }
}
