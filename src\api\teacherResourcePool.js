import { http } from "@/utils/http";

/*  师资库  */
// 分页查询
export const teacherResourcePoolFindAll = params => {
  return http.request(
    "get",
    "/platform/teacherDatabase/findAllV2",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};

// 校验导入师资库
export const verificationTeacherDatabase = data => {
  return http.request(
    "post",
    "/platform/teacherDatabase/verificationTeacherDatabase",
    { data },
    { isNeedToken: true, isNeedEncrypt: false },
    { timeout: 0 }
  );
};

// Excel导入师资库信息
export const teacherDatabaseImportExcel = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/teacherDatabase/importExcel",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: false,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "TEACHER_DATABASE",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};

// 冻结教师
export const isFreeze = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/teacherDatabase/isFreeze",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "TEACHER_DATABASE",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};

//新增师资
export const addTeacherDatabase = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/teacherDatabase/save",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "TEACHER_DATABASE",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};

//师资详情
export const getTeacherDetail = params => {
  return http.request(
    "get",
    "/platform/teacherDatabase/findById",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};

//更新师资信息
export const updateTeacherDatabase = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/teacherDatabase/update",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "TEACHER_DATABASE",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};

//验证手机号
export const verifyPhone = data => {
  return http.request(
    "post",
    "/platform/teacherDatabase/verifyPhone",
    { data },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};

//根据师资查询评价
export const findAllComments = params => {
  return http.request(
    "get",
    "/platform/teacherDatabaseComments/findAll",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};

//根据师资查询参与课程
export const findAllCourses = params => {
  return http.request(
    "get",
    "/platform/teacherDatabaseCoursePeriod/findAll",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};

//根据传入id查询不同选项字典
export const getDictOptions = params => {
  return http.request(
    "get",
    "/common/dict/findModelChildrenByParentId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
