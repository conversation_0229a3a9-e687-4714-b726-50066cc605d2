<script setup>
import { onMounted, ref, nextTick, computed, onActivated } from "vue";
import { formatTime } from "@/utils/index";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import { ledeterAll, isFreeze } from "@/api/leaderLecturer.js";
import { decrypt, encryption } from "@/utils/SM4.js";
import { View, Hide } from "@element-plus/icons-vue";

defineOptions({
  name: "LeaderManagement"
});
const router = useRouter();
const route = useRoute();
onActivated(() => {
  getTableList();
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);
});
onMounted(() => {
  getTableList();
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);
});

// 计算表格高度
const searchFormHeight = ref(0);
const tableHeight = ref("calc(100vh - 309px)");

const calculateTableHeight = async () => {
  await nextTick();
  const searchForm = document.querySelector(".con_search");
  if (searchForm) {
    searchFormHeight.value = searchForm.offsetHeight;
    // 动态计算表格高度，减去表单高度、页面其他元素高度和边距
    tableHeight.value = `calc(100vh - 248px - ${searchFormHeight.value}px)`;
  }
};

// 搜索表单
const form = ref({
  startTime: "",
  endTime: "",
  name: "",
  phone: "",
  roleId: "3",
  freeze: "all",
  organizationName: ""
});
// 表格数据
const tableData = ref([
  // {
  //   id: 0,
  //   createdAt: 0,
  //   updatedAt: 0,
  //   name: "1223",
  //   organizationName: "dd ",
  //   courseTypeName: "fff",
  //   termNumber: 0
  // }
]);
const params = ref({
  page: 1,
  size: 15,
  sort: "createdAt,desc",
  totalElements: 0
});
// 获取列表信息
const getTableList = async data => {
  let paramsData = {
    page: params.value.page - 1,
    size: params.value.size,
    sort: params.value.sort
  };
  for (const paramsDataKey in form.value) {
    let isArray = Array.isArray(form.value[paramsDataKey]);
    if (isArray) {
      if (form.value[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    } else {
      if (form.value[paramsDataKey]) {
        if (paramsDataKey === "freeze" && form.value[paramsDataKey] === "all") {
          continue;
        }
        paramsData[paramsDataKey] = paramsData[paramsDataKey] =
          paramsDataKey === "phone"
            ? encryption(String(form.value[paramsDataKey]))
            : form.value[paramsDataKey];
      }
    }
  }

  // console.log("领队 paramsData =", paramsData);
  // return;
  const [err, result] = await requestTo(ledeterAll(paramsData));
  if (result) {
    result?.content.forEach(item => {
      item.show_phone = false;
      // item.show_card = false;
    });
    tableData.value = result?.content;
    params.value.totalElements = result.totalElements;
    await nextTick();
    calculateTableHeight();
  } else {
    ElMessage.error(err);
  }
};
const eye_phone = id => {
  const item = tableData.value.find(item => item.id === id);
  if (item) {
    item.type_phone = !item.type_phone;
  }
};
// const eye_card = id => {
//   const item = tableData.value.find(item => item.id === id);
//   if (item) {
//     item.show_card = !item.show_card;
//   }
// };
//分页
const handleCurrentChange = e => {
  params.value.page = e;
  getTableList();
};
const handleSizeChange = val => {
  params.value.page = 1;
  params.value.size = val;
  getTableList();
};
// 查看详情
const getId = val => {
  console.log("🐳-----getId-----");
}; //搜索
const searchData = () => {
  params.value.page = 1;
  getTableList();
};
// 重置
const setData = () => {
  form.value = {};
  form.value.roleId = 3;
  form.value.freeze = "all";
  params.value.page = 1;
  value1.value = [];
  getTableList();
};
// 选择时间
const timeChange = async e => {
  form.value.startTime = e ? new Date(e[0])?.getTime() : "";
  form.value.endTime = e ? new Date(e[1])?.getTime() + (86400000 - 1) : "";
  await nextTick();
  calculateTableHeight();
};

const value1 = ref([]);
const freezeList = ref([
  {
    label: "全部",
    value: "all"
  },
  {
    label: "正常",
    value: "false"
  },
  {
    label: "冻结",
    value: "true"
  }
]);
// 冻结/解冻
const getButtonText = isPub => {
  return isPub === true ? "解冻" : "冻结";
};
const Freeze = async row => {
  const isFreezing = !row.freeze;
  const confirmText = isFreezing
    ? "你确定要冻结该账户吗?"
    : "你确定要解冻该账户吗?";
  const confirmTitle = isFreezing ? "确定冻结" : "确定解冻";
  const successMessage = isFreezing ? "已冻结" : "已解冻";

  try {
    await ElMessageBox.confirm(confirmText, confirmTitle, {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: ""
    });
    const params = {
      id: row.id,
      freeze: isFreezing
    };
    const operateLog = {
      operateLogType: "LEADER_MANAGEMENT",
      operateType: isFreezing ? "冻结了" : "解冻了",
      operatorTarget: row.name
    };
    const { code, msg } = await isFreeze(params, operateLog);
    if (code === 200) {
      ElMessage({
        message: successMessage,
        type: "success"
      });

      getTableList();
    } else {
      ElMessage.error(msg);
    }
  } catch (error) {
    // console.log("操作取消");
  }
};

// 领队详情
const getInfoid = id => {
  router.push({
    path: "/accountNumber/components/teacherDetails",
    query: { title: "ldMang", id: id }
  });
};
</script>

<template>
  <div class="page-container">
    <div class="common">
      <!-- <div class="con_top">
        <div class="titles">领队管理</div>
      </div> -->
      <div class="con_search">
        <el-form :model="form" :inline="true">
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="value1"
              type="daterange"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              style="width: 310px"
              @change="timeChange"
            />
          </el-form-item>
          <el-form-item label="机构">
            <el-input
              v-model.trim="form.organizationName"
              placeholder="请输入"
              clearable
              style="width: 180px"
            />
          </el-form-item>
          <el-form-item label="领队姓名">
            <el-input
              v-model.trim="form.name"
              placeholder="请输入"
              clearable
              style="width: 180px"
            />
          </el-form-item>
          <el-form-item label="领队手机号">
            <el-input
              v-model="form.phone"
              placeholder="请输入"
              clearable
              style="width: 180px"
              @input="form.phone = form.phone.replace(/\D/g, '')"
            />
          </el-form-item>
          <el-form-item label="账号状态">
            <el-select
              v-model="form.freeze"
              style="width: 180px"
              placeholder="请选择"
            >
              <el-option
                v-for="item in freezeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label=" ">
            <div class="flex">
              <el-button type="primary" @click="searchData">搜索</el-button>
              <el-button @click="setData">重置</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <div class="containers">
      <el-scrollbar :style="{ height: tableHeight }">
        <div class="con_table">
          <el-table
            :data="tableData"
            table-layout="fixed"
            :header-cell-style="{
              backgroundColor: '#fafafa',
              color: '#565353'
            }"
            highlight-current-row
            :max-height="tableHeight"
          >
            <el-table-column prop="id" label="领队ID">
              <template #default="scope">
                {{ scope.row.id || "--" }}
              </template>
            </el-table-column>
            <el-table-column prop="name" label="领队姓名" align="left">
              <template #default="scope">
                <div>
                  {{ scope.row.name || "--" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="phone"
              label="领队手机号"
              align="left"
              min-width="140"
            >
              <template #default="scope">
                <div class="eye_style">
                  {{
                    scope.row.phone
                      ? scope.row.type_phone
                        ? decrypt(scope.row.phoneCt)
                        : scope.row.phone
                      : "-"
                  }}
                  <div
                    v-if="scope.row.phone"
                    class="eye"
                    @click="eye_phone(scope.row.id, scope.row.phoneCt)"
                  >
                    <el-icon v-if="!scope.row.type_phone">
                      <Hide />
                    </el-icon>
                    <el-icon v-else>
                      <View />
                    </el-icon>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="account" label="领队账号" align="left">
              <template #default="scope">
                <div>
                  {{ scope.row.account || "--" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="organizationName" label="机构" align="left">
              <template #default="scope">
                <div>
                  {{ scope.row.organizationName || "--" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column width="200px" prop="createdAt" label="创建时间">
              <template #default="scope">
                <div>
                  {{
                    formatTime(scope.row?.createdAt, "YYYY-MM-DD HH:mm") || "--"
                  }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="freeze" label="账号状态" align="left">
              <template #default="scope">
                <div
                  :style="{
                    color: scope.row.freeze === true ? 'red' : ''
                  }"
                >
                  {{ scope.row.freeze === true ? "冻结" : "正常" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              align="left"
              min-width="104"
            >
              <template #default="scope">
                <div class="operate">
                  <el-button
                    v-code="['619']"
                    type="primary"
                    link
                    @click="getInfoid(scope.row.id)"
                  >
                    详情
                  </el-button>
                  <el-button
                    type="primary"
                    link
                    :style="{
                      color: scope.row.freeze === true ? 'red' : '#409EFF'
                    }"
                    @click="Freeze(scope.row)"
                  >
                    {{ getButtonText(scope.row.freeze) }}
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-scrollbar>
      <div class="con_pagination">
        <!-- 分页 -->
        <el-pagination
          v-model:current-page="params.page"
          v-model:page-size="params.size"
          class="pagination"
          background
          :page-sizes="[15, 30, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="params.totalElements"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.page-container {
  display: flex;
  flex-direction: column;
  // height: 86vh;
  overflow: hidden;
}

.scrollbar {
  background-color: #fff;
}

.common {
  box-sizing: border-box;
  padding: 20px 20px 2px;
  background-color: #fff;
  margin-bottom: 20px;
  // width: calc(100% - 48px);
  .con_top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: fit-content;
    margin-bottom: 24px;

    .titles {
      font-size: 20px;
      font-weight: bold;
      color: #606266;
    }
  }

  .con_search {
    display: flex;
    align-items: center;
    width: 100%;
    height: fit-content;
  }
}

.containers {
  box-sizing: border-box;
  // width: calc(100% - 48px);
  // display: flex;
  // flex-direction: column;
  // flex: 1;
  padding: 20px;
  background: #fff;
  // overflow: hidden;

  .con_table {
    // width: calc(100% - 25px);
    // margin-bottom: 24px;
    margin-left: 20px;

    .btnse {
      color: #409eff;
    }
  }

  .con_pagination {
    display: flex;
    justify-content: flex-end;
    width: 100%;
    padding: 10px 20px;
    background-color: #fff;
  }
  .eye_style {
    width: 120px;
    // display: flex;
    // align-items: center;
    // justify-content: center;
    .eye {
      float: right;
      cursor: pointer;
      :hover {
        color: #409eff;
      }
    }
  }
}
</style>
