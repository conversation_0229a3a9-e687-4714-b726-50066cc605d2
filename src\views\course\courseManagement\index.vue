<script setup>
import {
  onMounted,
  ref,
  onActivated,
  nextTick,
  onUnmounted,
  onDeactivated
} from "vue";
import { formatTime } from "@/utils/index";
import {
  courseFindAll,
  courseIsFreeze,
  findAllCourseType
} from "@/api/course.js";
// import { courseTypeFindAllNotPage } from "@/api/courseClassify.js";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";

defineOptions({
  name: "CourseManagementIndex"
});
onActivated(() => {
  getTableList();
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);
});
onMounted(() => {
  courseTypeFindApi();
  getTableList();
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);
});

onUnmounted(() => {
  window.removeEventListener("resize", calculateTableHeight);
});

onDeactivated(() => {
  window.removeEventListener("resize", calculateTableHeight);
});

const router = useRouter();

// 计算表格高度
const searchFormHeight = ref(0);
const tableHeight = ref("88vh"); // 初始默认值，会被 calculateTableHeight 覆盖

const calculateTableHeight = async () => {
  await nextTick();
  const searchForm = document.querySelector(".search .con_search");
  if (searchForm) {
    searchFormHeight.value = searchForm.offsetHeight;
    tableHeight.value = `calc(88vh - 140px - ${searchFormHeight.value}px)`;
  }
};

// 搜索表单
const form = ref({
  startTime: "",
  endTime: "",
  name: "",
  organizationName: "",
  courseTypeId: "",
  freeze: 0
});
// 课程状态选项
const stateOptions = ref([
  {
    name: "全部",
    id: 0
  },
  {
    name: "正常",
    id: 1
  },
  {
    name: "冻结",
    id: 2
  }
]);
// 表格数据
const tableData = ref([]);
const params = ref({
  page: 1,
  size: 15,
  sort: "createdAt,desc",
  totalElements: 0
});
const courseTypeoptions = ref([{ value: 0, label: "全部" }]);
const courseTypeFindApi = async () => {
  let [err, res] = await requestTo(findAllCourseType());
  // console.log("🐠res-----------3333------------------->", res);
  if (res) {
    courseTypeoptions.value = courseTypeoptions.value.concat(
      transformArray(res)
    );
  }
};
function transformArray(inputArray) {
  return inputArray?.map(item => {
    const newItem = {
      value: item.id,
      label: item.name
    };

    // 如果有 children，则递归转换
    if (item.children && item.children.length > 0) {
      newItem.children = transformArray(item.children);
    }

    return newItem;
  });
}
const courseTypeChange = val => {
  form.value.courseTypeId = val[val.length - 1];
};
// 获取列表信息
const getListLoading = ref(false);
const getTableList = async data => {
  if (getListLoading.value) {
    return;
  }
  getListLoading.value = true;
  let paramsData = {
    page: params.value.page - 1,
    size: params.value.size,
    sort: params.value.sort
  };
  for (const paramsDataKey in form.value) {
    let isArray = Array.isArray(form.value[paramsDataKey]);
    if (isArray) {
      if (form.value[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    } else {
      if (form.value[paramsDataKey]) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    }
  }
  // console.log("🍧-----paramsData-----", paramsData);
  if (paramsData.freeze && form.value.freeze === 1) {
    paramsData.freeze = false;
  } else if (paramsData.freeze && form.value.freeze === 2) {
    paramsData.freeze = true;
  }
  const [err, result] = await requestTo(courseFindAll(paramsData));
  // console.log("🎁-----result-----", result);
  if (result) {
    tableData.value = result?.content;

    params.value.totalElements = result.totalElements;
    await nextTick();
    calculateTableHeight();
  } else {
    ElMessage.error(err);
  }
  getListLoading.value = false;
};
//分页
const handleCurrentChange = e => {
  params.value.page = e;
  getTableList();
};
const handleSizeChange = val => {
  params.value.page = 1;
  params.value.size = val;
  getTableList();
};
// 查看详情
const getId = id => {
  router.push({ path: "/course/courseDetails", query: { id } });
}; //搜索
const searchData = () => {
  params.value.page = 1;
  getTableList();
};
// 重置
const setData = () => {
  form.value = {};
  params.value.page = 1;
  value1.value = [];
  getTableList();
};
// 清除数据
const clearEvt = val => {
  if (val === "name") {
    form.value.name = "";
  } else if (val === "time") {
    form.value.startTime = "";
    form.value.endTime = "";
  } else if (val === "organizationName") {
    form.value.organizationName = "";
  } else if (val === "courseTypeId") {
    form.value.courseTypeId = "";
  }
  // params.value.page = 1;
  getTableList();
};
// 选择时间
const timeChange = value => {
  if (value) {
    form.value.startTime = new Date(value[0])?.getTime();
    // form.value.endTime = new Date(value[1])?.getTime();
    const endDate = new Date(value[1]);
    endDate.setHours(23, 59, 59, 999); // 关键修改
    form.value.endTime = endDate.getTime();
  }
};

const value1 = ref([]);
// 详情
const detailEvt = id => {
  router.push({ path: "/course/management/detail", query: { id } });
};
const operateLog = ref({});
// 是否冻结
const freezeEvt = (row, bool) => {
  let freezeText =
    bool === true
      ? "冻结该课程后，无法新建和编辑课期，已有课期安排不受影响，确定要冻结该课程吗？"
      : "解冻该课程后，可以新建和编辑课期，已有课期安排不受影响，确定要解冻该课程吗？";
  let title = bool === true ? "冻结课程" : "解冻课程";
  ElMessageBox.confirm(`${freezeText}`, `${title}`, {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(() => {
      isFreezeApi(row, bool);
    })
    .catch(() => {});
};
const isFreezeApi = async (row, bool) => {
  const params = {
    id: row.id,
    freeze: bool
  };
  if (bool === true) {
    operateLog.value = {
      operateLogType: "COURSE_MANAGEMENT",
      operateType: `冻结了“${row.name}”课程`
    };
  } else {
    operateLog.value = {
      operateLogType: "COURSE_MANAGEMENT",
      operateType: `解冻了“${row.name}”课程`
    };
  }

  const { code } = await courseIsFreeze(params, operateLog.value);
  if (code === 200) {
    ElMessage({
      type: "success",
      message: bool === true ? "冻结成功" : "解冻成功"
    });
    getTableList();
  } else {
    ElMessage({
      type: "error",
      message: bool === true ? "冻结失败" : "解冻失败"
    });
  }
};

const defaultTime = [
  new Date(2024, 4, 1, 0, 0, 0),
  new Date(2024, 4, 1, 23, 59, 59)
];
</script>

<template>
  <div class="containers">
    <div class="con_top">
      <!-- <div class="titles">课程管理</div> -->
    </div>
    <div class="search">
      <div class="con_search">
        <el-form :model="form" :inline="true">
          <el-form-item label="创建时间">
            <el-date-picker
              v-model.trim="value1"
              type="daterange"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              @change="timeChange"
              @clear="clearEvt('time')"
            />
          </el-form-item>
          <el-form-item label="课程名">
            <el-input
              v-model.trim="form.name"
              placeholder="请输入"
              style="width: 180px"
              clearable
              @clear="clearEvt('name')"
            />
          </el-form-item>
          <el-form-item label="机构">
            <el-input
              v-model.trim="form.organizationName"
              placeholder="请输入"
              style="width: 180px"
              clearable
              @clear="clearEvt('organizationName')"
            />
          </el-form-item>
          <el-form-item label="课程类型">
            <!-- <el-select
            v-model="form.courseTypeId"
            style="width: 120px"
            placeholder="请选择"
            :empty-values="[null, undefined]"
            :value-on-clear="null"
          >
            <el-option
              v-for="item in courseTypeoptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select> -->
            <el-cascader
              v-model.trim="form.courseTypeId"
              :options="courseTypeoptions"
              :show-all-levels="false"
              @change="courseTypeChange"
              @clear="clearEvt('courseTypeId')"
            />
          </el-form-item>
          <el-form-item label="课程状态">
            <el-select
              v-model="form.freeze"
              style="width: 120px"
              placeholder="请选择"
              value-key="id"
            >
              <el-option
                v-for="item in stateOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label=" ">
            <div class="flex">
              <el-button type="primary" @click="searchData">搜索</el-button>
              <el-button @click="setData">重置</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <div class="main">
      <el-scrollbar class="scrollbar" :style="{ height: tableHeight }">
        <div class="con_table">
          <el-table
            :data="tableData"
            :header-cell-style="{
              backgroundColor: '#fafafa',
              color: '#565353'
            }"
            table-layout="fixed"
            :max-height="tableHeight"
          >
            <el-table-column
              prop="name"
              label="课程名"
              width="300"
              align="left"
              show-overflow-tooltip
              fixed
            >
              <template #default="scope">
                {{ scope.row.name || "--" }}
              </template>
            </el-table-column>
            <el-table-column
              prop="organizationName"
              label="机构"
              align="left"
              width="300"
              show-overflow-tooltip
            >
              <template #default="scope">
                {{ scope.row.organizationName || "--" }}
              </template>
            </el-table-column>
            <el-table-column
              prop="courseTypeName"
              label="课程类型"
              align="left"
              min-width="120"
            >
              <template #default="scope">
                <div>
                  {{ scope.row.courseTypeName || "--" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              width="250"
              prop="createdAt"
              label="创建时间"
              align="left"
            >
              <template #default="scope">
                <div>
                  {{
                    formatTime(scope.row?.createdAt, "YYYY-MM-DD HH:mm:ss") ||
                    "--"
                  }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="termNumber" label="课程期数" align="left">
              <template #default="scope">
                <div>
                  {{ scope.row.termNumber || 0 }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="freeze" label="课程状态" align="left">
              <template #default="scope">
                <div
                  :style="{ color: scope.row.freeze === true ? '#f56c6c' : '' }"
                >
                  {{ scope.row.freeze === true ? "冻结" : "正常" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              align="left"
              width="200px"
            >
              <template #default="{ row }">
                <div class="option">
                  <div class="btnse" @click="detailEvt(row.id)">详情</div>
                  <div
                    class="btnse"
                    @click="
                      router.push({
                        path: '/course/management/allEvaluate',
                        query: { courseId: row.id }
                      })
                    "
                  >
                    用户评价
                  </div>
                  <div class="btnse">
                    <div
                      v-if="!row.freeze"
                      class="freeze"
                      @click="freezeEvt(row, true)"
                    >
                      冻结
                    </div>
                    <div v-else class="nofreeze" @click="freezeEvt(row, false)">
                      解冻
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-scrollbar>

      <div class="con_pagination">
        <!-- 分页 -->
        <el-pagination
          v-model:current-page="params.page"
          v-model:page-size="params.size"
          class="pagination"
          background
          :page-sizes="[15, 30, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="params.totalElements"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.scrollbar {
  // padding-top: 20px;
  // padding: 20px;
  // height: calc(100vh - 181px);
  // height: calc(100vh - 329px);

  background-color: #fff;
}
// :deep(.el-table .cell) {
//   padding: 0;
// }
.containers {
  display: flex;
  flex-direction: column;
  height: 88vh; /* Or adjust based on your global layout, e.g., calc(100vh - GlobalHeaderHeight) */
  overflow: hidden;
  box-sizing: border-box;
  // width: calc(100% - 48px);
  // height: 100%;
  // padding: 24px;
  // background: #fff;

  // .con_top {
  //   display: flex;
  //   align-items: center;
  //   justify-content: space-between;
  //   width: 100%;
  //   height: fit-content;
  //   margin-bottom: 24px;

  //   .titles {
  //     font-size: 20px;
  //     font-weight: bold;
  //     color: #606266;
  //   }
  // }
  .search {
    box-sizing: border-box;
    padding: 20px 20px 2px 20px;
    background-color: #fff;
    margin-bottom: 20px;
    .con_search {
      display: flex;
      align-items: center;
      width: 100%;
      height: fit-content;
      // .input_width {
      //   width: 200px;
      // }
    }
  }

  .main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden; /* Prevents .main from expanding due to its own content */
    background: #fff;
    padding: 20px 20px 0 20px;
    box-sizing: border-box;
    // height: 100%; /* Removed as height is now controlled by flex:1 */
  }
  .con_table {
    // width: calc(100% - 25px);

    // margin-left: 25px;

    .option {
      display: flex;
      // justify-content: center;

      .btnse {
        display: flex;
        margin-right: 16px;
        color: #409eff;
        cursor: pointer;

        .nofreeze {
          color: #f56c6c;
          cursor: pointer;
        }
      }
    }
  }

  .con_pagination {
    display: flex;
    justify-content: flex-end;
    width: 100%;
    margin-top: 15px;
  }
}
</style>
