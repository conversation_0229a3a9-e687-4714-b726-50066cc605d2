<script setup>
import { ref } from "vue";
import { Hide, View } from "@element-plus/icons-vue";
import RichEditor from "@/components/Base/RichEditor.vue";

const form = ref({
  startTime: "",
  endTime: "",
  name: "",
  ordersId: "",
  courseName: "",
  orderStatus: ""
});
const formData = ref([
  {
    label: "机构名称",
    type: "text",
    // check: true,
    prop: "orderFile",
    placeholder: "",
    width: "200px"
  },
  {
    label: "机构ID",
    type: "text",
    // check: true,
    prop: "orderFile",
    placeholder: "",
    width: "200px"
  },
  {
    label: "申请时间",
    type: "text",
    // check: true,
    prop: "orderFile",
    placeholder: "",
    width: "200px"
  },
  {
    label: "审核状态",
    type: "text",
    // check: true,
    prop: "orderFile",
    placeholder: "",
    width: "200px"
  },
  {
    label: "审核员",
    type: "text",
    // check: true,
    prop: "orderFile",
    placeholder: "",
    width: "200px"
  },
  {
    label: "审核类型",
    type: "text",
    // check: true,
    prop: "orderFile",
    placeholder: "",
    width: "200px"
  },
  {
    label: "审核意见",
    type: "text",
    // check: true,
    prop: "orderFile",
    placeholder: "",
    width: "200px"
  }
]);
// 表格数据
const tableData = ref([
  {
    id: 0,
    name: "机构营业执照",
    File: "营业执照.jpg",
    FileH: "营业执照.jpg"
  },
  {
    id: 1,
    name: "机构管理员",
    File: "张三",
    FileH: "李四"
  },
  {
    id: 2,
    name: "机构简介",
    type: "editor",
    File: "111",
    FileH: "2222"
  }
]);
</script>

<template>
  <div>
    <div class="header">
      <el-descriptions title="" :column="2" border :label-width="'200px'">
        <el-descriptions-item
          v-for="(item, index) in formData"
          :key="index"
          label-align="center"
          label-class-name="my-label"
        >
          <template #label>
            {{ item.label }}
          </template>
          <el-form-item
            :prop="item.prop"
            :inline-message="item.check"
            style="margin-bottom: 0"
            :show-message="true"
            error-placement="right"
          >
            <!-- text -->
            <template v-if="item.type === 'text'">
              <div class="cell_item">
                <div style="color: #a8a8a8">
                  <!-- {{
                    item.prop === "createdAt"
                      ? formatTime(form[item.prop], "YYYY-MM-DD HH:mm:ss")
                      : form[item.prop] || "--"
                  }} -->
                  {{ form[item.prop] || "--" }}
                </div>
                <span v-if="form[item.prop] && item.isEye" class="icon">
                  <el-icon
                    v-if="item.isView"
                    style="cursor: pointer"
                    @click="isViewFn(index)"
                  >
                    <Hide />
                  </el-icon>
                  <el-icon
                    v-else
                    style="cursor: pointer"
                    @click="isViewFn(index)"
                  >
                    <View />
                  </el-icon>
                </span>
              </div>
            </template>
          </el-form-item>
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="mian">
      <p style="font-weight: 600">修改内容</p>
      <el-table :data="tableData" height="450">
        <el-table-column prop="name" width="120px" label="项目" align="center">
          <template #default="scope">
            <div>
              {{ scope.row.name || "--" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="File" label="修改前" align="center">
          <template #default="scope">
            <div v-if="scope.row.type !== 'editor'">
              {{ scope.row.File || "--" }}
            </div>
            <div v-else style="background: #f5f5f5">
              <div style="width: 100%; background: #f5f5f5">
                <RichEditor
                  v-model="scope.row.File"
                  height="150px"
                  :isOpen="false"
                  :readOnly="true"
                />
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="FileH" label="修改后" align="center">
          <template #default="scope">
            <div v-if="scope.row.type !== 'editor'">
              {{ scope.row.FileH || "--" }}
            </div>
            <div v-else>
              <div style="width: 100%; background: #f5f5f5">
                <RichEditor
                  v-model="scope.row.FileH"
                  height="150px"
                  :isOpen="false"
                  :readOnly="true"
                />
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.header {
  padding: 20px;
  background: #fff;
}
:deep(.my-label) {
  background: #e1f5ff !important;
}
.mian {
  padding: 20px;
  background: #fff;
  margin-top: 20px;
}
</style>
