<script setup>
import { ref, reactive, onMounted } from "vue";
import { uploadFile } from "@/utils/upload/upload.js";
import ImgBlurHash from "@/components/ImgBlurHash";
import { Edit, Plus } from "@element-plus/icons-vue";
import { useRole } from "./utils/recommendHook.jsx";

const props = defineProps({
  label: {
    type: String,
    default: ""
  }
});

const {
  heatOne,
  heatTow,
  refinedOne,
  refinedTow,
  loadingTable,
  optionsData,

  columnsOne,
  columnsTow,
  dataListOne,
  dataListTow,
  popularNewlyAdd,
  refinedNewlyAdd,
  handleMenuOne,
  handleMenuTow,
  // dataList,
  paginationOne,
  paginationTow,
  handleSizeChangeOne,
  handleCurrentChangeOne,
  handleSizeChangeTow,
  handleCurrentChangeTow,

  handleDelete,
  ruleFormRef,
  rules,
  form,
  gridData,
  params,
  // optionsData,

  paramsHandleCurrentChange,
  paramsHandleSizeChange,

  getInfoid,
  changeOrganization,
  columnsThree,

  addressLinkDom,

  focusCourseId,
  editAdd,
  fileUpload,
  confirmAdd,
  beforeCloseDom,
  buttonLoading,

  onSearchA,
  onSearchB
} = useRole();

onMounted(() => {
  const paramsArg = {
    page: 0,
    size: 10
  };
  if (props.label === "热门课程") {
    onSearchA(paramsArg);
  } else if (props.label === "精品课程") {
    onSearchB(paramsArg);
  }
});

console.log("🍧-----props-----", props);
</script>

<template>
  <div class="commonapp">
    <div v-if="props.label === '热门课程'">
      <div class="title">
        <!-- 热门课程路线推荐 -->
        <el-button type="primary" @click="popularNewlyAdd">新增</el-button>
      </div>
      <pure-table
        ref="tableRef"
        row-key="id"
        adaptive
        :adaptiveConfig="{ offsetBottom: 108 }"
        align-whole="left"
        table-layout="auto"
        :loading="loadingTable"
        :data="dataListOne"
        :style="{ height: 'calc(100%-110px)' }"
        :columns="columnsOne"
        :pagination="{ ...paginationOne }"
        :header-cell-style="{
          background: 'var(--el-fill-color-light)',
          color: 'var(--el-text-color-primary)'
        }"
        @page-size-change="handleSizeChangeOne"
        @page-current-change="handleCurrentChangeOne"
      >
        <template #operation="{ row }">
          <div class="botlist">
            <div class="u">
              <el-button
                class="reset-margin"
                link
                type="primary"
                @click="editAdd('热门课程', row)"
              >
                编辑
              </el-button>
            </div>
            <div class="u">
              <el-button
                class="reset-margin"
                link
                type="danger"
                @click="handleDelete('热门课程', row)"
              >
                删除
              </el-button>
            </div>
            <div class="u">
              <el-button
                v-if="row.id !== heatOne"
                class="reset-margin"
                link
                type="primary"
                @click="handleMenuOne('上移', row)"
              >
                上移
              </el-button>
            </div>
            <div class="u">
              <el-button
                v-if="row.id !== heatTow"
                class="reset-margin"
                link
                type="primary"
                @click="handleMenuOne('下移', row)"
              >
                下移
              </el-button>
            </div>
          </div>
        </template>
      </pure-table>
    </div>

    <div v-else>
      <div class="title">
        <!-- 精品课程展示 -->
        <el-button type="primary" @click="refinedNewlyAdd">新增</el-button>
      </div>
      <pure-table
        ref="tableRef"
        row-key="id"
        adaptive
        :adaptiveConfig="{ offsetBottom: 108 }"
        align-whole="left"
        table-layout="auto"
        :loading="loadingTable"
        :data="dataListTow"
        :style="{ height: 'calc(100%-110px)' }"
        :columns="columnsTow"
        :pagination="{ ...paginationTow }"
        :header-cell-style="{
          background: 'var(--el-fill-color-light)',
          color: 'var(--el-text-color-primary)'
        }"
        @page-size-change="handleSizeChangeTow"
        @page-current-change="handleCurrentChangeTow"
      >
        <template #operation="{ row }">
          <div class="botlist">
            <div class="u">
              <el-button
                class="reset-margin"
                link
                type="primary"
                @click="editAdd('精品课程', row)"
              >
                编辑
              </el-button>
            </div>
            <div class="u">
              <el-button
                class="reset-margin"
                link
                type="danger"
                @click="handleDelete('精品课程', row)"
              >
                删除
              </el-button>
            </div>
            <div class="u">
              <el-button
                v-if="row.id !== refinedOne"
                class="reset-margin"
                link
                type="primary"
                @click="handleMenuTow('上移', row)"
              >
                上移
              </el-button>
            </div>
            <div class="u">
              <el-button
                v-if="row.id !== refinedTow"
                class="reset-margin"
                link
                type="primary"
                @click="handleMenuTow('下移', row)"
              >
                下移
              </el-button>
            </div>
          </div>
        </template>
      </pure-table>
    </div>

    <el-dialog
      v-model="addressLinkDom"
      style="min-width: 420px"
      width="30%"
      :title="form.title + props.label"
      @closed="beforeCloseDom"
    >
      <el-form
        ref="ruleFormRef"
        :model="form"
        label-width="110px"
        label-position="right"
        :rules="rules"
      >
        <el-form-item
          label="图片"
          prop="url"
          show-word-limit
          style="display: flex; align-items: center"
        >
          <el-upload
            v-model="form.url"
            class="avatar-uploader"
            accept="image/*"
            :show-file-list="false"
            :http-request="() => {}"
            :before-upload="uploadFile => fileUpload(uploadFile, form)"
          >
            <el-image
              v-if="form.url"
              :src="form.url"
              fit="scale-down"
              class="avatar"
            />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <text style="display: block; width: 100%; font-size: 12px">
            支持上传png、jpg、jpeg、gif、bmp、webp图片格式，最多上传1张，最佳尺寸：200*200px，单张大小不超过10MB
          </text>
        </el-form-item>
        <el-form-item label="地址链接" prop="textarea2" show-word-limit>
          <el-input
            v-model.trim="form.textarea2"
            placeholder="请输入地址链接"
            clearable
          />
        </el-form-item>
        <el-form-item label="机构" prop="organizationId" show-word-limit>
          <el-select
            v-model="form.organizationId"
            placeholder="请选择机构"
            clearable
            filterable
            @change="changeOrganization(form)"
          >
            <el-option
              v-for="item in optionsData"
              :key="item?.id"
              :label="item?.name"
              :value="item?.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="课程" prop="courseId" show-word-limit>
          <el-select
            v-model="form.courseId"
            placeholder="请选择课程"
            clearable
            filterable
            @focus="focusCourseId(form)"
          >
            <template v-if="gridData.length > 0">
              <el-option
                v-for="course in gridData"
                :key="course.id"
                :label="course.name"
                :value="course.id"
              />
            </template>
            <template v-else>
              <el-option
                v-if="form.organizationId"
                :label="'该机构暂无课程'"
                :value="''"
                disabled
                style="text-align: center"
              />
              <el-option
                v-else
                :label="'请先选择机构'"
                :value="''"
                disabled
                style="text-align: center"
              />
            </template>
          </el-select>
        </el-form-item>
        <!-- <el-form-item
          label="图片"
          prop="url"
          show-word-limit
          style="display: flex; align-items: center"
        >
          <div style="display: flex; min-width: 220px; min-height: 110px">
            <div
              style="
                width: 160px;
                margin-right: 20px;
                display: grid;
                place-items: center;
                border: 1px solid var(--el-border-color);
              "
            >
              <el-image
                v-if="form.url"
                style="width: 158px; height: 108px"
                :src="form.url"
                show-progress
                :zoom-rate="1.2"
                :max-scale="7"
                :min-scale="0.2"
                :preview-src-list="[form.url]"
                fit="scale-down"
              />
            </div>
            <div style="padding-top: 42px">
              <el-upload
                v-model="form.url"
                action="#"
                :show-file-list="false"
                :http-request="() => {}"
                :before-upload="uploadFile => fileUpload(uploadFile, form)"
              >
                <el-button text circle>
                  <el-icon size="20">
                    <Edit />
                  </el-icon>
                </el-button>
              </el-upload>
            </div>
          </div>
        </el-form-item> -->
      </el-form>

      <div class="buttom">
        <el-button @click="beforeCloseDom">取消</el-button>
        <el-button
          type="primary"
          :loading="buttonLoading"
          @click="confirmAdd(props.label, ruleFormRef, form)"
        >
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.commonapp {
  // margin: 20px 20px 0px;
  .title {
    margin-bottom: 10px;
    font-size: 20px;
    font-weight: bold;
    color: #606266;
    display: flex;
    justify-content: end;
    // height: ;
  }
}

.buttom {
  display: flex;
  justify-content: end;
}

.botlist {
  display: flex;
  .u {
    width: 50px;
  }
}

// .custom-title {
//   font-size: 20px;
//   font-weight: bold;
//   color: #606266;
// }

.con_pagination {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  padding-top: 20px;
}

:deep(.el-select--large .el-select__wrapper) {
  height: 50px;
}

:deep(.avatar-uploader .avatar) {
  width: 120px;
  height: 120px;
  display: block;
}
:deep(.avatar-uploader .el-upload) {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}
:deep(.avatar-uploader .el-upload:hover) {
  border-color: var(--el-color-primary);
}
:deep(.el-icon.avatar-uploader-icon) {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 120px;
  text-align: center;
}
</style>
