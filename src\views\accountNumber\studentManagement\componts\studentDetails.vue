<script setup>
import { ref, reactive, onMounted, onActivated } from "vue";
import {
  studentFindById,
  studentSituationFindAllByStudentId
} from "@/api/studentManage.js";
import { requestTo } from "@/utils/http/tool";
import { Edit, Hide, View } from "@element-plus/icons-vue";
import { decrypt, encryption } from "@/utils/SM4.js";
import { useRouter, useRoute } from "vue-router";
import dayjs from "dayjs";
defineOptions({
  name: "StudentManagementCompontsStudentDetails"
});
const router = useRouter();
const route = useRoute();

const columns = [
  {
    label: "上课时间", // 如果需要表格多选，此处label必须设置
    prop: "openTime",
    width: 180,
    formatter: ({ openTime }) => {
      return openTime ? dayjs(openTime).format("YYYY-MM-DD HH:mm:ss") : "--";
    }
  },
  {
    label: "课程名",
    prop: "coursePeriodName",
    minWidth: 90,
    formatter: ({ coursePeriodName }) => {
      return coursePeriodName || "--";
    }
  },
  {
    label: "期号",
    prop: "coursePeriodId",
    width: 80,
    formatter: ({ coursePeriodId }) => {
      return coursePeriodId || "--";
    }
  },
  {
    label: "机构",
    prop: "organizationName",
    minWidth: 90,
    formatter: ({ organizationName }) => {
      return organizationName || "--";
    }
  },
  {
    label: "签到时间",
    width: 180,
    prop: "signInTime",
    formatter: ({ signInTime }) => {
      return signInTime
        ? dayjs(signInTime).format("YYYY-MM-DD HH:mm:ss")
        : "--";
    }
  },
  {
    label: "获取学分",
    prop: "credit",
    width: 80,
    formatter: ({ credit }) => {
      return credit || "--";
    }
  },
  {
    label: "操作",
    fixed: "right",
    width: 280,
    slot: "operation"
  }
];

const tableHeader = ref([
  {
    label: "学生ID",
    type: "text",
    prop: "id",
    placeholder: "",
    width: "200px"
  },
  {
    label: "学生姓名",
    type: "text",
    prop: "name",
    placeholder: "",
    width: "200px"
  },
  {
    label: "创建时间",
    type: "createdAt",
    prop: "createdAt",
    placeholder: "",
    width: "200px"
  },
  {
    label: "学校",
    type: "text",
    prop: "school",
    placeholder: "",
    width: "200px"
  },
  {
    label: "证件类型",
    type: "text",
    prop: "idType",
    placeholder: "",
    width: "200px"
  },
  {
    label: "证件号",
    type: "idNumber",
    isEye: true,
    prop: "idNumber",
    prop1: "idNumberCt",
    placeholder: "",
    width: "200px"
  },
  {
    label: "家长",
    type: "parentDTOS",
    prop: "parentDTOS",
    placeholder: "",
    width: "200px"
  },
  {
    label: "参与课程",
    type: "text",
    prop: "courseNumber",
    placeholder: "",
    width: "200px"
  },
  {
    label: "获得学分",
    type: "text",
    prop: "credit",
    placeholder: "",
    width: "200px"
  }
]);

const loadingTable = ref(false);
const dataList = ref([]);
const from = reactive({
  coursePeriodName: "",
  organizationName: "",
  time: []
});
const pagination = {
  total: 0,
  pageSize: 10,
  currentPage: 1,
  background: true
};
const studentId = ref();
const iconidNum = ref(false);
const courseNumber = ref();

// 学生详情
const indexList = ref({});
const startAdd = async val => {
  const paramsArg = { id: val.id };
  const [err, res] = await requestTo(studentFindById(paramsArg));
  if (res) {
    indexList.value = res;
  }
  if (err) {
  }
};

// 去空
const removeEmptyValues = obj => {
  return Object.fromEntries(
    Object.entries(obj).filter(
      ([key, value]) =>
        value !== "" && !(Array.isArray(value) && value.length === 0)
    )
  );
};
// 课程列表
const startAddOne = async (val, text) => {
  const paramsArg = {
    studentId: val.studentId,
    coursePeriodName: val.coursePeriodName || "",
    organizationName: val.organizationName || "",
    startTime: val.startTime || "",
    endTime: val.endTime || "",
    page: val.page || 0,
    size: val.size,
    sort: "createdAt,desc"
  };
  let aee = removeEmptyValues(paramsArg);
  console.log("🦄-----aee-----", aee);
  const [err, res] = await requestTo(studentSituationFindAllByStudentId(aee));
  if (res) {
    console.log("🎉-----res---课程列表--", res);
    if (text) {
      courseNumber.value = res.totalElements;
    }
    dataList.value = res.content;
    pagination.total = res.totalElements;
  }
  if (err) {
  }
};
// 搜索按钮
const onSearch = () => {
  let time1 = from.time !== null ? new Date(from.time[0]).getTime() : "";
  let time2 = from.time !== null ? new Date(from.time[1]).getTime() : "";
  const text = {
    studentId: studentId.value,
    coursePeriodName: from.coursePeriodName || "",
    organizationName: from.organizationName || "",
    startTime: time1 || "",
    endTime: time2 || "",
    page: pagination.currentPage - 1 || "",
    size: pagination.pageSize || ""
  };
  startAddOne(text);
};
// 重置
const setData = () => {
  from.coursePeriodName = "";
  from.organizationName = "";
  from.time = [];
};
// 每页多少条
async function handleSizeChange(val) {
  // console.log("🎉-----val--每页多少条---", val);
  pagination.pageSize = val;
  let time1 = from.time !== null ? new Date(from.time[0]).getTime() : "";
  let time2 = from.time !== null ? new Date(from.time[1]).getTime() : "";
  const paramsArg = {
    studentId: studentId.value,
    coursePeriodName: from.coursePeriodName || "",
    organizationName: from.organizationName || "",
    startTime: time1 || "",
    endTime: time2 || "",
    page: 0,
    size: val
  };
  startAddOne(paramsArg);
}
// 前往页数
async function handleCurrentChange(val) {
  // console.log("🎉-----val--前往页数---", val);
  pagination.currentPage = val;
  let time1 = from.time !== null ? new Date(from.time[0]).getTime() : "";
  let time2 = from.time !== null ? new Date(from.time[1]).getTime() : "";
  const paramsArg = {
    studentId: studentId.value,
    coursePeriodName: from.coursePeriodName || "",
    organizationName: from.organizationName || "",
    startTime: time1 || "",
    endTime: time2 || "",
    page: val - 1,
    size: pagination.pageSize
  };
  startAddOne(paramsArg);
}
// 相关订单
const openDialog = (text, val) => {
  console.log("🎁-----text, val--学生---", text, val);
  if (text === "课程详情") {
    router.push({
      path: "/studentManagement/management/current/details",
      query: {
        periodId: val.coursePeriodId,
        text: "person",
        studentSituationId: val.id
      }
    });
  } else if (text === "实践感悟情况") {
    router.push({
      path: "/studentManagement/management/current/details",
      query: {
        periodId: val.coursePeriodId,
        text: "work",
        studentId: studentId.value,
        contentShow: "homework",
        showTabContent: false,
        homeworkId: val?.homeworkId
      }
    });
  } else if (text === "家长评价") {
    router.push({
      path: "/studentManagement/management/current/details",
      query: {
        periodId: val.coursePeriodId,
        text: "work",
        studentId: studentId.value,
        contentShow: "evaluate",
        showTabContent: false,
        coursePeriodId: val?.coursePeriodId
      }
    });
  } else if (text === "关联订单") {
    // router.push({
    //   path: "/studentManagement/management/current/details",
    //   query: {
    //     periodId: val.coursePeriodId,
    //     text: "work",
    //     studentId: studentId.value,
    //     contentShow: "order",
    //     showTabContent: false
    //   }
    // });

    router.replace({
      path: "/studentManagement/orderManagement/orderDetail",
      query: {
        id: val?.ordersId || "",
        type: "order",
        studentId: val?.studentId,
        coursePeriodId: val?.coursePeriodId
      }
    });
  }
};
// 证件号
function idNumAdd(val) {
  iconidNum.value = !iconidNum.value;
}
function idNumberAdd(val) {
  if (val) {
    return decrypt(val);
  } else {
    return "--";
  }
}

onActivated(() => {
  let time1 = from.time !== null ? new Date(from.time[0]).getTime() : "";
  let time2 = from.time !== null ? new Date(from.time[1]).getTime() : "";
  const paramsArg = {
    studentId: studentId.value,
    coursePeriodName: from.coursePeriodName || "",
    organizationName: from.organizationName || "",
    startTime: time1 || "",
    endTime: time2 || "",
    page: pagination.currentPage - 1 || "",
    size: pagination.pageSize || ""
  };
  startAddOne(paramsArg);
});

onMounted(() => {
  let teId = route.query;
  studentId.value = teId.id;
  startAdd(teId);
  let text = {
    studentId: teId.id,
    page: 0,
    size: 10
  };
  startAddOne(text, "开始");
});
const defaultTime = [
  new Date(2024, 4, 1, 0, 0, 0),
  new Date(2024, 4, 1, 23, 59, 59)
];
</script>

<template>
  <div>
    <div class="common bottom">
      <!-- <div class="title">学生管理 \ 详情</div> -->
      <div class="puretable">
        <el-descriptions
          class="margin-top"
          :label-width="'15%'"
          :column="3"
          border
        >
          <el-descriptions-item
            v-for="(item, index) in tableHeader"
            :key="index"
            label-align="center"
            label-class-name="my-label"
          >
            <template #label>
              <div class="cell-item">{{ item.label }}</div>
            </template>
            <el-form-item
              :prop="item.prop"
              :inline-message="item.check"
              style="margin-bottom: 0"
              :show-message="true"
              error-placement="right"
            >
              <span v-if="item.type === 'text'">
                <span v-if="item.prop === 'courseNumber'">{{
                  courseNumber || "--"
                }}</span>
                <span v-else-if="item.prop === 'credit'">5</span>
                <span v-else>
                  {{ indexList[item.prop] || "--" }}
                </span>
              </span>
              <span v-else-if="item.type === 'createdAt'">
                {{
                  indexList[item.prop]
                    ? dayjs(indexList[item.prop]).format("YYYY-MM-DD HH:mm:ss")
                    : "--"
                }}
              </span>
              <span v-else-if="item.type === 'parentDTOS'">
                <span v-if="Boolean(indexList?.parentDTOS?.length)">
                  <span v-for="it in indexList?.parentDTOS" :key="it">{{
                    it.name || "--"
                  }}</span>
                </span>
                <div v-else>{{ "--" }}</div>
              </span>
              <span v-else-if="item.type === 'idNumber'">
                <div v-if="iconidNum === true">
                  {{ idNumberAdd(indexList[item.prop1]) }}
                </div>
                <div v-else>{{ indexList[item.prop] || "--" }}</div>
              </span>
              <span v-if="indexList[item.prop1] && item.isEye" class="icon">
                <el-icon
                  v-if="iconidNum === true"
                  style="cursor: pointer"
                  @click="idNumAdd()"
                  ><View /></el-icon>
                <el-icon v-else style="cursor: pointer" @click="idNumAdd()"><Hide /></el-icon>
              </span>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
    <div class="common padd_bottom">
      <div class="search">
        <el-form :inline="true" :model="from" class="demo-form-inline">
          <el-form-item label="上课时间">
            <el-date-picker
              v-model="from.time"
              type="daterange"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              :default-time="defaultTime"
            />
          </el-form-item>
          <el-form-item label="课程名">
            <el-input
              v-model="from.coursePeriodName"
              placeholder="请输入课程名"
              clearable
            />
          </el-form-item>
          <el-form-item label="机构">
            <el-input
              v-model="from.organizationName"
              placeholder="请输入机构"
              clearable
            />
          </el-form-item>
          <el-form-item label="">
            <div class="button">
              <el-button type="primary" @click="onSearch(from)">搜索</el-button>
              <el-button @click="setData(from)">重置</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="common">
      <div class="puretable">
        <pure-table
          ref="tableRef"
          row-key="id"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="left"
          table-layout="auto"
          :loading="loadingTable"
          :data="dataList"
          :columns="columns"
          :pagination="{ ...pagination }"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #operation="{ row }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              @click="openDialog('实践感悟情况', row)"
            >
              实践感悟情况
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="primary"
              @click="openDialog('家长评价', row)"
            >
              评价
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="primary"
              @click="openDialog('关联订单', row)"
            >
              相关订单
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="primary"
              @click="openDialog('课程详情', row)"
            >
              课程详情
            </el-button>
          </template>
        </pure-table>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.common {
  width: 100%;
  height: 100%;
  padding: 20px 20px 2px;
  background-color: #fff;
  .title {
    margin-bottom: 24px;
    font-size: 20px;
    font-weight: bold;
    color: #606266;
  }
  .search {
    display: flex;
    justify-content: space-between;
    .button {
      display: flex;
      justify-content: right;
    }
  }
}
.iconteyp1 {
  display: flex;
  justify-content: space-between;
  width: 200px;
}
.bottom {
  margin-bottom: 20px;
  padding-bottom: 20px;
}
.padd_bottom {
  margin-bottom: 20px;
  padding-bottom: 2px;
}

.icon {
  display: flex;
  align-items: center;
  margin-left: 10px;
}
:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  background: #e1f5ff;
}
:deep(.el-descriptions__cell) {
  width: 18.333%;
}
</style>
