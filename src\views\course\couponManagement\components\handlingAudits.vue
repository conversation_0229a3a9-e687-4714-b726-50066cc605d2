<script setup>
import { ref, watch } from "vue";
import { ElMessage } from "element-plus";

// 定义props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
});

// 定义emits
const emit = defineEmits(["update:modelValue", "submit"]);

// 表单数据
const appealForm = ref({
  processingOpinion: "" // 处理意见
});

// 表单验证规则
const appealRules = ref({
  processingOpinion: [
    { required: true, message: "请选择或输入处理意见", trigger: "blur" }
  ]
});

const appealFormRef = ref(null);

// 预设处理意见选项 - 包含标题和详细描述
const presetOpinions = [
  {
    title: "您的申诉已收到",
    content:
      "您好,您的退款申诉我们已收到,目前正在核实相关情况。我们会尽快联系商家并进行评估,请您耐心等待。"
  },
  {
    title: "正在核实中",
    content:
      "您好,关于您提出的退款请求,平台已介入处理。当前正在对订单详情及沟通记录进行核实,后续将依据平台规则进行判断。"
  },
  {
    title: "需进一步沟通",
    content:
      "您好,我们已关注到您的申诉。为更好处理此事,可能需要您补充相关信息或凭证,以便平台公正评估。"
  },
  {
    title: "平台已介入协调",
    content:
      "您好,平台已就您的退款问题与商家进行沟通协调。处理进展将通过消息通知您,请留意后续信息。"
  },
  {
    title: "处理需一定周期",
    content:
      "您好,您的申诉已进入处理流程。此类问题需综合订单信息、服务约定及平台规则审慎判断,处理需要一定时间,请您理解。"
  },
  {
    title: "尊重双方权益",
    content:
      "您好,平台重视每一位用户的反馈。针对您的退款申诉,我们将基于事实和规则,公平、公正地协调处理。"
  }
];

// 监听弹窗显示状态，重置表单
watch(
  () => props.modelValue,
  newVal => {
    if (newVal && appealFormRef.value) {
      appealFormRef.value.resetFields();
    }
  }
);

// 关闭弹窗
const closeDialog = () => {
  emit("update:modelValue", false);
  // 主动重置表单内容，防止下次打开内容残留
  if (appealFormRef.value) {
    appealFormRef.value.resetFields();
  }
};

// 选择预设意见 - 点击标题时填入详细描述
const selectPresetOpinion = opinion => {
  appealForm.value.processingOpinion = opinion.content;
};

// 提交申诉处理
const submitAppealProcessing = async () => {
  if (!appealFormRef.value) return;

  try {
    await appealFormRef.value.validate();
    // 触发父组件的submit事件
    emit("submit", appealForm.value.processingOpinion);
    // 关闭弹窗并重置表单
    emit("update:modelValue", false);
    if (appealFormRef.value) {
      appealFormRef.value.resetFields();
    }
  } catch (error) {
    console.error("申诉处理失败:", error);
    ElMessage.error("申诉处理失败，请重试");
  }
};
</script>

<template>
  <el-dialog
    :model-value="modelValue"
    title="确认处理"
    width="600px"
    :close-on-click-modal="false"
    @update:model-value="closeDialog"
    @close="closeDialog"
  >
    <el-form
      ref="appealFormRef"
      :model="appealForm"
      :rules="appealRules"
      label-width="0"
    >
      <el-form-item prop="processingOpinion">
        <el-input
          v-model="appealForm.processingOpinion"
          type="textarea"
          :rows="4"
          placeholder="请选择或输入处理意见"
          style="width: 100%"
        />
      </el-form-item>

      <!-- 预设处理意见 -->
      <div class="preset-opinions">
        <div class="preset-title">预设意见：</div>
        <div class="preset-buttons">
          <el-button
            v-for="opinion in presetOpinions"
            :key="opinion.title"
            size="small"
            class="preset-opinion-btn"
            @click="selectPresetOpinion(opinion)"
          >
            {{ opinion.title }}
          </el-button>
        </div>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button class="cancel-btn" @click="closeDialog"> 取消 </el-button>
        <el-button
          type="primary"
          class="confirm-btn"
          @click="submitAppealProcessing"
        >
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
/* 申诉处理弹窗样式 */
.preset-opinions {
  margin-top: 16px;

  .preset-title {
    font-size: 14px;
    color: #606266;
    margin-bottom: 8px;
  }

  .preset-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;

    .preset-opinion-btn {
      margin: 0;
      font-size: 12px;
      padding: 6px 12px;
      border: 1px solid #dcdfe6;
      background-color: #f5f7fa;
      color: #606266;
      transition: all 0.3s;

      &:hover {
        border-color: #409eff;
        color: #409eff;
        background-color: #ecf5ff;
      }

      &:active {
        background-color: #d9ecff;
      }
    }
  }
}

.dialog-footer {
  text-align: right;

  .cancel-btn {
    margin-right: 12px;
    border-color: #dcdfe6;
    color: #606266;

    &:hover {
      border-color: #c0c4cc;
      color: #409eff;
      background-color: #ecf5ff;
    }

    &:focus {
      border-color: #409eff;
      color: #409eff;
    }
  }

  .confirm-btn {
    background-color: #409eff;
    border-color: #409eff;

    &:hover {
      background-color: #66b1ff;
      border-color: #66b1ff;
    }

    &:focus {
      background-color: #3a8ee6;
      border-color: #3a8ee6;
    }
  }
}
</style>
