<script setup>
import darkIcon from "@/assets/svg/dark.svg?component";
import dayIcon from "@/assets/svg/day.svg?component";
import globalization from "@/assets/svg/globalization.svg?component";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { ReImageVerify } from "@/components/ReImageVerify";
// import { loginRules } from "./utils/rule";
import TypeIt from "@/components/ReTypeit";
import { useDataThemeChange } from "@/layout/hooks/useDataThemeChange";
import { useLayout } from "@/layout/hooks/useLayout";
import { useNav } from "@/layout/hooks/useNav";
import { useTranslationLang } from "@/layout/hooks/useTranslationLang";
import { $t, transformI18n } from "@/plugins/i18n";
import { getTopMenu, initRouter } from "@/router/utils";
import { useUserStoreHook } from "@/store/modules/user";
import { message } from "@/utils/message";
import Check from "@iconify-icons/ep/check";
import Info from "@iconify-icons/ri/information-line";
import Lock from "@iconify-icons/ri/lock-fill";
import User from "@iconify-icons/ri/user-3-fill";
import { debounce, useGlobal } from "@pureadmin/utils";
import { useEventListener } from "@vueuse/core";
import { computed, reactive, ref, toRaw, watch } from "vue";
import { useI18n } from "vue-i18n";
import { useRouter, useRoute } from "vue-router";

import LoginPhone from "./components/LoginPhone.vue";
import LoginQrCode from "./components/LoginQrCode.vue";
import LoginRegist from "./components/LoginRegist.vue";
import LoginUpdate from "./components/LoginUpdate.vue";
import { operates, thirdParty } from "./utils/enums";
import Motion from "./utils/motion";
import { avatar, bg, illustration } from "./utils/static";
import { encryption } from "@/utils/SM4";
// import adminImg from "@/assets/login/admin.png";
import adminImg from "@/assets/login/localend.png";
import adminNoImg from "@/assets/login/localendNo.png";
// import platformImg from "@/assets/login/schooladmin.png";
import platformImg from "@/assets/login/platform.png";
import platformNoImg from "@/assets/login/platformNo.png";
import { ElMessage } from "element-plus";
import Verify from "@/components/Verifition/Verify.vue";
defineOptions({
  name: "Login"
});
const roleList = ref([
  {
    id: 1,
    label: "局端管理员",
    value: "ManagerMan",
    imgurl: adminImg,
    imgNo: adminNoImg
  },
  {
    id: 2,
    label: "平台管理员",
    value: "SubjectFacilitator",
    imgurl: platformImg,
    imgNo: platformNoImg
  }
  // {
  //   id: 3,
  //   label: "活动审核员",
  //   value: "AuditMan",
  //   imgurl: require("@/assets/role/reviewer.png"),
  // },
  // {
  //   id: 4,
  //   label: "活动评委",
  //   value: "GradingExpert",
  //   imgurl: require("@/assets/role/score.png"),
  // },
]);
const { $config } = useGlobal();
/** br项目定制化 */
const brlayout = computed(() => {
  return $config?.brLayout;
});
const imgCode = ref("");
const loginDay = ref(7);
const router = useRouter();
const route = useRoute();
const loading = ref(false);
const checked = ref(false);
const disabled = ref(false);
const ruleFormRef = ref();
const currentPage = computed(() => {
  return useUserStoreHook().currentPage;
});

const { t } = useI18n();
const { initStorage } = useLayout();
initStorage();
const { dataTheme, overallStyle, dataThemeChange } = useDataThemeChange();
dataThemeChange(overallStyle.value);
const { title, getDropdownItemStyle, getDropdownItemClass } = useNav();
const { locale, translationCh, translationEn } = useTranslationLang();

const ruleForm = reactive({
  username: "",
  password: "",
  verifyCode: ""
});

const platformInfo = JSON.parse(localStorage.getItem("platformInfo") || "{}");

const loginShow = ref(false);
//点击选择角色
const selectedid = ref(null);
const selected = ref("");
const addrole = data => {
  // console.log("🌳data------------------------------>", data);

  selectedid.value = data.id;
  selected.value = data.value;
  useUserStoreHook().SET_ROLETARGET(data.label);
};
const determine = () => {
  if (selectedid.value) {
    loginShow.value = true;
  } else {
    ElMessage({
      type: "error",
      message: "请选择角色"
    });
  }
};

const onLogin = async formEl => {
  loading.value = true;
  useUserStoreHook()
    .loginByUsername({
      account: ruleForm.username,
      // password: ruleForm.password
      password: encryption(ruleForm.password)
    })
    .then(res => {
      return initRouter().then(() => {
        disabled.value = true;
        router
          .push(getTopMenu(true).path)
          .then(() => {
            message(t("login.pureLoginSuccess"), { type: "success" });
          })
          .finally(() => (disabled.value = false));
      });
    })
    .catch(err => {
      if (err[0] === 10015) {
        message(t("login.pureLoglock"), { type: "error" });
      } else {
        message(t("login.pureLoginagain"), { type: "error" });
      }
    })
    .finally(() => (loading.value = false));
  loading.value = false;
};

const immediateDebounce = debounce(formRef => onLogin(formRef), 1000, true);

useEventListener(document, "keypress", ({ code }) => {
  if (
    ["Enter", "NumpadEnter"].includes(code) &&
    !disabled.value &&
    !loading.value
  ) {
    handleLoginClick(ruleFormRef.value);
  }
});

watch(imgCode, value => {
  useUserStoreHook().SET_VERIFYCODE(value);
});
watch(checked, bool => {
  useUserStoreHook().SET_ISREMEMBERED(bool);
});
watch(loginDay, value => {
  useUserStoreHook().SET_LOGINDAY(value);
});
const handleLoginClick = async formEl => {
  if (!formEl) return;
  await formEl.validate(valid => {
    if (valid) {
      if (verifyRef.value) {
        verifyRef.value.show();
      } else {
        onLogin();
      }
    }
  });
};
const verifyRef = ref(null);
const success = () => {
  console.log("图形验证通过");
  onLogin();
};

// 监听路由参数变化，处理微信登录回调
watch(
  () => route.query,
  newQuery => {
    let { code, state } = newQuery;

    // 如果URL中包含code和state参数，说明是微信登录回调，直接切换到二维码登录页面
    if (code && state) {
      console.log("检测到微信登录回调参数，切换到二维码登录页面:", {
        code,
        state
      });
      // 需要先显示登录表单，然后切换到二维码页面
      loginShow.value = true;
      // 设置默认角色为平台管理员，确保后续账号密码登录调用平台登录接口
      selectedid.value = 2; // 平台管理员ID
      selected.value = "SubjectFacilitator"; // 平台管理员value
      useUserStoreHook().SET_ROLETARGET("平台管理员");
      useUserStoreHook().SET_CURRENTPAGE(2);
    }
  },
  { immediate: true }
);
</script>

<template>
  <div class="select-none">
    <Verify
      ref="verifyRef"
      mode="pop"
      captchaType="blockPuzzle"
      :imgSize="{ width: '330px', height: '155px' }"
      @success="success"
    />
    <img
      :src="
        platformInfo.platformLogoLight ||
        'data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=='
      "
      class="wave"
    >
    <div class="flex-c absolute right-5 top-3">
      <!-- 主题 -->
      <el-switch
        v-if="brlayout !== 'br'"
        v-model="dataTheme"
        inline-prompt
        :active-icon="dayIcon"
        :inactive-icon="darkIcon"
        @change="dataThemeChange"
      />
      <!-- 国际化 -->
      <el-dropdown v-if="brlayout !== 'br'" trigger="click">
        <globalization
          class="hover:text-primary hover:!bg-[transparent] w-[20px] h-[20px] ml-1.5 cursor-pointer outline-none duration-300"
        />
        <template #dropdown>
          <el-dropdown-menu class="translation">
            <el-dropdown-item
              :style="getDropdownItemStyle(locale, 'zh')"
              :class="['dark:!text-white', getDropdownItemClass(locale, 'zh')]"
              @click="translationCh"
            >
              <IconifyIconOffline
                v-show="locale === 'zh'"
                class="check-zh"
                :icon="Check"
              />
              简体中文
            </el-dropdown-item>
            <el-dropdown-item
              :style="getDropdownItemStyle(locale, 'en')"
              :class="['dark:!text-white', getDropdownItemClass(locale, 'en')]"
              @click="translationEn"
            >
              <span v-show="locale === 'en'" class="check-en">
                <IconifyIconOffline :icon="Check" />
              </span>
              English
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <div class="login-container">
      <div class="img">
        <!-- <component :is="toRaw(illustration)" /> -->
        <img src="@/assets/login/banner.png" alt="">
      </div>
      <div class="login-box">
        <div v-if="loginShow" class="login-form">
          <!-- <avatar class="avatar" /> -->
          <Motion>
            <h2 class="outline-none">
              <TypeIt
                :options="{ strings: [title], cursor: false, speed: 100 }"
              />
            </h2>
          </Motion>

          <el-form
            v-if="currentPage === 0"
            ref="ruleFormRef"
            :model="ruleForm"
            size="large"
          >
            <Motion :delay="100">
              <el-form-item
                :rules="[
                  {
                    required: true,
                    message: transformI18n($t('login.pureUsernameReg')),
                    trigger: 'blur'
                  }
                ]"
                prop="username"
              >
                <el-input
                  v-model.trim="ruleForm.username"
                  clearable
                  :placeholder="t('login.pureUsername')"
                  :prefix-icon="useRenderIcon(User)"
                />
              </el-form-item>
            </Motion>

            <Motion :delay="150">
              <el-form-item
                prop="password"
                :rules="[
                  {
                    required: true,
                    message: transformI18n($t('login.purePassWordReg')),
                    trigger: 'blur'
                  }
                ]"
              >
                <el-input
                  v-model.trim="ruleForm.password"
                  clearable
                  show-password
                  :placeholder="t('login.purePassword')"
                  :prefix-icon="useRenderIcon(Lock)"
                />
              </el-form-item>
            </Motion>

            <Motion :delay="250">
              <el-form-item>
                <el-button
                  class="w-full mt-4"
                  size="default"
                  type="primary"
                  :loading="loading"
                  :disabled="disabled"
                  @click="handleLoginClick(ruleFormRef)"
                >
                  {{ t("login.pureLogin") }}
                </el-button>
              </el-form-item>
            </Motion>

            <Motion :delay="300">
              <el-form-item>
                <div class="w-full h-[20px] flex justify-between items-center">
                  <el-button
                    v-for="(item, index) in operates"
                    :key="index"
                    class="w-full mt-4"
                    size="default"
                    @click="useUserStoreHook().SET_CURRENTPAGE(index + 1)"
                  >
                    {{ t(item.title) }}
                  </el-button>
                </div>
              </el-form-item>
            </Motion>
          </el-form>

          <LoginPhone v-if="currentPage === 1" />
          <LoginQrCode v-if="currentPage === 2" />
          <LoginRegist v-if="currentPage === 3" />
          <LoginUpdate v-if="currentPage === 4" />
          <el-button
            :class="currentPage === 1 ? 'w-full' : 'w-full mt-4'"
            size="default"
            @click="loginShow = !loginShow"
          >
            返回
          </el-button>
        </div>
        <div v-else class="register">
          <div class="register-title">
            {{ expert ? "欢迎您，请登录" : "欢迎您，请选择角色" }}
          </div>
          <div class="register-content">
            <div class="role">
              <div class="content">
                <div
                  v-for="(item, index) in roleList"
                  :key="index"
                  class="item"
                  :class="selectedid === item.id ? 'Selected' : ''"
                  @click="addrole(item)"
                >
                  <img
                    :src="selectedid === item.id ? item.imgurl : item.imgNo"
                    alt=""
                  >
                  <h3>{{ item.label }}</h3>
                </div>
              </div>
              <el-button
                type="primary"
                style="
                  width: 465px;
                  height: 56px;
                  background-color: #409eff;
                  border-radius: 10px;
                  font-size: 20px;
                "
                @click="determine"
              >
                确定
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <div
      class="w-full flex-c absolute bottom-3 text-sm text-[rgba(0,0,0,0.6)] dark:text-[rgba(220,220,242,0.8)]"
    >
      Copyright © 2020-present
      <a
        class="hover:text-primary"
        href="https://github.com/pure-admin"
        target="_blank"
      >
        &nbsp;{{ title }}
      </a>
    </div> -->
  </div>
</template>

<style scoped>
@import url("@/style/login.css");
</style>

<style lang="scss" scoped>
:deep(.el-input-group__append, .el-input-group__prepend) {
  padding: 0;
}

.translation {
  ::v-deep(.el-dropdown-menu__item) {
    padding: 5px 40px;
  }

  .check-zh {
    position: absolute;
    left: 20px;
  }

  .check-en {
    position: absolute;
    left: 20px;
  }
}
.register {
  position: absolute;
  right: 15.5%;
  top: 24%;

  .register-title {
    width: 320px;
    font-family: Source Han Sans CN;
    font-size: 30px;
    line-height: 58px;
    color: #999999;
    margin-bottom: 40px;
    text-align: left;
    font-weight: 500;
  }

  .register-content {
    width: 464px;
    height: 348px;
    background-color: #ffffff;
    // box-shadow: 0px 3px 20px 0px rgba(2, 19, 168, 0.18);
    border-radius: 14px;
    position: relative;
    //选择角色
    .role {
      // padding-top: 50px;
      display: flex;
      flex-direction: column;
      // align-items: center;
      .content {
        display: flex;
        // flex-wrap: wrap;
        // padding: 0 80px;
        // justify-content: space-evenly;
        box-sizing: border-box;
        // gap: 16px;
        margin-bottom: 22px;
        // width: 700px;
        .item {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: space-evenly;
          width: 201px;
          height: 201px;

          margin-bottom: 20px;
          background-color: #ffffff;
          border-radius: 10px;
          border: solid 3px #efefef;
          cursor: pointer;
          margin-right: 63px;
          img {
            width: 70px;
            height: 70px;
          }
          h3 {
            font-family: Source Han Sans CN;
            font-size: 20px;
            color: #333333;
          }
        }
        .item:last-child {
          margin-right: 0;
        }
        .Selected {
          background-color: #dfeeff;
          border-radius: 10px;
          border: solid 3px #a6d2ff;
        }
      }
    }
  }
}
</style>
